const GRAPHQL_ENDPOINT = "http://localhost:5192/graphql"; // change to your endpoint
const CREATE_SKILL_MUTATION = `
mutation CreateSkill($name: String!){
  createSkill(name: $name) {
    id
  }
}
`
const CREATE_TRADE_MUTATION = `
mutation CreateTrade($name: String!){
  createTrade(name: $name) {
    id
  }
}
`

const CREATE_TRAINING_MUTATION = `
mutation CreateTraining($name: String!) {
  createTraining(name: $name) {
    id
  }
}
`

const skills = [
    "scaffolding",
    "welding",
    "rigging",
    "painting",
    "plumbing",
    "masonry",
    "carpentry",
    "steel fixing",
];

const trades = [
    "Mechanical",
    "Electrical",
    "Civil",
    "Plumbing",
];

const trainings = [
    "first aid",
    "fire safety",
    "working at heights",
    "electrical safety",
    "confined space entry",
    "PPE usage",
    "excavation safety",
];

async function graphqlRequest(query, variables) {
    const res = await fetch(GRAPHQL_ENDPOINT, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query, variables }),
    });

    const json = await res.json();
    if (json.errors) {
        console.error("GraphQL errors:", JSON.stringify(json.errors, null, 2));
        // throw new Error("GraphQL request failed");
    }
    return json.data;
}

async function createSkills() {
    for (const skill of skills) {
        const variables = { name: skill };
        const data = await graphqlRequest(CREATE_SKILL_MUTATION, variables);
        console.log(`Created skill ${data.createSkill.id} - ${skill}`);
    }
}

async function createTrades() {
    for (const trade of trades) {
        const variables = { name: trade };
        const data = await graphqlRequest(CREATE_TRADE_MUTATION, variables);
        console.log(`Created trade ${data.createTrade.id} - ${trade}`);
    }
}

async function createTrainings() {
    for (const training of trainings) {
        const variables = { name: training };
        const data = await graphqlRequest(CREATE_TRAINING_MUTATION, variables);
        console.log(`Created training ${data.createTraining.id} - ${training}`);
    }
}

async function main() {
    await createSkills();
    await createTrades();
    await createTrainings();
}

main();
