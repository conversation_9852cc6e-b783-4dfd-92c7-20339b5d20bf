const axios = require("axios");
const FormData = require("form-data");
const fs = require("fs");
const path = require("path");

// Configuration
const GRAPHQL_ENDPOINT = "http://localhost:5192/graphql"; // change to your endpoint
const DOCS_DIR = "docs";
const WORKERS_COUNT = 2;

const firstNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];
const lastNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Martinez",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>jer<PERSON>",
  "Kipkoech",
  "Mwikali",
];
const domains = [
  "gmail.com",
  "yahoo.com",
  "outlook.com",
  "company.co.ke",
  "irontech.co.ke",
];

const companies = [
  "IronTech Works",
  "SteelFoundry Inc",
  "MegaBuilders",
  "TruWorks Ltd",
];
const genders = ["Male", "Female"];
const certificateNames = [
  "carpentry certificate",
  "plumbing certificate",
  "electrical certificate",
  "welding certificate",
  "medical certificate",
  "safety certificate",
  "mechanical certificate",
  "painting certificate",
  "masonry certificate",
];

const tradeIds = [1, 2, 3, 4];
const skillIds = [1, 2, 3, 4, 5, 6, 7, 8];
const trainingIds = [1, 2, 3, 4, 5, 6, 7];

const certificateFiles = [
  "docs/certificates/cert_jpg_1.jpg",
  "docs/certificates/cert_jpg_2.jpg",
  "docs/certificates/cert_jpg_3.jpg",
  "docs/certificates/cert_jpg_4.jpg",
  "docs/certificates/cert_jpg_5.jpg",
  "docs/certificates/cert_jpg_6.jpg",
  "docs/certificates/cert_jpg_7.jpg",
  "docs/certificates/cert_jpg_8.jpg",
  "docs/certificates/cert_jpg_9.jpg",
  "docs/certificates/cert_jpg_10.jpg",
  "docs/certificates/cert_jpg_11.jpg",
  "docs/certificates/cert_jpg_12.jpg",
  "docs/certificates/cert_jpg_13.jpg",
];

const idFiles = [
  "docs/id/id_jpg_1.jpg",
  "docs/id/id_jpg_2.jpg",
  "docs/id/id_jpg_3.jpg",
  "docs/id/id_jpg_4.jpg",
  "docs/id/id_jpg_5.jpg",
  "docs/id/id_jpg_6.jpg",
  "docs/id/id_jpg_7.jpg",
  "docs/id/id_jpg_8.jpg",
  "docs/id/id_jpg_9.jpg",
  "docs/id/id_jpg_10.jpg",
  "docs/id/id_jpg_11.jpg",
  "docs/id/id_jpg_12.jpg",
];

const profilePictureFiles = [
  "docs/profile_pictures/pfp_jpg_1.jpg",
  "docs/profile_pictures/pfp_jpg_2.jpg",
  "docs/profile_pictures/pfp_jpg_3.jpg",
  "docs/profile_pictures/pfp_jpg_4.jpg",
  "docs/profile_pictures/pfp_jpg_5.jpg",
  "docs/profile_pictures/pfp_jpg_6.jpg",
  "docs/profile_pictures/pfp_jpg_7.jpg",
  "docs/profile_pictures/pfp_jpg_8.jpg",
  "docs/profile_pictures/pfp_jpg_9.jpg",
  "docs/profile_pictures/pfp_jpg_10.jpg",
  "docs/profile_pictures/pfp_jpg_11.jpg",
  "docs/profile_pictures/pfp_jpg_12.jpg",
];

const signatureFiles = [
  "docs/signatures/sign_jpg_1.jpg",
  "docs/signatures/sign_jpg_2.jpg",
  "docs/signatures/sign_jpg_3.jpg",
  "docs/signatures/sign_png_1.png",
  "docs/signatures/sign_png_2.png",
  "docs/signatures/sign_png_3.png",
  "docs/signatures/sign_png_4.png",
  "docs/signatures/sign_png_5.png",
  "docs/signatures/sign_png_6.png",
  "docs/signatures/sign_png_7.png",
  "docs/signatures/sign_png_8.png",
];

function getRandomUniqueSubarray(inputArray) {
  if (!Array.isArray(inputArray) || inputArray.length === 0) return [];

  // Create a shallow copy to avoid modifying the original array
  const shuffled = [...inputArray];

  // Fisher–Yates shuffle
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  const size = Math.floor(Math.random() * (shuffled.length + 1)); // size from 0 to inputArray.length
  return shuffled.slice(0, size);
}


const randomInt = (min, max) =>
  Math.floor(Math.random() * (max - min + 1)) + min;
const randomItem = (arr) => arr[randomInt(0, arr.length - 1)];

function generateNationalId() {
  return Math.floor(Math.random() * 99999999)
    .toString()
    .padStart(8, "0");
}

function generateEmail(firstName, lastName) {
  const domain = randomItem(domains);
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`;
}

const readRandomFile = (folder) => {
  const dirPath = path.join(DOCS_DIR, folder);
  const files = fs.readdirSync(dirPath);
  return path.join(dirPath, randomItem(files));
};
function getRandomElements(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateArray(length, max, min = 0) {
  return Array.from({ length }, () => randomInt(min, max));
}

const createWorkerPayload = (index) => {
  const firstName = randomItem(firstNames);
  const lastName = randomItem(lastNames);
  const name = `${firstName} ${lastName}`;
  const company = randomItem(companies);
  const gender = randomItem(genders);
  const nationalId = generateNationalId();
  const phoneNumber = `+2547${randomInt(10000000, 99999999)}`;
  const email = generateEmail(firstName, lastName);
  const mpesaNumber = `07${randomInt(10, 99)}${randomInt(1000000, 9999999)}`;

  const inductionDate = new Date().toISOString();
  const medicalCheckDate = new Date(
    Date.now() - 7 * 24 * 3600 * 1000
  ).toISOString();

  const fileMap = {};
  const form = new FormData();
  let fileIndex = 0;

  const attachFile = (key, filePath) => {
    const fileStream = fs.createReadStream(filePath);
    const formKey = `file${fileIndex++}`;
    form.append(formKey, fileStream, path.basename(filePath));
    fileMap[formKey] = [key];
    return filePath;
  };

  // const trainings = generateArray(
  //   Math.floor(Math.random() * 3) + 1,
  //   trainingIds.length
  // ).map((id, i) => {
  //   const certPath = readRandomFile("certificates");
  //   attachFile(`variables.input.trainings.${i}.documents.0.file`, certPath);
  //   return {
  //     trainingId: id,
  //     documents: [
  //       {
  //         file: null,
  //         name: `certificate ${i + 1}`,
  //         isPublic: true,
  //       },
  //     ],
  //   };
  // });

  const trainings = getRandomUniqueSubarray(trainingIds).map((id, i) => {
    const certPath = readRandomFile("certificates");
    attachFile(`variables.input.trainings.${i}.documents.0.file`, certPath);
    return {
      trainingId: id,
      documents: [
        {
          file: null,
          name: `certificate ${i + 1}`,
          isPublic: true,
        },
      ],
    };
  });


  const documents = [
    {
      file: null,
      name: "national id",
      isPublic: false,
    },
  ];

  attachFile(`variables.input.documents.0.file`, readRandomFile("id"));
  
  let add = Math.floor(Math.random() * 100)%3;
  if (add === 0) {
    documents.push({
      file: null,
      name: "lift certificate",
      isPublic: false,
    });
    // console.table(documents);
    attachFile(`variables.input.documents.1.file`, readRandomFile("certificates"));
  }

  const profilePicturePath = readRandomFile("profile_pictures");
  attachFile(`variables.input.profilePicture`, profilePicturePath);

  const signaturePath = readRandomFile("signatures");
  attachFile(`variables.input.signature`, signaturePath);

  const variables = {
    input: {
      name,
      company,
      gender,
      nationalId,
      phoneNumber,
      email,
      mpesaNumber,
      dateOfBirth: "1990-01-01",
      inductionDate,
      medicalCheckDate,
      profilePicture: null,
      signature: null,
      documents,
      trainings,
      tradeIds: getRandomElements(tradeIds, Math.floor(Math.random() * 2) + 1),
      skillIds: getRandomElements(skillIds, Math.floor(Math.random() * 3) + 1),
    },
  };

  form.append(
    "operations",
    JSON.stringify({
      query: `mutation CreateWorkerWithTraining($input: CreateWorkerWithTrainingInput!) {
      createWorkerWithTraining(input: $input) {
        id
        name
      }
    }`,
      variables,
    })
  );

  form.append("map", JSON.stringify(fileMap));

  return form;
};

const sendWorker = async (index) => {
  const form = createWorkerPayload(index);
  // console.log(Object.keys(form))
  try {
    const res = await axios.post(GRAPHQL_ENDPOINT, form, {
      headers: { ...form.getHeaders(), "GraphQL-Preflight": "true", "Authorization":"Bearer " },
      maxBodyLength: Infinity,
    });
    console.log(
      `✅ Worker ${index + 1} created:`,
      res.data.data?.createWorkerWithTraining?.name || JSON.stringify(res.data, null, 2)
    );
    // console.log(form)
  } catch (err) {
    console.error(
      `❌ Failed to create worker ${index + 1}:`,
      err.response?.data || err.message
    );
    console.log(err.message);
  }
};

const main = async () => {
  for (let i = 0; i < WORKERS_COUNT; i++) {
    await sendWorker(i);
  }
};

main();
