// job_script.js
// const fetch =  require("node-fetch");
// const { default: axios } = require("axios");
const fs = require("fs");
const path = require("path");
const FormData = require("form-data");
const axios = require("axios");

const GRAPHQL_ENDPOINT = "http://localhost:5192/graphql"; // change to your endpoint
const NumberOfCreatedJobs = 12;
const NumberOfReviewedJobs = 7; // ! must be less than or equal to NumberOfCreatedJobs

// ---- MUTATIONS ----
const CREATE_JOB_MUTATION = `
mutation CreateJob($input: CreateJobInput!) {
  createJob(input: $input) {
    id
    status
    description
    title
  }
}
`;

const REVIEW_JOB_MUTATION = `
mutation RJ($input: ReviewJobInput!) {
  reviewJob(input: $input) {
    id
    title
  }
}
`;

// ---- CERTIFICATE FILES ----
const certificateDir = path.join(process.cwd(), "docs", "certificates");
const allCerts = fs.readdirSync(certificateDir).map(f => `docs/certificates/${f}`);

// ---- RANDOM HELPERS ----

const titles = [
  "Install safety harnesses",
  "Repair main stair rail",
  "Fit guard rails",
  "Inspect elevator shaft",
  "Clean ventilation ducts",
  "Replace window panels",
  "Check sprinkler system",
  "Upgrade lighting",
  "Paint exterior wall",
  "Install security cameras",
  "Repair flooring",
  "Inspect roof supports",
];

const locations = [
  "East wing - third floor",
  "West wing - ground floor",
  "North block - level 2",
  "South block - rooftop",
  "Central atrium",
  "Basement storage area",
  "Maintenance workshop",
  "Loading dock",
  "Conference room 5",
];

const hazardDescriptions = [
  "Loose cables",
  "Slippery floor",
  "Falling debris",
  "Open trench",
  "Sharp edges",
  "Confined space",
];

const controlMeasuresList = [
  "Wear helmet",
  "Use safety harness",
  "Install warning signs",
  "Clear debris",
  "Provide lighting",
  "Use gloves",
];

const ppeList = [
  "Helmet",
  "Gloves",
  "Goggles",
  "Safety boots",
  "Harness",
  "Ear protection",
];

const permitsList = [
  "GENERAL_WORK_PERMIT",
  "HOT_WORK_PERMIT",
  "CONFINED_SPACE_ENTRY_PERMIT",
  "WORK_AT_HEIGHT_PERMIT",
  "EXCAVATION_PERMIT",
];

const precautions = [
  "Wear appropriate personal protective equipment (PPE) at all times, including hard hats, safety glasses, high-visibility vests, and steel-toed boots.",
  "Ensure fall protection systems are in place when working at heights, such as guardrails, safety nets, and personal fall arrest systems.",
  "Regularly inspect all tools and equipment before use, and remove any damaged items from service.",
  "Maintain a clean and organized worksite to prevent trips, slips, and falls.",
  "Follow proper lifting techniques to avoid back injuries, or use mechanical aids for heavy loads.",
  "Never work under the influence of drugs or alcohol.",
  "Be aware of your surroundings and the activities of others on the site.",
  "Properly store and label all hazardous materials and chemicals.",
  "Ensure all electrical equipment is properly grounded and cords are not frayed or damaged.",
  "Provide adequate lighting in all work areas, especially in enclosed or poorly lit spaces.",
  "Establish clear emergency procedures and ensure all workers know the location of first-aid kits and emergency exits.",
  "Use ladders and scaffolding correctly, following manufacturer's instructions and safety guidelines.",
  "Create and maintain a trenching and excavation plan to prevent cave-ins.",
  "Use lockout/tagout procedures to de-energize machinery and equipment before maintenance or repair.",
  "Control dust and airborne particles through ventilation, wetting, or using respiratory protection.",
  "Keep fire extinguishers readily accessible and ensure workers are trained on their use.",
  "Stay hydrated and take regular breaks, especially in hot weather.",
  "Report all injuries, no matter how minor, to a supervisor immediately.",
  "Do not operate machinery or equipment unless you are properly trained and authorized.",
  "Ensure proper communication on the job site to prevent accidents and coordinate tasks effectively."
];

const fireExtinguisherTypes = [
  "Water",
  "Foam",
  "Dry Powder",
  "Carbon Dioxide (CO2)",
  "Wet Chemical"
];

function randomItem(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function randomItems(arr, min = 1, max = arr.length) {
  const shuffled = [...arr].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, Math.floor(Math.random() * (max - min + 1)) + min);
}

function getRandomDocs() {
  // 30% chance to have no documents
  if (Math.random() < 0.3) return [];
  const shuffled = [...allCerts].sort(() => Math.random() - 0.5);
  const count = Math.floor(Math.random() * allCerts.length) + 1;
  return shuffled.slice(0, count).map(file => ({
    file,
    isPublic: true,
    name: path.basename(file, path.extname(file)),
  }));
}
function generatePayLoad(input) {
  const fileMap = {};
  const form = new FormData();
  let fileIndex = 0;

  const docs = getRandomDocs();
  // console.log(docs);
  const attachFile = (key, filePath) => {
    const fileStream = fs.createReadStream(filePath);
    const formKey = `file${fileIndex++}`;
    form.append(formKey, fileStream, path.basename(filePath));
    fileMap[formKey] = [key];
    return filePath;
  };
  const payloadDocs = docs.map((d, i) => {
    attachFile(`variables.input.documents.${i}.file`, d.file);
    return {
      file: null,
      isPublic: d.isPublic,
      name: d.name,
    }
  })
  const variables = {
    input: {
      ...input,
      documents: payloadDocs,
    },
  };
  // console.log(JSON.stringify(variables, null, 1));
  form.append("operations",
    JSON.stringify({
      query: REVIEW_JOB_MUTATION,
      variables
    }));
  form.append("map", JSON.stringify(fileMap));
  return form;
}

async function reviewJobGraphql(variables) {
  const form = generatePayLoad(variables);
  try {
    const res = await axios.post(GRAPHQL_ENDPOINT, form, {
      headers: { ...form.getHeaders(), "GraphQL-Preflight": "true" },
      maxBodyLength: Infinity,
    });
    console.log(res.data.data?.reviewJob);
    return res.data;
  }
  catch (e) {
    // console.error(e.message);
    console.error(e.response?.data);
  }
}

function getRandomHazards() {
  const hazardsCount = Math.floor(Math.random() * 3) + 1; // 1-3 hazards
  return Array.from({ length: hazardsCount }, () => ({
    description: randomItem(hazardDescriptions),
    controlMeasures: randomItems(controlMeasuresList, 1, 3),
  }));
}

// ---- GRAPHQL REQUEST ----
async function graphqlRequest(query, variables) {
  const res = await fetch(GRAPHQL_ENDPOINT, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ query, variables }),
  });

  const json = await res.json();
  if (json.errors) {
    console.error("GraphQL errors:", JSON.stringify(json.errors, null, 2));
    // throw new Error("GraphQL request failed");
  }
  return json.data;
}
function getRandomUniqueSubarray(inputArray) {
  if (!Array.isArray(inputArray) || inputArray.length === 0) return [];

  // Create a shallow copy to avoid modifying the original array
  const shuffled = [...inputArray];

  // Fisher–Yates shuffle
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  const size = Math.floor(Math.random() * (shuffled.length + 1)); // size from 0 to inputArray.length
  return shuffled.slice(0, size);
}
function generatePermitDetails() {
  const perms = randomItems(permitsList, 1, 2);
  let details = {
    requiredPermits: perms,
  }
  if (perms.includes("GENERAL_WORK_PERMIT")) {

  };
  if (perms.includes("HOT_WORK_PERMIT")) {
    details.natureOfHotWork = randomItems(["Welding", "Cutting", "Soldering", "Grinding", "Heating"], 1, 3);
    details.fireExtinguishers = randomItems(fireExtinguisherTypes, 1, 3);
  };
  if (perms.includes("CONFINED_SPACE_PERMIT")) {

  };
  if (perms.includes("WORK_AT_HEIGHT_PERMIT")) {
    details.modesOfAccessToBeUsed = randomItems(["Ladder", "Scaffolding", "Aerial Work Platform", "Manlift"], 1, 3);
  };
  if (perms.includes("EXCAVATION_PERMIT")) {
    details.excavationProtectionSystems = randomItems(["Shoring", "Scaffolding", "Trench Boxes", "Benching"], 1, 3);
    details.excavationEquipmentsToBeUsed = randomItems(["Excavator", "Backhoe", "Front Loader", "Hand Tools"], 1, 3);
    details.depthOfExcavation = `${Math.floor(Math.random() * 10) + 1} m`;
  };
  return details;
}
// ---- MAIN SCRIPT ----
(async () => {
  const createdJobIds = [];

  // Create NumberOfCreatedJobs jobs
  for (let i = 0; i < NumberOfCreatedJobs; i++) {
    const variables = {
      input: {
        chiefEngineerId: Math.floor(Math.random() * 5) + 1, // random ID 1-5
        title: randomItem(titles),
        description: `Task: ${randomItem(titles)} - ${Math.floor(Math.random() * 1000)}`,
        location: randomItem(locations),
        timeForCompletion: `P${Math.floor(Math.random() * 5) + 1}D`, // P1D - P5D
        // workerIds: Array.from({length:40}, (_,i)=> i+1),
        tradeIds: getRandomUniqueSubarray(Array.from({ length: 4 }, (_, i) => i + 1)),
      },
    };

    const data = await graphqlRequest(CREATE_JOB_MUTATION, variables);
    const job = data.createJob;
    createdJobIds.push(job.id);
    console.log(`Created job ${job.id} - ${job.title}`);
  }
  // const createdJobIds = Array.from({ length: NumberOfCreatedJobs }, (_, i) => i + 1);
  // Pick NumberOfReviewedJobs jobs randomly for review
  const jobsToReview = createdJobIds.sort(() => Math.random() - 0.5).slice(0, NumberOfReviewedJobs);

  for (const jobId of jobsToReview) {

    const input = {
      jobId,
      reviewedById: Math.floor(Math.random() * 5) + 1, // random ID 1-5
      hazards: getRandomHazards(),
      ppEs: getRandomUniqueSubarray(ppeList),
      precautionsRequired: getRandomUniqueSubarray(precautions),
      /**above is required */
      trainingIds: getRandomUniqueSubarray(Array.from({ length: 7 }, (_, i) => i + 1)),
      ...generatePermitDetails(),
    };


    await reviewJobGraphql(input);
  }
})();
