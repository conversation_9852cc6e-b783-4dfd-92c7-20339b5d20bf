using Shared.GraphQL.Models.Permits;
using Shared.Enums;
using System;
using System.Collections.Generic;

namespace TestPermitJsonStorage
{
    /// <summary>
    /// Example demonstrating how to create and use permits with JSON storage
    /// </summary>
    public class PermitJsonStorageExample
    {
        public static void DemonstratePermitCreation()
        {
            // Example: Creating a General Work Permit
            var generalWorkPermit = new GeneralWorkPermit
            {
                JobId = 1,
                PTWRefNumber = "GWP-2024-001",
                ProjectName = "Office Building Renovation",
                StartingDateTime = DateTime.Now,
                EndingDateTime = DateTime.Now.AddDays(1),
                Description = "General maintenance work",
                Location = "Building A, Floor 2",
                Hazards = "Electrical hazards, working at height",
                PrecautionsRequired = "Use proper PPE, follow safety protocols",
                PPE = "Hard hat, safety boots, high-vis vest",
                Status = PermitStatus.DRAFTED,
                DaysValid = 1,
                Isolation = "Electrical isolation required",
                
                // JSON object for permit issuer
                PermitIssuer = new PermitIssuer
                {
                    CompetentPersons = new List<CompetentPerson>
                    {
                        new CompetentPerson
                        {
                            WorkerId = 101,
                            Name = "<PERSON>",
                            SignatureFileId = "sig-001",
                            SignedAt = DateTime.Now
                        }
                    },
                    AuthorisedPersons = new List<AuthorisedPerson>
                    {
                        new AuthorisedPerson
                        {
                            WorkerId = 102,
                            Name = "Jane Doe",
                            SignatureFileId = "sig-002",
                            SignedAt = DateTime.Now
                        }
                    }
                },
                
                // JSON object for sign off
                SignOff = new SignOff
                {
                    DateTime = DateTime.Now,
                    Workers = new List<PermitWorker>
                    {
                        new PermitWorker
                        {
                            WorkerId = 103,
                            Designation = "Site Supervisor",
                            Name = "Bob Wilson",
                            SignatureFileId = "sig-003",
                            SignedAt = DateTime.Now
                        }
                    }
                },
                
                // JSON array for work area inspections
                WorkAreaInspectionAndPermitRenewal = new List<WorkAreaInspection>
                {
                    new WorkAreaInspection
                    {
                        Name = "Initial Inspection",
                        SignatureFileId = "sig-004",
                        SignedAt = DateTime.Now,
                        Comments = "Area cleared and safe for work"
                    }
                }
            };

            // Example: Creating a Confined Space Permit
            var confinedSpacePermit = new ConfinedSpacePermit
            {
                JobId = 2,
                PTWRefNumber = "CSP-2024-001",
                ProjectName = "Tank Maintenance",
                StartingDateTime = DateTime.Now,
                EndingDateTime = DateTime.Now.AddHours(8),
                Description = "Tank cleaning and inspection",
                Location = "Storage Tank #3",
                Hazards = "Oxygen deficiency, toxic gases",
                PrecautionsRequired = "Continuous air monitoring, rescue team on standby",
                PPE = "Full face respirator, safety harness, gas detector",
                Status = PermitStatus.PENDING_APPROVAL,
                DaysValid = 1,
                WorkersHaveBeenTrained = true,
                NameOfTrainingOrganization = "Safety Training Institute",
                EmergencyGuidelines = "Emergency evacuation procedures attached",
                
                // JSON objects for atmospheric readings
                TopReading = new AtmosphericReading
                {
                    Oxygen = "20.9%",
                    Explosive = "0%",
                    Toxic = "0 ppm",
                    Co2 = "400 ppm"
                },
                MidReading = new AtmosphericReading
                {
                    Oxygen = "20.8%",
                    Explosive = "0%",
                    Toxic = "0 ppm",
                    Co2 = "450 ppm"
                },
                BottomReading = new AtmosphericReading
                {
                    Oxygen = "20.7%",
                    Explosive = "0%",
                    Toxic = "0 ppm",
                    Co2 = "500 ppm"
                },
                
                // JSON object for task observer
                TaskObserver = new TaskObserver
                {
                    WorkerId = 104,
                    Name = "Safety Officer Mike",
                    SignatureFileId = "sig-005",
                    SignedAt = DateTime.Now
                },
                
                // Base permit JSON objects
                PermitIssuer = new PermitIssuer
                {
                    CompetentPersons = new List<CompetentPerson>
                    {
                        new CompetentPerson
                        {
                            WorkerId = 105,
                            Name = "Lead Engineer",
                            SignatureFileId = "sig-006",
                            SignedAt = DateTime.Now
                        }
                    },
                    AuthorisedPersons = new List<AuthorisedPerson>
                    {
                        new AuthorisedPerson
                        {
                            WorkerId = 106,
                            Name = "Plant Manager",
                            SignatureFileId = "sig-007",
                            SignedAt = DateTime.Now
                        }
                    }
                },
                
                SignOff = new SignOff
                {
                    DateTime = DateTime.Now,
                    Workers = new List<PermitWorker>
                    {
                        new PermitWorker
                        {
                            WorkerId = 107,
                            Designation = "Safety Manager",
                            Name = "Sarah Johnson",
                            SignatureFileId = "sig-008",
                            SignedAt = DateTime.Now
                        }
                    }
                }
            };

            Console.WriteLine("Permit examples created successfully!");
            Console.WriteLine($"General Work Permit: {generalWorkPermit.PTWRefNumber}");
            Console.WriteLine($"Confined Space Permit: {confinedSpacePermit.PTWRefNumber}");
        }
    }
}
