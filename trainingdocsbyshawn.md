# Introduction
Below I have outlined the tables and possible workflows for the curren training system. 

# Tables
```sql
TrainingProgram
TrainingProvider
TrainingSession
TrainingEnrollment
ProgramProvider
TrainingCertificate
```

Screenshots for each table have been atached below for any reference. 

## Training Program 
A main catalog of all trainings available in a company. (will eventually be scoped to an organization upon maturity)
![alt text](docs/image.png)

Relationships:
- Has many TrainingSessions
- Has many TrainingCertificates
- Has many TradeRequirements
- Has many ProviderPrograms (junction with providers)


## Training Provider
who is giving out the training. Org/person
![alt text](docs/image-1.png)

Relationships:
- Belongs to Tenant
- Has many TrainingSessions
- Has many TrainingCertificates
- Has many Certifications
- Has many Programs (via the program provider junction table)

## Provider Program 
links provider to programs they;re accredited to offer. 
![alt text](docs/image-2.png)

Relationships:
- Belongs to TrainingProvider
- Belongs to TrainingProgram

> Notes: validates that a provider is authorized to deliver a specific program. along with pricing info per provider. 

## Training Session 
The scheduled training events 
![alt text](docs/image-3.png)

Relationships:
- Belongs to TrainingProgram
- Belongs to TrainingProvider
- Has many TrainingEnrollments
- Has one sessionpicturefile 

> Notes: sessionpicturefile is a file that is uploaded by the provider to represent the training session. 

## Training Enrollment 
![alt text](docs/image-4.png)

Relationships:
- Belongs to TrainingSession
- Belongs to Worker
- Belongs to TrainingProvider

>Notes: One enrollment per worker per session. Enrollment status is updated based on the calendar and outcome(Fail/pass) is determined by the attendance. 

## Training Certificate 
issued certificate for a worker for a specific training session after passing training. 
![alt text](docs/image-5.png)

Relationships:
- Belongs to TrainingSession(nullable)
- Belongs to Worker
- Belongs to TrainingProgram
- Has one certificatefile

>Notes: TrainingSession is nullable because a certificate can be issued for a worker for a specific program without a session. 

## Trade Requirements 
defines which training programs are required for a specific trade. 
![alt text](docs/image-6.png)

Relationships:
- Belongs to TrainingProgram
- Belongs to Trade

# Workflows
## 1. Creating and scheduling a training session (`CreateSessionAsync`)
1. From the service layer, the user will select a training program and a provider. The system will then create a training session and schedule it to the default as Draft. 
2. Validation happens along the path. That is valid program exists, valid provider exists. 
3. Store the conductor information. 

On the db Level. 
INSERT into `TrainingSession`

--------------------------------------

## 2. Enrolling workers in training (`EnrollWorkerAsync`)
### Method 1: individual enrollment
1. Check if enrollment already exists 
2. Create `TrainingEnrollment` with status Registered
3. Optionally if configured, send a notification to the worker. (you have been enrolled blah blah.)

### Method 2: bulk enrollment
1. Validate session is in DRAFT status
2. Validate all workers exist
3. Create enrollment for each worker with status REGISTERED
4. Update session status to SCHEDULED

### Method 3: Batch by Trade
1. Validate session is in DRAFT status
2. Validate trade exists
3. Query all workers with specified trade
4. Create enrollments for workers not already enrolled
5. Update session status to SCHEDULED

On the db level.<br>
INSERT into  `TrainingEnrollments` (one per worker)<br>
UPDATE `TrainingSessions` status to SCHEDULED<br>

--------------------------------------

## 3. Conducting training and marking attendance 
### During Session: 
- Update session status from SCHEDULED to IN_PROGRESS to COMPLETED. 
- Use the function `MarkAttendanceAsync` to mark attendance for each worker. 
    - Updates the enrollment status from registered to Attended or DidNotAttend and CompletionDate to the current date. 

On the db level.<br>
UPDATE `TrainingSessions` status to IN_PROGRESS to COMPLETED.<br>
UPDATE `TrainingEnrollments` status and CompletionDate<br>

--------------------------------------

## 4. Issuing certificates (`FinalizeSessionAsync`)
on the service layer. 
1. Validate session is in COMPLETED status
2. for each attendance record: 
    - Update enrollment status(Attended or DidNotAttend)
    - Set outcome to Pass or Fail. (because i dont know the possibility of someone having attended a training but not fully and therefore failing this is why the status and outcome have been split. it also opens the window for us incase we might ever have any third party integration with a providers api IDK maybe)
    - set CompletedAt timestamp. 
    - If outcome is PASS: 
        - Call `issueCertificateAsync` 
        - Generate certificate number. 
        - Calculate expiry date from program `validitydays` 
        - Upload certificate file to MinIO. 
        - Create `TrainingCertificate` with status Issued. 
        - Optionally if configured send notification. 

    - Update session to FINALIZED. 

On the db level.<br>
UPDATE `TrainingEnrollments` (status, outcome, CompletedAt)<br>
INSERT into `TrainingCertificates` (for passed workers)<br>
INSERT into `FileMetadata` (certificate files)<br>
UPDATE `TrainingSessions` status to FINALIZED<br>

certificate storage is done per program for each worker under the docs bucket. 

--------------------------------------

## 5. Retrieving workers who attended training(`getsessionattendanceasync`)
queries the above method by passing session id as a parameter. 
Returns: all the enrollments within a session with: <br>
    - worker detaild(via navigation property)<br>
    - enrollment status<br>
    - outcome<br>
    - completedat timestamp<br>

From here multiple filter queries can be applied.

--------------------------------------

## 6. Certificate expiry and invalidation
Firstly there is a background service that runs every hour and calls 
`EvaluateandUpdateCertificateStatusesAsync` 

status Transitions: 
ISSUED -> VALID(on first evaluation)
VALID -> EXPIRING_SOON(30 days before expiry)
EXPIRING_SOON -> EXPIRED(on and after expiry date)
EXPIRED -> VALID(upon renewal)

Then secondly there is room for manual invalidation. 
calls `UpdateCertificateStatusAsync` 
- sets status to REVOKED
- Worker loses eligibility. 

On the db level.<br>
UPDATE `trainingcertificates` status field<br>
Background service updates in batch<br>
automatic configuration of notification urgency based on time window. 

## 7. Worker eligibility check(`CheckWorkerEligibilityAsync`)
a utlility service that checks if a worker is eligible within any context. 
1. Get all mandatory programs for trade from TradeRequirements
2. Get worker's valid certificates from TrainingCertificates
3. Check for missing requirements
4. Check for expiring certificates (7-day grace period)
5. Return eligibility status

On the db level.<br>

Return Values:<br>
    - ELIGIBLE: All required certificates valid<br>
    - ELIGIBLE_WITHIN_GRACE: Has all certs but some expiring within 7 days<br>
    - NOT_ELIGIBLE: Missing required certificates

usage: 
- before assigning a worker to a job
- before allowing site access
- periodic compliance checks
--------------------------------------

## 8. Certificate status summary(`GetCertificateStatusSummaryAsync`)
returns a summary of the certificate status for a worker.
1. Get all certificates for worker
2. Categorize by status:
    - Valid: Status = VALID or ISSUED, not expired
    - Expiring: Within 30 days of expiry
    - Expired: Past expiry date or status = EXPIRED
3. Find nearest expiry date
4. Determine overall status (worst case)

Batch query(`getworkerswithcertificatesummaryasync`)
accepts multiple workerids. has a paging component. 
- returns a dictionary: Dictionary<int, CertificateStatusSummary>

----------------------------------------

providers and programs are company level. 


sessions can either belong to a site or company depending on if the sitefield is null or not. 

all queries that implement siteid field will only return sessions that belong to the site. that is when auth works exactly the way its supposed to, both CRUD operations and audit logging will be scoped to the appropriate user permissions. 

