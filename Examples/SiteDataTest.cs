using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using GraphQLApi.Services;
using Shared.DTOs;
using Shared.GraphQL.Models;

namespace Examples
{
    /// <summary>
    /// Simple test to verify the site data system is working
    /// </summary>
    public class SiteDataTest
    {
        public static async Task RunTestAsync()
        {
            Console.WriteLine("🏗️ Testing Site Data Storage System...\n");

            // This would normally be injected via DI
            // For testing purposes, we'll create a simple in-memory context
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: "TestSiteDb")
                .Options;

            using var context = new AppDbContext(options);
            var logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<SiteService>.Instance;
            var siteService = new SiteService(context, logger);

            try
            {
                // Test 1: Create a basic site
                Console.WriteLine("✅ Test 1: Creating a basic site...");
                var basicSite = await siteService.CreateSiteAsync("Test Construction Site");
                Console.WriteLine($"   Created site: {basicSite.Name} (ID: {basicSite.Id})");
                Console.WriteLine($"   Schema Version: {basicSite.SchemaVersion}");
                Console.WriteLine($"   Status: {basicSite.Status}");
                Console.WriteLine();

                // Test 2: Create a site with full structured data
                Console.WriteLine("✅ Test 2: Creating a site with structured data...");
                var siteData = new SiteDataDto
                {
                    ProjectDetails = new ProjectDetailsDto
                    {
                        Name = "Downtown Office Complex",
                        Cost = "50,000,000",
                        MainContractor = "ABC Construction Ltd",
                        Subcontractors = new List<string> { "XYZ Electrical", "DEF Plumbing" },
                        KeyPersonel = new KeyPersonelDto
                        {
                            Architect = "John Smith",
                            Engineer = "Jane Doe"
                        },
                        ContractType = "Fixed Price",
                        ProjectType = new List<string> { "Commercial" }
                    },
                    SiteSpecification = new SiteSpecificationDto
                    {
                        SiteLocation = new SiteLocationDto
                        {
                            TotalArea = "5000 sq meters",
                            LocationMap = "GPS: -1.2921, 36.8219"
                        },
                        BuildingStats = new BuildingStatsDto
                        {
                            Floors = "15",
                            Basement = "2",
                            Parking = "200 spaces"
                        }
                    },
                    RegulatoryCompliance = new RegulatoryComplianceDto
                    {
                        BuildingPermit = new BuildingPermitDto
                        {
                            PermitNumber = "BP-2024-001",
                            PermitSpecification = "Commercial Building Permit",
                            PermitType = "New Construction"
                        }
                    }
                };

                var structuredSite = await siteService.CreateSiteAsync("Downtown Office Complex", siteData);
                Console.WriteLine($"   Created structured site: {structuredSite.Name}");
                Console.WriteLine($"   JSON Data Length: {structuredSite.SiteDataJson.Length} characters");
                Console.WriteLine($"   Cached Project Manager: {structuredSite.ProjectManager}");
                Console.WriteLine();

                // Test 3: Retrieve and verify data
                Console.WriteLine("✅ Test 3: Retrieving and verifying data...");
                var retrievedData = await siteService.GetSiteDataAsync(structuredSite.Id);
                if (retrievedData?.ProjectDetails != null)
                {
                    Console.WriteLine($"   Project Name: {retrievedData.ProjectDetails.Name}");
                    Console.WriteLine($"   Cost: {retrievedData.ProjectDetails.Cost}");
                    Console.WriteLine($"   Contractor: {retrievedData.ProjectDetails.MainContractor}");
                    Console.WriteLine($"   Architect: {retrievedData.ProjectDetails.KeyPersonel?.Architect}");
                    Console.WriteLine($"   Engineer: {retrievedData.ProjectDetails.KeyPersonel?.Engineer}");
                }
                Console.WriteLine();

                // Test 4: Update with JSON patch
                Console.WriteLine("✅ Test 4: Testing JSON patch update...");
                var jsonPatch = @"{
                    ""projectDetails"": {
                        ""cost"": ""55,000,000"",
                        ""keyPersonel"": {
                            ""engineer"": ""Bob Johnson""
                        }
                    },
                    ""customFields"": {
                        ""environmentalImpact"": ""Low"",
                        ""sustainabilityRating"": ""A+""
                    }
                }";

                var patchedSite = await siteService.PatchSiteDataAsync(structuredSite.Id, jsonPatch);
                if (patchedSite != null)
                {
                    Console.WriteLine($"   Successfully patched site data");
                    Console.WriteLine($"   Updated JSON Length: {patchedSite.SiteDataJson.Length} characters");
                }
                Console.WriteLine();

                // Test 5: Search and query
                Console.WriteLine("✅ Test 5: Testing search and query functions...");
                var allSites = await siteService.GetAllSitesAsync();
                Console.WriteLine($"   Total sites: {allSites.Count()}");

                var planningSites = await siteService.GetSitesByStatusAsync("planning");
                Console.WriteLine($"   Sites in planning: {planningSites.Count()}");

                var searchResults = await siteService.SearchSitesAsync("Downtown");
                Console.WriteLine($"   Sites matching 'Downtown': {searchResults.Count()}");
                Console.WriteLine();

                // Test 6: Clone site
                Console.WriteLine("✅ Test 6: Testing site cloning...");
                var clonedSite = await siteService.CloneSiteAsync(structuredSite.Id, "Cloned Office Complex");
                if (clonedSite != null)
                {
                    Console.WriteLine($"   Successfully cloned site: {clonedSite.Name}");
                    Console.WriteLine($"   Clone ID: {clonedSite.Id}");
                    
                    var clonedData = await siteService.GetSiteDataAsync(clonedSite.Id);
                    Console.WriteLine($"   Cloned data preserved: {clonedData?.ProjectDetails?.Name}");
                }
                Console.WriteLine();

                Console.WriteLine("🎉 All tests completed successfully!");
                Console.WriteLine("\n📊 Summary:");
                Console.WriteLine($"   • Created {allSites.Count()} sites");
                Console.WriteLine($"   • Tested structured data storage and retrieval");
                Console.WriteLine($"   • Verified JSON patching functionality");
                Console.WriteLine($"   • Confirmed search and query operations");
                Console.WriteLine($"   • Validated site cloning feature");
                Console.WriteLine("\n✨ Your flexible site data storage system is ready to use!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }
        }
    }
}
