# Site Data GraphQL Examples

## 🚀 Query Examples

### Get All Sites
```graphql
query GetAllSites {
  getAllSites {
    id
    name
    status
    healthStatus
    projectType
    startDate
    endDate
    progressPercentage
    schemaVersion
    createdAt
    updatedAt
  }
}
```

### Get Site by ID with Full Data
```graphql
query GetSiteById($id: UUID!) {
  getSiteById(id: $id) {
    id
    name
    status
    healthStatus
    projectManager
    location
    
    # Raw JSON for maximum flexibility
    siteDataJson
    
    # Structured data access
    siteData {
      projectDetails {
        name
        cost
        mainContractor
        subcontractors
        keyPersonel {
          architect
          engineer
        }
        contractType
        projectType
      }
      
      siteSpecification {
        siteLocation {
          totalArea
          locationMap
        }
        buildingStats {
          floors
          basement
          parking
        }
        buildingFootprint {
          buildingArea
          builtArea
        }
        utilitiesServices {
          water
          electricity
          sewer
          internet
        }
        accessRoads {
          mainAccessRoads
          secondaryAccessRoads
        }
      }
      
      regulatoryCompliance {
        buildingPermit {
          permitNumber
          permitSpecification
          permitType
        }
        classification {
          buildingClass
          constructionType
        }
        fireSafetyRating
        complianceStandard {
          accessibility
          environmental
        }
        occupancyType
      }
      
      projectTimeline {
        startDate
        endDate
        milestones
      }
      
      emergencyContacts {
        police
        fireDepartment
        medicalServices
        emergencyManagement
      }
      
      siteCommittee {
        committeeMembers
      }
    }
    
    createdAt
    createdBy
    updatedAt
    updatedBy
  }
}
```

### Search Sites
```graphql
query SearchSites($searchTerm: String!) {
  searchSites(searchTerm: $searchTerm) {
    id
    name
    location
    projectManager
    status
    projectType
  }
}
```

### Get Sites by Status
```graphql
query GetSitesByStatus($status: String!) {
  getSitesByStatus(status: $status) {
    id
    name
    location
    projectManager
    startDate
    endDate
    progressPercentage
  }
}
```

## 🔧 Mutation Examples

### Create Basic Site
```graphql
mutation CreateBasicSite($name: String!) {
  createSite(name: $name) {
    id
    name
    status
    healthStatus
    schemaVersion
    createdAt
  }
}
```

### Create Site with Full Data
```graphql
mutation CreateSiteWithData($name: String!, $siteData: SiteDataInput!) {
  createSite(name: $name, siteData: $siteData) {
    id
    name
    status
    projectManager
    location
    siteDataJson
    createdAt
  }
}
```

**Variables:**
```json
{
  "name": "Downtown Office Complex",
  "siteData": {
    "projectDetails": {
      "name": "Downtown Office Complex",
      "cost": "50,000,000",
      "mainContractor": "ABC Construction Ltd",
      "subcontractors": ["XYZ Electrical", "DEF Plumbing"],
      "keyPersonel": {
        "architect": "John Smith",
        "engineer": "Jane Doe"
      },
      "contractType": "Fixed Price",
      "projectType": ["Commercial"]
    },
    "siteSpecification": {
      "siteLocation": {
        "totalArea": "5000 sq meters",
        "locationMap": "GPS: -1.2921, 36.8219"
      },
      "buildingStats": {
        "floors": "15",
        "basement": "2",
        "parking": "200 spaces"
      }
    },
    "regulatoryCompliance": {
      "buildingPermit": {
        "permitNumber": "BP-2024-001",
        "permitSpecification": "Commercial Building Permit",
        "permitType": "New Construction"
      }
    }
  }
}
```

### Update Site Data
```graphql
mutation UpdateSiteData($id: UUID!, $siteData: SiteDataInput!) {
  updateSiteData(id: $id, siteData: $siteData) {
    id
    name
    siteDataJson
    updatedAt
  }
}
```

### Patch Site Data (JSON)
```graphql
mutation PatchSiteData($id: UUID!, $jsonPatch: String!) {
  patchSiteData(id: $id, jsonPatch: $jsonPatch) {
    id
    name
    siteDataJson
    updatedAt
  }
}
```

**Variables:**
```json
{
  "id": "your-site-id-here",
  "jsonPatch": "{\"projectDetails\":{\"cost\":\"55,000,000\"},\"customFields\":{\"environmentalImpact\":\"Low\",\"sustainabilityRating\":\"A+\"}}"
}
```

### Clone Site
```graphql
mutation CloneSite($sourceId: UUID!, $newName: String!) {
  cloneSite(sourceId: $sourceId, newName: $newName) {
    id
    name
    siteDataJson
    createdAt
  }
}
```

### Delete Site
```graphql
mutation DeleteSite($id: UUID!) {
  deleteSite(id: $id)
}
```

## 🎯 Key Benefits

1. **Schema Flexibility**: Store any JSON structure without database migrations
2. **Type Safety**: Strongly typed GraphQL schema for your current structure
3. **Performance**: Cached fields for fast queries and filtering
4. **Extensibility**: Add custom fields anytime using JSON patching
5. **Future-Proof**: Ready for authentication and authorization integration

## 🔍 Usage Tips

- Use structured queries for type safety and IntelliSense
- Use `siteDataJson` field for custom processing or when you need raw JSON
- Use JSON patching for adding custom fields without changing the schema
- Cache frequently queried fields in the Site model for better performance
- Clone sites to create templates for similar projects
