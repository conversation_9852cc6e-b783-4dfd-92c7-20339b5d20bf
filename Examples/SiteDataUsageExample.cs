using Shared.GraphQL.Models;
using Shared.DTOs;
using GraphQLApi.Services;
using System.Text.Json;

namespace Examples
{
    /// <summary>
    /// Example demonstrating how to use the flexible site data storage system
    /// </summary>
    public class SiteDataUsageExample
    {
        private readonly ISiteService _siteService;

        public SiteDataUsageExample(ISiteService siteService)
        {
            _siteService = siteService;
        }

        /// <summary>
        /// Example 1: Create a site with structured data using your JSON schema
        /// </summary>
        public async Task<Site> CreateSiteWithStructuredDataAsync()
        {
            var siteData = new SiteDataDto
            {
                ProjectDetails = new ProjectDetailsDto
                {
                    Name = "Downtown Office Complex",
                    Cost = "50,000,000",
                    MainContractor = "ABC Construction Ltd",
                    Subcontractors = new List<string> { "XYZ Electrical", "DEF Plumbing" },
                    KeyPersonel = new KeyPersonelDto
                    {
                        Architect = "<PERSON>",
                        Engineer = "<PERSON>"
                    },
                    ContractType = "Fixed Price",
                    ProjectType = new List<string> { "Commercial" }
                },
                SiteSpecification = new SiteSpecificationDto
                {
                    SiteLocation = new SiteLocationDto
                    {
                        TotalArea = "5000 sq meters",
                        LocationMap = "GPS: -1.2921, 36.8219"
                    },
                    BuildingStats = new BuildingStatsDto
                    {
                        Floors = "15",
                        Basement = "2",
                        Parking = "200 spaces"
                    },
                    BuildingFootprint = new BuildingFootprintDto
                    {
                        BuildingArea = "3000 sq meters",
                        BuiltArea = "45000 sq meters"
                    },
                    UtilitiesServices = new UtilitiesServicesDto
                    {
                        Water = "Nairobi City Water",
                        Electricity = "Kenya Power",
                        Sewer = "Nairobi City Sewer",
                        Internet = "Safaricom Fiber"
                    },
                    AccessRoads = new AccessRoadsDto
                    {
                        MainAccessRoads = new List<string> { "Uhuru Highway" },
                        SecondaryAccessRoads = new List<string> { "Haile Selassie Avenue" }
                    }
                },
                RegulatoryCompliance = new RegulatoryComplianceDto
                {
                    BuildingPermit = new BuildingPermitDto
                    {
                        PermitNumber = "BP-2024-001",
                        PermitSpecification = "Commercial Building Permit",
                        PermitType = "New Construction"
                    },
                    Classification = new ClassificationDto
                    {
                        BuildingClass = "Class A Office",
                        ConstructionType = "Type I - Fire Resistant"
                    },
                    FireSafetyRating = "Excellent",
                    ComplianceStandard = new ComplianceStandardDto
                    {
                        Accessibility = "ADA Compliant",
                        Environmental = "LEED Gold Certified"
                    },
                    OccupancyType = "Business"
                },
                ProjectTimeline = new ProjectTimelineDto
                {
                    StartDate = "2024-01-15",
                    EndDate = "2026-12-31",
                    Milestones = new List<string> 
                    { 
                        "Foundation Complete - 2024-06-30",
                        "Structure Complete - 2025-06-30",
                        "Fit-out Complete - 2026-10-31"
                    }
                }
            };

            return await _siteService.CreateSiteAsync("Downtown Office Complex", siteData);
        }

        /// <summary>
        /// Example 2: Create a site with minimal data and add more later
        /// </summary>
        public async Task<Site> CreateMinimalSiteAsync()
        {
            // Create with just a name
            var site = await _siteService.CreateSiteAsync("New Construction Site");

            // Later, add more data
            var basicData = new SiteDataDto
            {
                ProjectDetails = new ProjectDetailsDto
                {
                    Name = "New Construction Site",
                    ProjectType = new List<string> { "Residential" }
                }
            };

            return await _siteService.UpdateSiteDataAsync(site.Id, basicData);
        }

        /// <summary>
        /// Example 3: Update site data using JSON patching for flexibility
        /// </summary>
        public async Task<Site?> UpdateSiteWithJsonPatchAsync(Guid siteId)
        {
            // You can update any part of the JSON structure
            var jsonPatch = @"{
                ""projectDetails"": {
                    ""cost"": ""55,000,000"",
                    ""keyPersonel"": {
                        ""engineer"": ""Bob Johnson""
                    }
                },
                ""customFields"": {
                    ""environmentalImpact"": ""Low"",
                    ""communityBenefit"": ""High"",
                    ""sustainabilityRating"": ""A+""
                }
            }";

            return await _siteService.PatchSiteDataAsync(siteId, jsonPatch);
        }

        /// <summary>
        /// Example 4: Retrieve and work with site data
        /// </summary>
        public async Task<void> WorkWithSiteDataAsync(Guid siteId)
        {
            // Get the site
            var site = await _siteService.GetSiteByIdAsync(siteId);
            if (site == null) return;

            // Get structured data
            var siteData = await _siteService.GetSiteDataAsync(siteId);
            if (siteData?.ProjectDetails != null)
            {
                Console.WriteLine($"Project: {siteData.ProjectDetails.Name}");
                Console.WriteLine($"Cost: {siteData.ProjectDetails.Cost}");
                Console.WriteLine($"Contractor: {siteData.ProjectDetails.MainContractor}");
            }

            // Get raw JSON for custom processing
            var rawJson = site.SiteDataJson;
            var jsonDocument = JsonDocument.Parse(rawJson);
            
            // You can access any custom fields you've added
            if (jsonDocument.RootElement.TryGetProperty("customFields", out var customFields))
            {
                if (customFields.TryGetProperty("sustainabilityRating", out var rating))
                {
                    Console.WriteLine($"Sustainability Rating: {rating.GetString()}");
                }
            }
        }

        /// <summary>
        /// Example 5: Search and filter sites
        /// </summary>
        public async Task<void> SearchSitesAsync()
        {
            // Search by various criteria
            var commercialSites = await _siteService.GetSitesByProjectTypeAsync("Commercial");
            var activeSites = await _siteService.GetSitesByStatusAsync("active");
            var searchResults = await _siteService.SearchSitesAsync("Downtown");
            
            Console.WriteLine($"Found {commercialSites.Count()} commercial sites");
            Console.WriteLine($"Found {activeSites.Count()} active sites");
            Console.WriteLine($"Found {searchResults.Count()} sites matching 'Downtown'");
        }

        /// <summary>
        /// Example 6: Clone a site for similar projects
        /// </summary>
        public async Task<Site?> CloneSiteAsync(Guid templateSiteId)
        {
            return await _siteService.CloneSiteAsync(templateSiteId, "New Site Based on Template");
        }

        /// <summary>
        /// Example 7: Extend the schema with custom fields
        /// </summary>
        public async Task<Site?> AddCustomFieldsAsync(Guid siteId)
        {
            // The beauty of this approach is you can add any fields you want
            var customData = @"{
                ""projectDetails"": {
                    ""name"": ""Extended Project""
                },
                ""customSections"": {
                    ""qualityControl"": {
                        ""inspectionSchedule"": ""Weekly"",
                        ""qualityStandards"": [""ISO 9001"", ""Local Building Code""],
                        ""inspectionTeam"": [""John Inspector"", ""Jane Auditor""]
                    },
                    ""riskManagement"": {
                        ""riskLevel"": ""Medium"",
                        ""mitigationStrategies"": [""Regular safety meetings"", ""Equipment checks""],
                        ""insuranceCoverage"": ""Full""
                    },
                    ""stakeholders"": {
                        ""primaryContact"": ""Project Manager"",
                        ""secondaryContacts"": [""Site Engineer"", ""Safety Officer""],
                        ""clientRepresentative"": ""Client PM""
                    }
                }
            }";

            return await _siteService.PatchSiteDataAsync(siteId, customData);
        }
    }
}
