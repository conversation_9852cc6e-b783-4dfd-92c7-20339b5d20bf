# Training System Composition Architecture

## Core Philosophy: Composition over Inheritance

Instead of extending existing training/certificate systems, we build **standalone, composable components** that work together through well-defined interfaces.

## 1. Certificate Management via Composition

### Current Issues with Inheritance/Extension:
- Complex dependencies on old training system
- Tight coupling between certificate issuance and training records
- Difficult to test and maintain

### Composition Approach:

```csharp
// Standalone Certificate Manager (no inheritance)
public interface ICertificateManager
{
    Task<Certificate> IssueAsync(CertificateRequest request);
    Task<Certificate> RenewAsync(int certificateId, RenewRequest request);
    Task<Certificate> RevokeAsync(int certificateId, string reason);
    Task<IEnumerable<Certificate>> GetExpiringAsync(int daysAhead = 30);
    Task<CertificateValidationResult> ValidateAsync(int certificateId);
}

// Certificate Request (composition of data)
public class CertificateRequest
{
    // Core certificate data
    public int WorkerId { get; set; }
    public string CertificateType { get; set; } // e.g., "TrainingCertificate", "MedicalCertificate"
    public string Source { get; set; } // e.g., "TrainingSession", "ExternalProvider", "Manual"
    public string SourceId { get; set; } // ID of the source (session ID, etc.)
    
    // Certificate details
    public string CertificateNo { get; set; }
    public DateTime IssueDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public string IssuedBy { get; set; }
    public Dictionary<string, object> Metadata { get; set; } // Flexible metadata
    
    // File attachments
    public IFile? CertificateFile { get; set; }
    public List<IFile>? SupportingDocuments { get; set; }
}

// Generic Certificate (not tied to training)
public class Certificate : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int WorkerId { get; set; }
    public string CertificateNo { get; set; }
    public string CertificateType { get; set; } // "TrainingCertificate", "MedicalCertificate", etc.
    public string Source { get; set; } // Where it came from
    public string SourceId { get; set; } // Reference to source
    public DateTime IssueDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public CertificateStatus Status { get; set; }
    public string IssuedBy { get; set; }
    public string? Metadata { get; set; } // JSON for flexible data
    public int? CertificateFileId { get; set; }
    
    // Navigation
    public virtual Worker Worker { get; set; } = null!;
    public virtual FileMetadata? CertificateFile { get; set; }
    
    // Audit & Soft Delete
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    // ... other audit fields
}
```

### Training System Integration via Composition:

```csharp
// Training service composes with certificate manager
public class TrainingService : ITrainingService
{
    private readonly ICertificateManager _certificateManager;
    private readonly INotificationComposer _notificationComposer;
    
    public async Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance)
    {
        // ... finalize session logic
        
        // Issue certificates for passed attendees (composition)
        foreach (var attendee in attendance.Where(a => a.Outcome == TrainingOutcome.PASS))
        {
            var certificateRequest = new CertificateRequest
            {
                WorkerId = attendee.WorkerId,
                CertificateType = "TrainingCertificate",
                Source = "TrainingSession",
                SourceId = sessionId.ToString(),
                CertificateNo = GenerateCertificateNumber(session, attendee.WorkerId),
                IssueDate = DateTime.UtcNow,
                ExpiryDate = CalculateExpiryDate(session.Program.ValidityDays),
                IssuedBy = session.Conductor.Name,
                Metadata = new Dictionary<string, object>
                {
                    ["ProgramId"] = session.ProgramId,
                    ["ProgramTitle"] = session.Program.Title,
                    ["ProviderId"] = session.ProviderId,
                    ["SessionId"] = sessionId,
                    ["Outcome"] = attendee.Outcome.ToString()
                }
            };
            
            var certificate = await _certificateManager.IssueAsync(certificateRequest);
            
            // Compose notification (no inheritance)
            await _notificationComposer.ComposeAndSendAsync("certificate.issued", new
            {
                CertificateId = certificate.Id,
                WorkerId = attendee.WorkerId,
                ProgramTitle = session.Program.Title
            });
        }
    }
}
```

## 2. Notification Composition (Replace Training Alerts)

### Notification Composer Pattern:

```csharp
public interface INotificationComposer
{
    Task ComposeAndSendAsync(string eventType, object data);
    Task ComposeAndSendAsync(string eventType, object data, NotificationOptions options);
}

public class TrainingNotificationComposer : INotificationComposer
{
    private readonly INotificationService _notificationService;
    private readonly ICertificateManager _certificateManager;
    
    public async Task ComposeAndSendAsync(string eventType, object data)
    {
        var notification = eventType switch
        {
            "certificate.expiring" => await ComposeCertificateExpiryNotification(data),
            "certificate.issued" => ComposeCertificateIssuedNotification(data),
            "session.invitation" => ComposeSessionInvitationNotification(data),
            _ => throw new ArgumentException($"Unknown event type: {eventType}")
        };
        
        await _notificationService.PublishToUserAsync(notification.Event, notification.UserId);
    }
    
    private async Task<(NotificationEvent Event, int UserId)> ComposeCertificateExpiryNotification(object data)
    {
        // Compose notification from certificate data
        var certificateData = JsonSerializer.Deserialize<CertificateExpiryData>(JsonSerializer.Serialize(data));
        var certificate = await _certificateManager.GetByIdAsync(certificateData.CertificateId);
        
        var daysUntilExpiry = (certificate.ExpiryDate - DateTime.UtcNow).Days;
        var priority = daysUntilExpiry <= 7 ? NotificationPriority.High : NotificationPriority.Medium;
        
        var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(certificate.Metadata ?? "{}");
        var programTitle = metadata.GetValueOrDefault("ProgramTitle", "Training")?.ToString();
        
        return (new NotificationEvent
        {
            Type = "training.certificate.expiring",
            Title = "Training Certificate Expiring",
            Message = $"Your {programTitle} certificate expires in {daysUntilExpiry} days",
            Priority = priority,
            Entity = "Certificate",
            EntityId = certificate.Id.ToString(),
            ActionUrl = $"/certificates/{certificate.Id}",
            ActionLabel = "View Certificate",
            Metadata = JsonSerializer.Serialize(new
            {
                CertificateId = certificate.Id,
                CertificateType = certificate.CertificateType,
                Source = certificate.Source,
                ExpiryDate = certificate.ExpiryDate,
                DaysUntilExpiry = daysUntilExpiry,
                ProgramTitle = programTitle
            })
        }, certificate.WorkerId);
    }
}
```

## 3. Company-Level Training Provider Management

### Multi-Tenant Provider Management:

```csharp
// Company-scoped training provider
public class TrainingProvider : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int TenantId { get; set; } // Company/Tenant scope
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Contact { get; set; } = string.Empty;
    public decimal Charges { get; set; }
    public ProviderType Type { get; set; } // Internal, External, Partner
    public string? Certifications { get; set; } // JSON array
    public string? AccreditationInfo { get; set; } // JSON object
    public bool Active { get; set; } = true;
    
    // Navigation Properties
    public virtual Tenant Tenant { get; set; } = null!;
    public ICollection<TrainingSession> Sessions { get; set; } = new List<TrainingSession>();
    public ICollection<Certificate> IssuedCertificates { get; set; } = new List<Certificate>();
    
    // Provider capabilities (composition)
    public ICollection<TrainingProgram> AuthorizedPrograms { get; set; } = new List<TrainingProgram>();
}

public enum ProviderType
{
    Internal,    // Company's own trainers
    External,    // Third-party providers
    Partner,     // Strategic partners
    Certified    // Certified external providers
}

// Provider management service (tenant-scoped)
public interface ITrainingProviderManager
{
    Task<TrainingProvider> CreateProviderAsync(int tenantId, CreateProviderRequest request);
    Task<TrainingProvider> UpdateProviderAsync(int providerId, UpdateProviderRequest request);
    Task<IEnumerable<TrainingProvider>> GetTenantProvidersAsync(int tenantId);
    Task AuthorizeProviderForProgramAsync(int providerId, int programId);
    Task RevokeProviderAuthorizationAsync(int providerId, int programId);
    Task<ProviderCapabilityReport> GetProviderCapabilitiesAsync(int providerId);
}
```

### Company-Level Provider Dashboard:

```csharp
// GraphQL mutations for company admin
[ExtendObjectType("Mutation")]
public class TrainingProviderMutations
{
    [AuthorizePermission("TrainingProviders", "Create", "Tenant")]
    public async Task<TrainingProvider> CreateTrainingProviderAsync(
        [Service] ITrainingProviderManager providerManager,
        [Service] ICurrentUserService currentUser,
        CreateTrainingProviderInput input)
    {
        var tenantId = await currentUser.GetTenantIdAsync();
        
        return await providerManager.CreateProviderAsync(tenantId, new CreateProviderRequest
        {
            Name = input.Name,
            Description = input.Description,
            Contact = input.Contact,
            Type = input.Type,
            Charges = input.Charges,
            Certifications = input.Certifications?.ToList(),
            AccreditationInfo = input.AccreditationInfo
        });
    }
    
    [AuthorizePermission("TrainingProviders", "Update", "Tenant")]
    public async Task<TrainingProvider> AuthorizeProviderForProgramAsync(
        [Service] ITrainingProviderManager providerManager,
        [Service] ICurrentUserService currentUser,
        int providerId,
        int programId)
    {
        // Verify provider belongs to current tenant
        await ValidateProviderOwnership(currentUser, providerId);
        
        await providerManager.AuthorizeProviderForProgramAsync(providerId, programId);
        return await providerManager.GetProviderByIdAsync(providerId);
    }
}

// GraphQL queries for company dashboard
[ExtendObjectType("Query")]
public class TrainingProviderQueries
{
    [AuthorizePermission("TrainingProviders", "Read", "Tenant")]
    public async Task<IEnumerable<TrainingProvider>> GetCompanyTrainingProvidersAsync(
        [Service] ITrainingProviderManager providerManager,
        [Service] ICurrentUserService currentUser)
    {
        var tenantId = await currentUser.GetTenantIdAsync();
        return await providerManager.GetTenantProvidersAsync(tenantId);
    }
    
    [AuthorizePermission("TrainingProviders", "Read", "Tenant")]
    public async Task<TrainingProviderDashboard> GetProviderDashboardAsync(
        [Service] ITrainingProviderManager providerManager,
        [Service] ICurrentUserService currentUser,
        int? providerId = null)
    {
        var tenantId = await currentUser.GetTenantIdAsync();
        return await providerManager.GetTenantProviderDashboardAsync(tenantId, providerId);
    }
}
```

## 4. Auth Integration via Composition

### Permission-Based Access Control:

```csharp
// Training-specific permissions (compose with existing auth)
public static class TrainingPermissions
{
    // Training Programs
    public const string CreatePrograms = "TrainingPrograms.Create";
    public const string ReadPrograms = "TrainingPrograms.Read";
    public const string UpdatePrograms = "TrainingPrograms.Update";
    public const string DeletePrograms = "TrainingPrograms.Delete";
    
    // Training Providers (Company-level)
    public const string CreateProviders = "TrainingProviders.Create";
    public const string ReadProviders = "TrainingProviders.Read";
    public const string UpdateProviders = "TrainingProviders.Update";
    public const string DeleteProviders = "TrainingProviders.Delete";
    
    // Training Sessions
    public const string CreateSessions = "TrainingSessions.Create";
    public const string ReadSessions = "TrainingSessions.Read";
    public const string UpdateSessions = "TrainingSessions.Update";
    public const string FinalizeSessions = "TrainingSessions.Finalize";
    
    // Certificates
    public const string IssueCertificates = "Certificates.Issue";
    public const string ReadCertificates = "Certificates.Read";
    public const string RevokeCertificates = "Certificates.Revoke";
    
    // Worker Eligibility
    public const string CheckEligibility = "WorkerEligibility.Check";
    public const string ManageEligibility = "WorkerEligibility.Manage";
}

// Compose with existing permission system
public class TrainingAuthorizationService
{
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserService _currentUser;
    
    public async Task<bool> CanManageProviderAsync(int providerId)
    {
        var hasPermission = await _permissionService.HasPermissionAsync(
            await _currentUser.GetUserIdAsync(),
            TrainingPermissions.UpdateProviders,
            "Tenant");
            
        if (!hasPermission) return false;
        
        // Additional check: provider belongs to user's tenant
        var provider = await GetProviderAsync(providerId);
        var userTenantId = await _currentUser.GetTenantIdAsync();
        
        return provider.TenantId == userTenantId;
    }
}
```

## Benefits of Composition Approach:

1. **No Inheritance Complexity**: Each component is standalone
2. **Flexible Certificate Types**: Can issue medical, training, safety certificates using same system
3. **Clean Separation**: Training sessions, certificates, and notifications are loosely coupled
4. **Easy Testing**: Each component can be tested independently
5. **Extensible**: New certificate types or notification types via composition
6. **Company-Scoped**: Training providers are properly scoped to tenants/companies
7. **Permission Integration**: Composes with existing auth without inheritance

## Migration Strategy:

1. **Phase 1**: Build new Certificate Manager (standalone)
2. **Phase 2**: Build Notification Composer (replace training alerts)
3. **Phase 3**: Build Training Provider Manager (company-scoped)
4. **Phase 4**: Update Training Service to use composition
5. **Phase 5**: Remove old training dependencies from Worker model
6. **Phase 6**: Clean up old training tables and services

This approach ensures we build a robust, maintainable system without the complexity of inheritance or tight coupling with existing training systems.





