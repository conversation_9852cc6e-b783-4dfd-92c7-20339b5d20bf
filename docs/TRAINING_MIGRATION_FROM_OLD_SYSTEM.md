# Migration from Old Training System

## Issues with Current Training System

### 1. Worker Model Dependencies (REMOVE THESE):
```csharp
// In Worker.cs - THESE NEED TO BE REMOVED:
public ICollection<Training> Trainings { get; set; } = new List<Training>();
public ICollection<WorkerTraining> WorkerTrainings { get; set; } = new List<WorkerTraining>();
public ICollection<WorkerTrainingHistory> TrainingHistory { get; set; } = [];

// REPLACE WITH:
// No direct training collections - use composition through services
```

### 2. Old Services in Mutation.cs (REMOVE THESE):
```csharp
// REMOVE THESE OLD SERVICES:
private readonly ITrainingService _trainingService; // OLD ONE
private readonly IWorkerTrainingService _workerTrainingService;
private readonly ITrainingStatusService _trainingStatusService;

// REPLACE WITH NEW COMPOSITION-BASED SERVICES:
private readonly GraphQLApi.Services.ITrainingService _newTrainingService;
private readonly ICertificateManager _certificateManager;
private readonly INotificationComposer _notificationComposer;
private readonly ITrainingProviderManager _providerManager;
```

### 3. Database Tables to Remove/Replace:
- `Trainings` → `TrainingPrograms` (already created)
- `WorkerTrainings` → Use `Certificates` with `CertificateType = "TrainingCertificate"`
- `WorkerTrainingHistory` → Use audit fields in `Certificates` and `TrainingSessions`
- `training_alerts` → Use `Notifications` system

## Migration Steps

### Phase 1: Update Worker Model (Remove Old Dependencies)

```csharp
// NEW Worker.cs (clean version)
public class Worker : IAuditableEntity, ISoftDeletable
{
    // ... existing properties ...
    
    // REMOVE OLD TRAINING COLLECTIONS:
    // public ICollection<Training> Trainings { get; set; } = new List<Training>();
    // public ICollection<WorkerTraining> WorkerTrainings { get; set; } = new List<WorkerTraining>();
    // public ICollection<WorkerTrainingHistory> TrainingHistory { get; set; } = [];

    // KEEP ONLY NECESSARY RELATIONSHIPS:
    public ICollection<Trade> Trades { get; set; } = [];
    public ICollection<Skill> Skills { get; set; } = [];
    public ICollection<Incident> Incidents { get; set; } = [];

    // COMPUTED PROPERTIES - UPDATE TO USE NEW SYSTEM:
    // Remove: public int TrainingsCompleted => TrainingHistory?.Count(t => t.Status == TrainingStatus.Completed) ?? 0;
    // This will be computed via services when needed
}
```

### Phase 2: Update GraphQL Mutations (Remove Old Services)

```csharp
// NEW Mutation.cs constructor (composition-based)
public Mutation(
    IWorkerService workerService,
    // REMOVE OLD TRAINING SERVICES:
    // ITrainingService trainingService,
    // IWorkerTrainingService workerTrainingService,
    // ITrainingStatusService trainingStatusService,
    
    // ADD NEW COMPOSITION-BASED SERVICES:
    GraphQLApi.Services.ITrainingService newTrainingService,
    ICertificateManager certificateManager,
    INotificationComposer notificationComposer,
    ITrainingProviderManager providerManager,
    
    // ... other services
    )
{
    _workerService = workerService;
    _newTrainingService = newTrainingService;
    _certificateManager = certificateManager;
    _notificationComposer = notificationComposer;
    _providerManager = providerManager;
    // ... other services
}
```

### Phase 3: Create Worker Training Composer (Replaces Direct Dependencies)

```csharp
// NEW: WorkerTrainingComposer.cs
public interface IWorkerTrainingComposer
{
    Task<IEnumerable<Certificate>> GetWorkerTrainingCertificatesAsync(int workerId);
    Task<WorkerTrainingStatus> GetWorkerTrainingStatusAsync(int workerId);
    Task<WorkerEligibilityReport> GetWorkerEligibilityReportAsync(int workerId);
    Task<int> GetTrainingsCompletedCountAsync(int workerId);
}

public class WorkerTrainingComposer : IWorkerTrainingComposer
{
    private readonly ICertificateManager _certificateManager;
    private readonly GraphQLApi.Services.ITrainingService _trainingService;

    public async Task<IEnumerable<Certificate>> GetWorkerTrainingCertificatesAsync(int workerId)
    {
        var allCertificates = await _certificateManager.GetWorkerCertificatesAsync(workerId);
        return allCertificates.Where(c => c.CertificateType == "TrainingCertificate");
    }

    public async Task<WorkerTrainingStatus> GetWorkerTrainingStatusAsync(int workerId)
    {
        var certificates = await GetWorkerTrainingCertificatesAsync(workerId);
        
        return new WorkerTrainingStatus
        {
            WorkerId = workerId,
            TotalCertificates = certificates.Count(),
            ValidCertificates = certificates.Count(c => c.Status == CertificateStatus.VALID),
            ExpiringCertificates = certificates.Count(c => c.ExpiryDate <= DateTime.UtcNow.AddDays(30)),
            ExpiredCertificates = certificates.Count(c => c.Status == CertificateStatus.EXPIRED)
        };
    }

    public async Task<int> GetTrainingsCompletedCountAsync(int workerId)
    {
        var certificates = await GetWorkerTrainingCertificatesAsync(workerId);
        return certificates.Count(c => c.Status == CertificateStatus.VALID || c.Status == CertificateStatus.EXPIRED);
    }
}
```

### Phase 4: Update Training System to Use Composition

```csharp
// UPDATED TrainingService.cs to use composition
public class TrainingService : ITrainingService
{
    private readonly ICertificateManager _certificateManager;
    private readonly INotificationComposer _notificationComposer;
    private readonly ITrainingProviderManager _providerManager;
    
    public async Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance)
    {
        // ... finalize session logic
        
        // COMPOSITION: Issue certificates for passed attendees
        foreach (var attendee in attendance.Where(a => a.Outcome == TrainingOutcome.PASS))
        {
            var certificateRequest = new CertificateRequest
            {
                WorkerId = attendee.WorkerId,
                CertificateType = "TrainingCertificate",
                Source = "TrainingSession",
                SourceId = sessionId.ToString(),
                CertificateNo = GenerateCertificateNumber(session, attendee.WorkerId),
                IssueDate = DateTime.UtcNow,
                ExpiryDate = CalculateExpiryDate(session.Program.ValidityDays),
                IssuedBy = session.Conductor.Name,
                Metadata = new Dictionary<string, object>
                {
                    ["ProgramId"] = session.ProgramId,
                    ["ProgramTitle"] = session.Program.Title,
                    ["ProviderId"] = session.ProviderId,
                    ["SessionId"] = sessionId,
                    ["Outcome"] = attendee.Outcome.ToString()
                }
            };
            
            // COMPOSITION: Use certificate manager
            var certificate = await _certificateManager.IssueAsync(certificateRequest);
            
            // COMPOSITION: Use notification composer
            await _notificationComposer.ComposeAndSendAsync("certificate.issued", new CertificateNotificationData
            {
                CertificateId = certificate.Id,
                WorkerId = attendee.WorkerId,
                CertificateType = certificate.CertificateType,
                CertificateNo = certificate.CertificateNo,
                ExpiryDate = certificate.ExpiryDate,
                ProgramTitle = session.Program.Title
            });
        }
    }
}
```

### Phase 5: Permission System Integration (Composition)

```csharp
// NEW: TrainingPermissions.cs (compose with existing auth)
public static class TrainingPermissions
{
    // Certificate permissions
    public const string IssueCertificates = "Certificates.Issue";
    public const string ReadCertificates = "Certificates.Read";
    public const string RevokeCertificates = "Certificates.Revoke";
    
    // Provider permissions (tenant-scoped)
    public const string CreateProviders = "TrainingProviders.Create.Tenant";
    public const string ReadProviders = "TrainingProviders.Read.Tenant";
    public const string UpdateProviders = "TrainingProviders.Update.Tenant";
    public const string DeleteProviders = "TrainingProviders.Delete.Tenant";
    
    // Session permissions
    public const string CreateSessions = "TrainingSessions.Create.Site";
    public const string UpdateSessions = "TrainingSessions.Update.Site";
    public const string FinalizeSessions = "TrainingSessions.Finalize.Site";
}

// Compose with existing authorization
[AuthorizePermission("Certificates", "Issue", "Site")]
public async Task<Certificate> IssueCertificateAsync(...)

[AuthorizePermission("TrainingProviders", "Create", "Tenant")]
public async Task<TrainingProvider> CreateTrainingProviderAsync(...)
```

### Phase 6: Database Migration Script

```sql
-- Migration script to clean up old training system

-- Step 1: Migrate existing data to new system
-- (This should be done carefully with data validation)

-- Step 2: Remove old training collections from Worker
-- This will be handled by updating the Worker model

-- Step 3: Drop old training alert system
-- DROP TABLE training_alerts; -- (if exists)

-- Step 4: Update existing training data to use new certificate system
-- (Convert WorkerTraining records to Certificate records)

-- Step 5: Add new tables for composition system
-- Certificates table (already created)
-- TrainingProviders with TenantId (updated)
-- Provider-Program authorization junction table
```

## Benefits of Migration

### 1. Clean Separation of Concerns
- Certificates are managed independently
- Training sessions focus on delivery
- Notifications are composed, not hardcoded

### 2. No More Circular Dependencies
- Worker model is clean
- Training system doesn't depend on Worker collections
- Services compose instead of inherit

### 3. Flexible Certificate Management
- Can issue any type of certificate (training, medical, safety)
- Not tied to specific training system
- Easy to extend for new certificate types

### 4. Company-Level Provider Management
- Providers are scoped to tenants/companies
- Clear permission boundaries
- Easy to manage provider authorizations

### 5. Better Testing
- Each component can be tested independently
- Mock services easily for unit tests
- No complex inheritance hierarchies

## Implementation Order

1. ✅ Create Certificate Manager (composition-based)
2. ✅ Create Notification Composer
3. ✅ Update Training Provider (tenant-scoped)
4. ⏳ Create Worker Training Composer
5. ⏳ Update Training Service to use composition
6. ⏳ Remove old dependencies from Worker model
7. ⏳ Update Mutation.cs to use new services
8. ⏳ Create database migration scripts
9. ⏳ Update frontend to use new APIs
10. ⏳ Remove old training tables and services

This migration ensures we build a clean, maintainable system without the baggage of the old training system dependencies.


