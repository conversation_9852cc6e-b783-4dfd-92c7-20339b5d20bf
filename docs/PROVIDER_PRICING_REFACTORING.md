# Provider Pricing Refactoring Summary

## Overview
This document describes the refactoring of the training provider pricing model from a provider-level field to a provider-program relationship field with multiple pricing strategies.

## Changes Made

### 1. Model Renaming and Enhancement
- **Renamed:** `ProviderProgramAccreditation` → `ProviderProgram`
  - File: `Shared/GraphQL/Models/Training/ProviderProgram.cs`
  - Rationale: Better reflects the dual purpose of accreditation and pricing

### 2. New Pricing Strategy Enum
- **Added:** `PricingStrategy` enum in `Shared/Enums/TrainingEnums.cs`
  - `PerPerson` - Charge per person trained
  - `PerHour` - Charge per hour of training
  - `FlatFee` - Fixed fee regardless of attendees or duration
  - `Subscription` - Recurring subscription model

### 3. New Fields in ProviderProgram Model
- **Added:** `PricingStrategy` - Enum to specify pricing model
- **Added:** `Charges` - Decimal field for pricing amount (moved from TrainingProvider)
- **Added:** `DurationHours` - Integer field for training program duration in hours

### 4. TrainingProvider Model Updates
- **Removed:** `Charges` field (moved to ProviderProgram)
- **Renamed navigation property:** `ProgramAccreditations` → `Programs`
- **Updated comment:** Clarified that the junction table now includes pricing and duration

### 5. TrainingProgram Model Updates
- **Renamed navigation property:** `ProviderAccreditations` → `Providers`
- **Updated comment:** Clarified that the relationship includes accreditation, pricing, and duration

### 6. Database Configuration Updates
- **Renamed:** `ProviderProgramAccreditationConfiguration` → `ProviderProgramConfiguration`
  - File: `GraphQLApi/Data/Configurations/ProviderProgramConfiguration.cs`
  - Table name: `ProviderProgramAccreditations` → `ProviderPrograms`
  - Added precision configuration for `Charges` field (18, 2)
  - Added string conversion for `PricingStrategy` enum
  - Updated navigation property names in relationships

- **Updated:** `TrainingProviderConfiguration`
  - Removed `Charges` field configuration

- **Updated:** `AppDbContext`
  - Changed DbSet from `ProviderProgramAccreditations` to `ProviderPrograms`

### 7. GraphQL Type Updates
- **Updated:** `TrainingProviderType` (`Shared/GraphQL/Types/Training/TrainingProviderType.cs`)
  - Removed `Charges` field from GraphQL schema

### 8. Input Type Updates
- **Updated:** `CreateTrainingProviderInput` (`GraphQLApi/GraphQL/Types/TrainingInputTypes.cs`)
  - Removed `Charges` field

- **Updated:** `UpdateTrainingProviderInput`
  - Removed `Charges` field

### 9. Service Interface Updates
- **Updated:** `ITrainingProviderManager` (`GraphQLApi/Services/Training/ITrainingProviderManager.cs`)
  - Removed `Charges` from `CreateProviderRequest`
  - Removed `Charges` from `UpdateProviderRequest`

## Migration Considerations

### Database Migration Required
This refactoring requires a database migration to:
1. Rename table `ProviderProgramAccreditations` to `ProviderPrograms`
2. Add `PricingStrategy` column (string) to `ProviderPrograms` table
3. Add `Charges` column (decimal(18,2)) to `ProviderPrograms` table
4. Add `DurationHours` column (int) to `ProviderPrograms` table
5. Remove `Charges` column from `TrainingProviders` table

### Data Migration Script
```sql
-- Step 1: Add new columns to ProviderProgramAccreditations
ALTER TABLE ProviderProgramAccreditations 
ADD PricingStrategy NVARCHAR(50) NOT NULL DEFAULT 'PerPerson',
    Charges DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    DurationHours INT NOT NULL DEFAULT 0;

-- Step 2: Migrate charges from TrainingProviders to ProviderProgramAccreditations
-- This requires business logic to determine how to distribute provider charges across programs
-- Example: Copy the provider's charge to all their programs
UPDATE ppa
SET ppa.Charges = tp.Charges
FROM ProviderProgramAccreditations ppa
INNER JOIN TrainingProviders tp ON tp.Id = ppa.ProviderId;

-- Step 3: Drop the Charges column from TrainingProviders
ALTER TABLE TrainingProviders DROP COLUMN Charges;

-- Step 4: Rename the table
EXEC sp_rename 'ProviderProgramAccreditations', 'ProviderPrograms';
```

## Benefits of This Refactoring

1. **Flexible Pricing:** Different providers can charge differently for the same program
2. **Multiple Pricing Models:** Supports various business models (per person, per hour, flat fee, subscription)
3. **Duration Tracking:** Each provider-program relationship now tracks program duration
4. **Better Data Model:** Pricing is tied to the specific provider-program relationship, not the provider entity
5. **Scalability:** Easier to add new pricing strategies in the future

## Breaking Changes

### API Changes
- `TrainingProvider` GraphQL type no longer includes `charges` field
- Creating/updating providers no longer accepts `charges` parameter
- Pricing must now be managed at the provider-program relationship level

### Code Impact
- Any code referencing `provider.Charges` needs to be updated
- Any code using `ProgramAccreditations` navigation property needs to use `Programs` instead
- Any code using `ProviderAccreditations` navigation property needs to use `Providers` instead

## Next Steps

1. **Create Database Migration:** Generate and test the EF Core migration
2. **Update Services:** Implement services to manage provider-program pricing
3. **Update GraphQL Schema:** Add mutations for managing provider-program pricing
4. **Update Frontend:** Update UI to support per-program pricing configuration
5. **Data Migration:** Run data migration script in production (with backup!)
6. **Documentation:** Update API documentation to reflect new pricing model

## Files Modified

### Models
- `Shared/GraphQL/Models/Training/ProviderProgram.cs` (renamed)
- `Shared/GraphQL/Models/Training/TrainingProvider.cs`
- `Shared/GraphQL/Models/Training/TrainingProgram.cs`
- `Shared/Enums/TrainingEnums.cs`

### Configurations
- `GraphQLApi/Data/Configurations/ProviderProgramConfiguration.cs` (renamed)
- `GraphQLApi/Data/Configurations/TrainingProviderConfiguration.cs`
- `GraphQLApi/Data/AppDbContext.cs`

### GraphQL
- `Shared/GraphQL/Types/Training/TrainingProviderType.cs`
- `GraphQLApi/GraphQL/Types/TrainingInputTypes.cs`

### Services
- `GraphQLApi/Services/Training/ITrainingProviderManager.cs`

## Testing Checklist

- [ ] EF Core migration generates correctly
- [ ] Database tables are renamed properly
- [ ] All navigation properties work correctly
- [ ] GraphQL queries for providers work without errors
- [ ] GraphQL queries for programs work without errors
- [ ] Creating providers works without charges field
- [ ] Updating providers works without charges field
- [ ] Can create provider-program relationships with pricing
- [ ] Can update provider-program pricing
- [ ] Can query provider-program pricing information
- [ ] Frontend displays pricing correctly per program
- [ ] Data migration script tested with sample data

## Notes

- The `Charges` field is now at the provider-program level, meaning each provider can have different charges for different programs
- The `DurationHours` field represents how long a provider typically delivers a specific training program
- The pricing strategy determines how to interpret the `Charges` field (per person, per hour, flat fee, or subscription)
- Documentation files still reference the old names (`ProviderProgramAccreditation`) - these are informational and don't affect code execution


