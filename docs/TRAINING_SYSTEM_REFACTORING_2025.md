# Training System Refactoring - September 2025

## Overview

This document describes the major refactoring of the training system implemented in September 2025 to better track provider certifications, program accreditations, and training enrollments.

## Key Changes

### 1. Provider Certification & Accreditation Tracking

**Previous Approach:**
- Provider certifications stored as <PERSON>SO<PERSON> in `TrainingProvider.Certifications` field
- Accreditation info stored as JSON in `TrainingProvider.AccreditationInfo` field
- Difficult to query and maintain

**New Approach:**
- **ProviderCertification** - Separate table for provider-level certifications (ISO, OSHA, etc.)
- **ProviderProgramAccreditation** - Junction table tracking which providers are authorized to deliver which programs

#### ProviderCertification Model
```csharp
public class ProviderCertification : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int ProviderId { get; set; }
    public string CertificationType { get; set; }      // e.g., "ISO9001", "OSHA"
    public string CertificateNumber { get; set; }
    public DateTime IssueDate { get; set; }
    public DateTime? ExpiryDate { get; set; }          // Track expiry
    public string? IssuingAuthority { get; set; }
    public int? CertificateFileId { get; set; }
    public bool Active { get; set; } = true;
    
    public virtual TrainingProvider Provider { get; set; } = null!;
    public virtual FileMetadata? CertificateFile { get; set; }
}
```

#### ProviderProgramAccreditation Model
```csharp
public class ProviderProgramAccreditation : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int ProviderId { get; set; }
    public int ProgramId { get; set; }
    public string? CertificateNumber { get; set; }     // Accreditation cert number
    public DateTime? IssueDate { get; set; }
    public DateTime? ExpiryDate { get; set; }          // Track when accreditation expires
    public string? CertifyingOrganization { get; set; }
    public string? Notes { get; set; }
    public bool Active { get; set; } = true;
    
    public virtual TrainingProvider Provider { get; set; } = null!;
    public virtual TrainingProgram Program { get; set; } = null!;
}
```

**Benefits:**
- ✅ Queryable: Can easily find providers with expiring certifications
- ✅ Relational: Proper foreign keys and navigation properties
- ✅ Auditable: Full audit trail for certification changes
- ✅ Flexible: Can track multiple certifications per provider
- ✅ Expiry Tracking: Automated alerts for expiring accreditations

### 2. Simplified Enrollment Status

**Previous Approach:**
```csharp
public enum EnrollmentStatus
{
    INVITED, CONFIRMED, IN_PROGRESS, 
    COMPLETED_PASS, COMPLETED_FAIL, 
    WITHDRAWN, NO_SHOW
}
```

**New Approach:**
```csharp
public enum EnrollmentStatus
{
    INVITED,        // Worker has been invited to the session
    CONFIRMED,      // Worker has confirmed attendance
    ATTENDED,       // Worker attended the session
    DID_NOT_ATTEND, // Worker did not attend (no-show)
    WITHDRAWN       // Worker withdrew from the session
}
```

**Rationale:**
- Binary attendance tracking (attended vs did not attend)
- Pass/fail outcomes tracked separately in certificates
- Simpler state machine
- Clearer semantics

### 3. Removed Attendees Collection from TrainingSession

**Previous Approach:**
- `TrainingSession.Attendees` - JSON collection of `TrainingAttendee` objects
- Duplicated data already in `TrainingEnrollment`
- Difficult to maintain consistency

**New Approach:**
- Use `TrainingEnrollment` as single source of truth for attendance
- Enrollment status indicates whether worker attended
- Certificates track pass/fail outcomes

**Migration Impact:**
- Removed `TrainingAttendee` class
- Removed `TrainingSession.Attendees` property
- Updated `TrainingService.AddAttendeesToSessionAsync()` to only create enrollments
- Updated `TrainingService.FinalizeSessionAsync()` to update enrollment status

### 4. Certificate Provider Tracking

**Previous Approach:**
```csharp
public class TrainingCertificate
{
    public int ProviderId { get; set; }  // Foreign key
    public virtual TrainingProvider Provider { get; set; }
}
```

**New Approach:**
```csharp
public class TrainingCertificate
{
    public string? ProviderName { get; set; }  // Store name, not FK
    public string? CertificateNo { get; set; } // Now nullable
}
```

**Rationale:**
- Certificates are historical records
- Provider details shouldn't change if provider is deleted/modified
- Simpler queries (no need to join Provider table)
- Certificate number may not always be available

## Database Migration

### Migration Name
`RefactorTrainingSystemAccreditationsAndEnrollments`

### Key Schema Changes

1. **New Tables:**
   - `ProviderCertifications`
   - `ProviderProgramAccreditations`

2. **Modified Tables:**
   - `TrainingProviders` - Removed JSON columns (Certifications, AccreditationInfo)
   - `TrainingCertificates` - Removed ProviderId FK, added ProviderName string
   - `TrainingSessions` - Removed Attendees JSON column

3. **Indexes Added:**
   - `IX_ProviderCertifications_ProviderId`
   - `IX_ProviderCertifications_CertificationType`
   - `IX_ProviderCertifications_ExpiryDate`
   - `IX_ProviderProgramAccreditations_ProviderId`
   - `IX_ProviderProgramAccreditations_ProgramId`
   - `IX_ProviderProgramAccreditations_ProviderId_ProgramId` (composite)
   - `IX_ProviderProgramAccreditations_ExpiryDate`

## Service Layer Changes

### TrainingService.cs

**Updated Methods:**

1. `AddAttendeesToSessionAsync()`
   - Removed attendee list creation
   - Only creates enrollment records
   - Simplified logic

2. `FinalizeSessionAsync()`
   - Updates enrollment status instead of attendee outcomes
   - Maps outcomes to ATTENDED/DID_NOT_ATTEND status
   - Issues certificates for passed workers

3. `IssueCertificateAsync()`
   - Sets `ProviderName` instead of `ProviderId`
   - Retrieves provider name from session

4. `GetWorkerCertificatesAsync()` & `GetExpiringCertificatesAsync()`
   - Removed `.Include(c => c.Provider)` (no longer exists)

### TrainingNotificationService.cs

**Updated Methods:**

1. `SendSessionCompletedNotificationAsync()`
   - Uses enrollment status instead of attendee outcomes
   - Updated notification messages for new status values

## GraphQL API Changes

### Type Updates

1. **TrainingSessionType** - Removed `Attendees` field
2. **TrainingCertificateType** - Replaced `ProviderId` with `ProviderName`

### New Types Needed (TODO)

1. **ProviderCertificationType**
2. **ProviderProgramAccreditationType**
3. **Input types for creating/updating certifications and accreditations**

## Testing Recommendations

### Unit Tests
- [ ] Test provider certification CRUD operations
- [ ] Test program accreditation CRUD operations
- [ ] Test enrollment status transitions
- [ ] Test certificate issuance with provider name

### Integration Tests
- [ ] Test session finalization workflow
- [ ] Test certificate queries without provider FK
- [ ] Test expiring certification queries
- [ ] Test provider-program authorization checks

### Migration Tests
- [ ] Verify data migration from JSON to relational tables
- [ ] Verify certificate provider data migration
- [ ] Verify enrollment status migration

## Breaking Changes

⚠️ **API Breaking Changes:**

1. `TrainingSession.Attendees` field removed from GraphQL schema
2. `TrainingCertificate.ProviderId` replaced with `ProviderName`
3. `TrainingCertificate.Provider` navigation removed
4. `EnrollmentStatus` enum values changed

⚠️ **Database Breaking Changes:**

1. `TrainingProviders.Certifications` column removed
2. `TrainingProviders.AccreditationInfo` column removed
3. `TrainingSessions.Attendees` column removed
4. `TrainingCertificates.ProviderId` column removed

## Rollback Plan

If issues arise:

1. **Before applying migration:**
   ```bash
   dotnet ef migrations remove --project GraphQLApi --context AppDbContext
   ```

2. **After applying migration:**
   ```bash
   dotnet ef database update <PreviousMigrationName> --project GraphQLApi --context AppDbContext
   ```

3. **Restore code:**
   ```bash
   git revert <commit-hash>
   ```

## Future Enhancements

1. **Automated Expiry Notifications**
   - Background job to check expiring provider certifications
   - Notify admins when accreditations are about to expire

2. **Provider Authorization Validation**
   - Validate provider is accredited before creating sessions
   - Warn when creating sessions with providers whose accreditation is expiring

3. **Certification Renewal Workflow**
   - UI for renewing provider certifications
   - Track renewal history

4. **Reporting**
   - Provider certification status reports
   - Program accreditation coverage reports
   - Training completion rates by provider

## References

- Original requirements: User message dated 25/09/25
- Migration file: `GraphQLApi/Migrations/*_RefactorTrainingSystemAccreditationsAndEnrollments.cs`
- Related models: `Shared/GraphQL/Models/Training/`
- Related services: `GraphQLApi/Services/TrainingService.cs`

