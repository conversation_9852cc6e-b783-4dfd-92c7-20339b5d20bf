# 📜 Certificate Handling: Internal vs External

## 🎯 Overview

The Training Management System handles **two types of certificates** with different workflows:

1. **Internal Certificates** - Generated from training sessions conducted within the system
2. **External Certificates** - Imported from training done outside the system

## 📊 Database Design for Both Types

### **TrainingCertificate Model**
```csharp
public class TrainingCertificate
{
    public int Id { get; set; }
    public int WorkerId { get; set; }
    public int ProgramId { get; set; }
    
    // 🔑 KEY DIFFERENTIATOR
    public int? SessionId { get; set; }        // NULL for external certs!
    
    public string? CertificateNo { get; set; }
    public string? ProviderName { get; set; }  // Stored as text for external
    public DateTime IssueDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public CertificateStatus Status { get; set; }
    
    // 🔑 ANOTHER DIFFERENTIATOR
    public CertificateType CertType { get; set; }  // Internal or External
    
    public string? FileUrl { get; set; }
    public int? CertificateFileId { get; set; }
    public string? Notes { get; set; }
    
    // Navigation
    public virtual TrainingSession? Session { get; set; }  // NULL for external!
    public virtual FileMetadata? CertificateFile { get; set; }
}
```

### **Key Fields Explained**

| Field | Internal Cert | External Cert | Purpose |
|-------|--------------|---------------|---------|
| **SessionId** | ✅ REQUIRED (links to session) | ❌ NULL (no system session) | Tracks which internal session issued it |
| **CertType** | `Internal` | `External` | Explicit type marker |
| **ProviderName** | Auto-filled from Session.Provider | Manually entered | Who issued the certificate |
| **CertificateNo** | Auto-generated OR uploaded | Manually entered | Certificate number |
| **CertificateFileId** | Optional (system-generated) | Usually required (scanned doc) | PDF/image of certificate |

---

## 🏗️ Workflow 1: Internal Certificates (From System Sessions)

### **Step-by-Step Process**

```
┌─────────────────────────────────────────────────────────────────┐
│ PHASE 1: Training Session Conducted                            │
└─────────────────────────────────────────────────────────────────┘

1. Training session is SCHEDULED
2. Workers enroll (TrainingEnrollments created with Status: REGISTERED)
3. Session happens (Status → IN_PROGRESS)
4. Session ends (Status → COMPLETED)

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 2: Session Finalization (Certificate Auto-Generation)    │
└─────────────────────────────────────────────────────────────────┘

5. Admin/Trainer calls FinalizeSessionAsync() with attendance data:
   
   Input:
   {
       SessionId: 123,
       Attendance: [
           { WorkerId: 101, Outcome: PASS, Notes: "Excellent" },
           { WorkerId: 102, Outcome: FAIL, Notes: "Needs retake" },
           { WorkerId: 103, Outcome: PASS, Notes: "Good" }
       ]
   }

6. For each worker in attendance:
   
   a) Update TrainingEnrollment:
      - Status → ATTENDED (if they showed up)
      - Outcome → PASS/FAIL/INCOMPLETE
      - CompletedAt → DateTime.UtcNow
   
   b) IF Outcome = PASS:
      
      🎯 AUTO-ISSUE CERTIFICATE:
      
      IssueCertificateAsync({
          SessionId: 123,
          WorkerId: 101,
          Outcome: PASS,
          CertificateNo: "FIRE-20251015-000101",  // Auto-generated
          CustomExpiryDate: null,                  // Use program default
          CertificateFile: null,                   // Optional
          Notes: "Excellent"
      })

7. Certificate Creation Logic:
   
   TrainingCertificate {
       WorkerId = 101,
       ProgramId = session.ProgramId,              // From session
       SessionId = 123,                            // ✅ Linked to session
       ProviderName = session.Provider.Name,       // ✅ From session
       CertificateNo = "FIRE-20251015-000101",    // Auto-generated
       IssueDate = DateTime.UtcNow,
       ExpiryDate = DateTime.UtcNow.AddDays(session.Program.ValidityDays),
       Status = ISSUED,
       CertType = Internal,                        // ✅ Marked as internal
       CertificateFileId = null                    // No upload needed
   }

8. Session marked as FINALIZED

9. Notification sent: "Certificate issued for Fire Safety Training"
```

### **Code Implementation**

```csharp
// From TrainingService.cs
public async Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance)
{
    var session = await context.TrainingSessions
        .Include(s => s.Program)
        .Include(s => s.Enrollments)
        .FirstOrDefaultAsync(s => s.Id == sessionId);

    if (session.Status != TrainingSessionStatus.COMPLETED)
        throw new InvalidOperationException("Cannot finalize non-completed session");

    foreach (var attendanceInput in attendance)
    {
        var enrollment = session.Enrollments.FirstOrDefault(e => e.WorkerId == attendanceInput.WorkerId);
        
        if (enrollment != null)
        {
            // Update enrollment
            enrollment.Status = attendanceInput.Outcome == PASS ? ATTENDED : DID_NOT_ATTEND;
            enrollment.CompletedAt = DateTime.UtcNow;
            enrollment.Outcome = attendanceInput.Outcome;
            
            // 🎯 AUTO-ISSUE CERTIFICATE IF PASSED
            if (attendanceInput.Outcome == TrainingOutcome.PASS)
            {
                await IssueCertificateAsync(new IssueCertificateInput
                {
                    SessionId = sessionId,
                    WorkerId = attendanceInput.WorkerId,
                    Outcome = attendanceInput.Outcome,
                    // Auto-generate cert number: "FIRE-20251015-000101"
                    CertificateNo = $"{session.Program.Code}-{DateTime.UtcNow:yyyyMMdd}-{attendanceInput.WorkerId:D6}",
                    Notes = attendanceInput.Notes
                });
            }
        }
    }

    session.Status = TrainingSessionStatus.FINALIZED;
    await context.SaveChangesAsync();
}

public async Task<TrainingCertificate> IssueCertificateAsync(IssueCertificateInput input)
{
    var session = await context.TrainingSessions
        .Include(s => s.Program)
        .Include(s => s.Provider)
        .FirstOrDefaultAsync(s => s.Id == input.SessionId);

    var expiryDate = input.CustomExpiryDate ?? 
        (session.Program.ValidityDays > 0 
            ? DateTime.UtcNow.AddDays(session.Program.ValidityDays)
            : DateTime.UtcNow.AddYears(1));

    var certificate = new TrainingCertificate
    {
        WorkerId = input.WorkerId,
        ProgramId = session.ProgramId,               // ✅ From session
        SessionId = input.SessionId,                 // ✅ Linked!
        ProviderName = session.Provider?.Name,       // ✅ From session
        CertificateNo = input.CertificateNo,
        IssueDate = DateTime.UtcNow,
        ExpiryDate = expiryDate,
        Status = CertificateStatus.ISSUED,
        CertType = CertificateType.Internal,         // ✅ Internal
        Notes = input.Notes
    };

    // Optional: Handle certificate file upload
    if (input.CertificateFile != null)
    {
        var metadata = await _minioService.UploadFileAsync(/* ... */);
        certificate.CertificateFile = metadata;
    }

    context.TrainingCertificates.Add(certificate);
    await context.SaveChangesAsync();

    return certificate;
}
```

### **GraphQL Mutation Example**

```graphql
mutation FinalizeTrainingSession {
  finalizeSession(input: {
    sessionId: 123
    attendance: [
      { workerId: 101, outcome: PASS, notes: "Excellent performance" }
      { workerId: 102, outcome: FAIL, notes: "Needs retake" }
      { workerId: 103, outcome: PASS, notes: "Good" }
    ]
  }) {
    id
    status
    enrollments {
      worker { name }
      outcome
      status
    }
  }
}

# Response includes auto-generated certificates for workers 101 and 103
```

---

## 🌐 Workflow 2: External Certificates (Imported from Outside)

### **Current Implementation Status** ⚠️

**PROBLEM:** There is **NO dedicated mutation for external certificates** in the current code!

The `IssueCertificateAsync` mutation requires a `SessionId`, which doesn't exist for external certs.

### **How External Certificates SHOULD Work**

```
┌─────────────────────────────────────────────────────────────────┐
│ PHASE 1: Worker Brings External Certificate                    │
└─────────────────────────────────────────────────────────────────┘

Worker completed training outside the system:
- Provider: "External Safety Institute"
- Program: "Fire Safety Training" (must match a program in system)
- Certificate Number: "ESI-2024-12345"
- Issue Date: 2024-06-15
- Expiry Date: 2025-06-15
- Physical/scanned certificate document

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 2: Admin Imports Certificate (MISSING MUTATION!)         │
└─────────────────────────────────────────────────────────────────┘

Admin should be able to call:

mutation ImportExternalCertificate {
  importExternalCertificate(input: {
    workerId: 101
    programId: 5              # Must match existing program in system
    providerName: "External Safety Institute"  # Free text
    certificateNo: "ESI-2024-12345"
    issueDate: "2024-06-15"
    expiryDate: "2025-06-15"   # OR auto-calc from program validity
    certificateFile: <file>    # REQUIRED - scanned PDF
    certType: EXTERNAL
    notes: "Verified with provider"
  }) {
    id
    certificateNo
    status
    expiryDate
  }
}

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 3: Certificate Stored & Validated                        │
└─────────────────────────────────────────────────────────────────┘

TrainingCertificate {
    WorkerId = 101,
    ProgramId = 5,                          // Fire Safety (system program)
    SessionId = NULL,                       // ❌ No session (external!)
    ProviderName = "External Safety Institute",  // Free text
    CertificateNo = "ESI-2024-12345",
    IssueDate = 2024-06-15,
    ExpiryDate = 2025-06-15,
    Status = ISSUED,
    CertType = External,                    // ✅ Marked as external
    CertificateFileId = 456,                // ✅ Uploaded document
    Notes = "Verified with provider"
}

The certificate is now in the system and counts toward:
- Worker eligibility checks
- Trade requirements
- Expiry monitoring
- Compliance reporting
```

### **Required Implementation (MISSING)**

```csharp
// Should be added to ITrainingService.cs
public interface ITrainingService
{
    // ... existing methods ...
    
    Task<TrainingCertificate> ImportExternalCertificateAsync(ImportExternalCertificateInput input);
}

// Should be added to TrainingService.cs
public async Task<TrainingCertificate> ImportExternalCertificateAsync(ImportExternalCertificateInput input)
{
    await using var context = await _contextFactory.CreateDbContextAsync();

    // Validate program exists
    var program = await context.TrainingPrograms
        .FirstOrDefaultAsync(p => p.Id == input.ProgramId) 
        ?? throw new ArgumentException($"Program with ID {input.ProgramId} not found");

    // Validate worker exists
    var worker = await context.Workers
        .FirstOrDefaultAsync(w => w.Id == input.WorkerId) 
        ?? throw new ArgumentException($"Worker with ID {input.WorkerId} not found");

    // Calculate expiry if not provided
    var expiryDate = input.ExpiryDate ?? DateTime.Now.AddDays(program.ValidityDays);

    // Upload certificate document (REQUIRED for external)
    if (input.CertificateFile == null)
        throw new ArgumentException("Certificate file is required for external certificates");

    FileMetadata? certificateMetadata = null;
    using (var fileStream = input.CertificateFile.OpenReadStream())
    {
        certificateMetadata = await _minioService.UploadFileAsync(
            fileStream,
            input.CertificateFile.Name,
            Shared.Constants.FileStorageConstants.BucketNames.DOCS,
            input.CertificateFile.ContentType ?? "application/pdf",
            $"External certificate for {worker.Name}",
            $"external_certificates/{worker.Id}/{program.Code}",
            false,
            null);
    }

    var certificate = new TrainingCertificate
    {
        WorkerId = input.WorkerId,
        ProgramId = input.ProgramId,
        SessionId = null,                           // ✅ NULL for external
        ProviderName = input.ProviderName,          // ✅ Free text
        CertificateNo = input.CertificateNo,
        IssueDate = input.IssueDate,
        ExpiryDate = expiryDate,
        Status = CertificateStatus.ISSUED,
        CertType = CertificateType.External,        // ✅ External
        CertificateFile = certificateMetadata,
        CertificateFileId = certificateMetadata.Id,
        Notes = input.Notes
    };

    context.TrainingCertificates.Add(certificate);
    await context.SaveChangesAsync();

    return certificate;
}

// Input type
public class ImportExternalCertificateInput
{
    public int WorkerId { get; set; }
    public int ProgramId { get; set; }
    public string ProviderName { get; set; } = string.Empty;  // Free text
    public string CertificateNo { get; set; } = string.Empty;
    public DateTime IssueDate { get; set; }
    public DateTime? ExpiryDate { get; set; }  // Nullable, can auto-calc
    public IFile CertificateFile { get; set; } = null!;  // REQUIRED
    public string? Notes { get; set; }
}
```

---

## 🔍 Key Differences Summary

| Aspect | Internal Certificate | External Certificate |
|--------|---------------------|---------------------|
| **SessionId** | ✅ Required (links to TrainingSession) | ❌ NULL (no system session) |
| **CertType** | `Internal` | `External` |
| **How Created** | Auto-generated during `FinalizeSessionAsync()` | ⚠️ Manual import (MISSING MUTATION) |
| **ProviderName** | Auto-filled from `Session.Provider.Name` | Manually entered (free text) |
| **CertificateNo** | Auto-generated pattern: `{Code}-{Date}-{WorkerId}` | Manually entered from physical cert |
| **IssueDate** | `DateTime.UtcNow` (when finalized) | Manually entered (actual issue date) |
| **ExpiryDate** | Auto-calculated: `IssueDate + Program.ValidityDays` | Can be manually set or auto-calculated |
| **Certificate File** | Optional (can be uploaded later) | ⚠️ Should be REQUIRED (scan of cert) |
| **Enrollment Link** | ✅ Links to TrainingEnrollment record | ❌ No enrollment (external) |
| **Outcome Tracking** | ✅ PASS/FAIL tracked in enrollment | ❌ Assumed PASS if imported |
| **Attendance** | ✅ Tracked in TrainingEnrollment | ❌ Not applicable |

---

## 🔄 How Both Types Are Used Identically

**Once in the system, both types work the same for:**

### **1. Worker Eligibility Checks**
```sql
-- Check if worker has required cert (internal OR external)
SELECT * FROM TrainingCertificates
WHERE WorkerId = 101
  AND ProgramId = 5  -- Fire Safety
  AND Status IN ('VALID', 'ISSUED')
  AND IsDeleted = 0
-- Doesn't matter if SessionId is NULL or not!
```

### **2. Trade Requirements Validation**
```csharp
// System doesn't care if cert is internal or external
var hasRequiredCerts = await context.TrainingCertificates
    .Where(c => c.WorkerId == workerId 
             && requiredProgramIds.Contains(c.ProgramId)
             && c.Status == CertificateStatus.VALID
             && !c.IsDeleted)
    .CountAsync() == requiredProgramIds.Count;
```

### **3. Expiry Monitoring**
```csharp
// Background service checks ALL certificates, internal and external
var expiringCerts = await context.TrainingCertificates
    .Where(c => c.ExpiryDate <= DateTime.UtcNow.AddDays(30)
             && c.Status == CertificateStatus.VALID
             && !c.IsDeleted)
    .ToListAsync();

// Sends notifications for both types
foreach (var cert in expiringCerts)
{
    cert.Status = CertificateStatus.EXPIRING_SOON;
    await SendExpiryNotification(cert);
}
```

### **4. Compliance Reporting**
```sql
-- Report shows both types
SELECT 
    w.Name AS WorkerName,
    tp.Title AS Program,
    tc.CertificateNo,
    tc.IssueDate,
    tc.ExpiryDate,
    tc.CertType,  -- Shows 'Internal' or 'External'
    CASE 
        WHEN tc.SessionId IS NULL THEN 'External Import'
        ELSE 'Internal Training'
    END AS Source
FROM TrainingCertificates tc
JOIN Workers w ON w.Id = tc.WorkerId
JOIN TrainingPrograms tp ON tp.Id = tc.ProgramId
WHERE tc.Status = 'VALID'
ORDER BY tc.ExpiryDate
```

---

## 📝 Querying & Filtering

### **GraphQL Query: Get Worker Certificates (Both Types)**

```graphql
query GetWorkerCertificates($workerId: Int!) {
  workerCertificates(workerId: $workerId) {
    id
    certificateNo
    issueDate
    expiryDate
    status
    certType          # Internal or External
    
    # Program info (always present)
    program {
      title
      code
    }
    
    # Session info (only for internal)
    session {
      id
      startDate
      provider {
        name
      }
    }
    
    # Provider name (text field for external)
    providerName
    
    # Certificate file (required for external, optional for internal)
    certificateFile {
      fileName
      filePath
    }
  }
}

# Response example:
{
  "workerCertificates": [
    {
      "certificateNo": "FIRE-20251015-000101",
      "certType": "Internal",
      "session": {                           # ✅ Has session
        "id": 123,
        "provider": { "name": "ABC Safety" }
      },
      "providerName": "ABC Safety"           # Auto-filled
    },
    {
      "certificateNo": "ESI-2024-12345",
      "certType": "External",
      "session": null,                       # ❌ No session
      "providerName": "External Safety Institute",  # Manual entry
      "certificateFile": {                   # ✅ Uploaded scan
        "fileName": "fire-cert-scan.pdf"
      }
    }
  ]
}
```

---

## ⚠️ Current System Limitations

### **1. No External Certificate Import Mutation** ❌
**Problem:** Can only create certs from internal sessions

**Solution Needed:**
- Add `ImportExternalCertificateAsync()` method
- Add GraphQL mutation `importExternalCertificate`
- Make certificate file upload REQUIRED for external

### **2. No Validation on Provider** ⚠️
**Problem:** For external certs, `ProviderName` is free text with no validation

**Current:** "External Saftey Institue" (typo!)
**Better:** Validate against a list or provider registry

### **3. No Audit Trail for External Certs** ⚠️
**Problem:** Can't track who imported the external certificate

**Solution:** Use proper `CreatedBy` field (currently just "System")

### **4. Program Must Exist in System** ✅ (Good!)
**Behavior:** External cert MUST map to an existing TrainingProgram

This is correct - ensures consistency in trade requirements and eligibility checks.

---

## ✅ Best Practices

### **For Internal Certificates:**
1. ✅ Always auto-generate from session finalization
2. ✅ Use consistent certificate number format
3. ✅ Link to session and provider
4. ✅ Calculate expiry from program validity days
5. ✅ Send notifications automatically

### **For External Certificates:**
1. ⚠️ **MUST** require certificate file upload (proof)
2. ⚠️ **SHOULD** validate provider name (avoid typos)
3. ⚠️ **SHOULD** require admin approval workflow
4. ✅ Map to existing programs only
5. ✅ Allow manual expiry date override (if different from program default)
6. ⚠️ **SHOULD** track who imported (audit trail)

---

## 🎯 Recommendations

### **Immediate Fixes:**
1. **Add `ImportExternalCertificateAsync` method and mutation**
2. **Make certificate file REQUIRED for external certs**
3. **Fix audit trail to use actual user (not "System")**

### **Future Enhancements:**
1. Add provider validation/suggestions for external certs
2. Add approval workflow for external certificate imports
3. Add certificate verification status (Verified, Pending, Rejected)
4. Add bulk import for external certificates (CSV upload)
5. Add certificate renewal workflow (for both types)
6. Add OCR to extract data from scanned certificates

---

## 📊 Summary Table

| Feature | Internal Cert | External Cert | Notes |
|---------|--------------|---------------|-------|
| Auto-generation | ✅ Yes | ❌ No | Internal created on session finalize |
| SessionId | ✅ Required | ❌ NULL | Key differentiator |
| Enrollment | ✅ Linked | ❌ None | Internal has attendance record |
| Provider | ✅ From DB | ⚠️ Free text | External needs validation |
| Cert File | ⚠️ Optional | ⚠️ Should be required | External should mandate upload |
| Expiry Calc | ✅ Auto | ✅ Auto or manual | Both use program validity |
| Status Tracking | ✅ Same | ✅ Same | Both use same lifecycle |
| Eligibility | ✅ Counts | ✅ Counts | Both valid for trade requirements |
| Notifications | ✅ Same | ✅ Same | Both monitored for expiry |

**Bottom Line:** The database design supports both types well, but the system is **missing the mutation to import external certificates**. Once imported, both types are treated equally for eligibility, expiry monitoring, and compliance.
