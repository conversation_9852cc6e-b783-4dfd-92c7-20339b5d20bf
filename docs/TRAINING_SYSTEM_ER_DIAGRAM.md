# Training System - Visual Entity Relationship Diagram

## 🎨 Complete ER Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         TRAINING MANAGEMENT SYSTEM                          │
│                            Database Relationships                           │
└─────────────────────────────────────────────────────────────────────────────┘

┌──────────────────────┐
│   TrainingPrograms   │  ◄──── MASTER CATALOG OF TRAINING COURSES
├──────────────────────┤
│ Id (PK)             │
│ Code (UNIQUE)       │  "FIRE-101", "H2S-BASIC"
│ Title               │  "Fire Safety Training"
│ Description         │
│ ValidityDays        │  365 (how long cert is valid)
│ CertificateType     │  TEMPORARY, MEDICAL, PERMANENT
│ Prerequisites (JSON)│  [1, 5, 7] = must complete these first
│ Active              │  true/false
└──────────────────────┘
         │
         │ (1)
         │
         ├──────────────────────────────────────────┐
         │                                          │
         ▼ (Many)                                   ▼ (Many)
┌──────────────────────┐                  ┌──────────────────────┐
│  TrainingSessions    │                  │ TrainingCertificates │
├──────────────────────┤                  ├──────────────────────┤
│ Id (PK)             │                  │ Id (PK)             │
│ ProgramId (FK) ────┼──────────────────│ ProgramId (FK)      │
│ ProviderId (FK)     │                  │ WorkerId (FK)       │
│ SiteId (FK)         │                  │ SessionId (FK)      │
│ Mode                │  ONLINE/ONSITE   │ CertificateNo       │  "FIRE-2025-001"
│ Location            │  "Room A"        │ IssueDate           │  
│ StartDate           │                  │ ExpiryDate          │  IssueDate + ValidityDays
│ EndDate             │                  │ Status              │  VALID, EXPIRING_SOON, EXPIRED
│ Status              │  SCHEDULED       │ CertType            │  Internal/External
│ Capacity            │  20              │ FileUrl             │
│ Conductor (JSON)    │  {WorkerId, Name}│ CertificateFileId   │
│ SessionPictureFileId│                  └──────────────────────┘
└──────────────────────┘                           ▲
         │                                         │
         │ (1)                                     │ (Many)
         │                                         │
         ▼ (Many)                                  │
┌──────────────────────┐                           │
│ TrainingEnrollments  │  ◄──── TRACKS ATTENDANCE  │
├──────────────────────┤                           │
│ Id (PK)             │                           │
│ SessionId (FK)      │                           │
│ WorkerId (FK) ──────┼───────────────────────────┤
│ Status              │  REGISTERED, ATTENDED     │
│ CompletedAt         │  DID_NOT_ATTEND           │
│ Outcome             │  PASS, FAIL, INCOMPLETE   │
│ Notes               │                           │
└──────────────────────┘                           │
         ▲                                         │
         │                                         │
         │ (Many)                                  │ (Many)
         │                                         │
         │                              ┌──────────────────────┐
         │                              │       Workers        │
         │                              ├──────────────────────┤
         └──────────────────────────────│ Id (PK)             │
                                        │ ❌ TenantId (MISSING)│
                                        │ Name                │
                                        │ Trade               │  "Electrician"
                                        │ Email               │
                                        └──────────────────────┘


┌──────────────────────┐
│  TrainingProviders   │  ◄──── WHO DELIVERS TRAINING
├──────────────────────┤
│ Id (PK)             │
│ ✅ TenantId (FK)     │  Company/Tenant scoped
│ Name                │  "ABC Safety Training"
│ Description         │
│ Contact             │
│ Type                │  Internal, External, Partner, Certified
│ Active              │
└──────────────────────┘
         │
         │ (1)
         │
         ├───────────────────────────────────────────┐
         │                                           │
         ▼ (Many)                                    ▼ (Many)
┌──────────────────────┐                   ┌──────────────────────┐
│ProviderCertifications│ ◄── GENERAL       │   ProviderPrograms   │ ◄── PROGRAM-SPECIFIC
├──────────────────────┤     CREDENTIALS    ├──────────────────────┤     ACCREDITATION
│ Id (PK)             │                    │ Id (PK)             │
│ ProviderId (FK)     │                    │ ProviderId (FK) ────┼────┐
│ CertificationType   │ "ISO 9001"         │ ProgramId (FK) ─────┼───┐│
│ CertificateNumber   │ "OSHA Trainer"     │                     │   ││
│ IssueDate           │                    │ CertificateNumber   │   ││
│ ExpiryDate          │                    │ IssueDate           │   ││
│ IssuingAuthority    │                    │ ExpiryDate          │   ││
│ CertificateFileId   │                    │ CertifyingOrg       │   ││
│ Active              │                    │ Active              │   ││
└──────────────────────┘                   │                     │   ││
                                           │ 💰 Pricing:         │   ││
                                           │ PricingStrategy     │   ││
                                           │ Charges             │   ││
                                           │ Duration            │   ││
                                           └──────────────────────┘   ││
                                                      │                ││
                                                      │ (Many)         ││
                                                      └────────────────┘│
                                                                        │
                                                                        │
                                                      ┌─────────────────┘
                                                      │
                                                      ▼
                                           ┌──────────────────────┐
                                           │  TrainingPrograms    │
                                           │  (shown above)       │
                                           └──────────────────────┘


┌──────────────────────┐         (Many)        ┌──────────────────────┐
│      Trades          │ ◄─────────────────────│  TradeRequirements   │
├──────────────────────┤                       ├──────────────────────┤
│ Id (PK)             │                       │ Id (PK)             │
│ ❌ TenantId (MISSING)│                       │ TradeId (FK)        │
│ Name                │  "Electrician"        │ ProgramId (FK) ─────┼──┐
└──────────────────────┘  "Welder"            │ Mandatory           │  │
                          "Scaffolder"        │ Notes               │  │
                                              └──────────────────────┘  │
                                                                        │
                                                      ┌─────────────────┘
                                                      │
                                                      ▼ (Many)
                                           ┌──────────────────────┐
                                           │  TrainingPrograms    │
                                           │  (shown above)       │
                                           └──────────────────────┘


┌──────────────────────┐
│       Sites          │  ◄──── TRAINING LOCATIONS
├──────────────────────┤
│ Id (PK)             │
│ ✅ TenantId (FK)     │
│ Name                │  "Site A", "Training Center"
│ Location            │
└──────────────────────┘
         ▲
         │
         │ (1)
         │
         │ (Many)
┌──────────────────────┐
│  TrainingSessions    │
│  (SiteId - nullable) │
└──────────────────────┘


┌──────────────────────┐
│    FileMetadata      │  ◄──── DOCUMENT STORAGE
├──────────────────────┤
│ Id (PK)             │
│ FileName            │
│ FilePath            │  MinIO/S3 storage
│ MimeType            │
└──────────────────────┘
         ▲
         │
         │ Used by:
         ├─── TrainingCertificates (CertificateFileId)
         ├─── TrainingSessions (SessionPictureFileId)
         └─── ProviderCertifications (CertificateFileId)
```

## 🔄 Data Flow: Complete Training Lifecycle

```
┌─────────────────────────────────────────────────────────────────┐
│ PHASE 1: SETUP (Admin)                                          │
└─────────────────────────────────────────────────────────────────┘

   1. Create TrainingProgram
      └─> Set ValidityDays, Prerequisites
   
   2. Register TrainingProvider
      ├─> Add ProviderCertifications (ISO 9001, OSHA, etc.)
      └─> Add ProviderPrograms (authorized for specific programs + pricing)
   
   3. Define TradeRequirements
      └─> Map: Electrician NEEDS Fire Safety + H2S + Electrical Safety

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 2: SCHEDULE                                               │
└─────────────────────────────────────────────────────────────────┘

   4. Create TrainingSession
      Program: "Fire Safety"
      Provider: "ABC Safety" (must have ProviderProgram record!)
      Date: Oct 15, 2025
      Capacity: 20
      Status: SCHEDULED
   
   5. Workers Enroll
      └─> TrainingEnrollment created (Status: REGISTERED)

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 3: CONDUCT                                                │
└─────────────────────────────────────────────────────────────────┘

   6. Training Day
      Session Status → IN_PROGRESS
   
   7. Mark Attendance
      TrainingEnrollment:
      ├─> Status: ATTENDED or DID_NOT_ATTEND
      └─> Outcome: PASS or FAIL or INCOMPLETE
   
   8. Complete Session
      Session Status → COMPLETED

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 4: CERTIFY                                                │
└─────────────────────────────────────────────────────────────────┘

   9. Issue Certificates (for PASS outcomes)
      TrainingCertificate:
      ├─> WorkerId: 101
      ├─> ProgramId: 1 (Fire Safety)
      ├─> IssueDate: 2025-10-15
      ├─> ExpiryDate: 2026-10-15 (IssueDate + ValidityDays)
      └─> Status: ISSUED
   
   10. Send Notification
       "Certificate issued for Fire Safety Training"

┌─────────────────────────────────────────────────────────────────┐
│ PHASE 5: MONITOR                                                │
└─────────────────────────────────────────────────────────────────┘

   11. Background Service (Daily)
       If ExpiryDate <= Today + 30 days:
       ├─> Status → EXPIRING_SOON
       └─> Send notification
       
       If ExpiryDate < Today:
       ├─> Status → EXPIRED
       └─> Worker may be ineligible for jobs
   
   12. Worker Eligibility Check
       When assigning to job:
       └─> Check Trade Requirements
           └─> All mandatory programs have VALID certificates?
```

## 🔍 Key Queries

### **Check Worker Eligibility for Trade**
```sql
-- Can Worker #101 work as Electrician (Trade #5)?
SELECT 
    tr.ProgramId,
    tp.Title AS RequiredProgram,
    tr.Mandatory,
    tc.Id AS HasCertificate,
    tc.Status AS CertStatus,
    tc.ExpiryDate
FROM TradeRequirements tr
INNER JOIN TrainingPrograms tp ON tp.Id = tr.ProgramId
LEFT JOIN TrainingCertificates tc 
    ON tc.ProgramId = tr.ProgramId 
    AND tc.WorkerId = 101
    AND tc.Status IN ('VALID', 'ISSUED')
WHERE tr.TradeId = 5  -- Electrician
    AND tr.Mandatory = 1
```

### **Find Expiring Certificates**
```sql
-- Certificates expiring in next 30 days
SELECT 
    w.Name AS WorkerName,
    tp.Title AS Program,
    tc.CertificateNo,
    tc.ExpiryDate,
    DATEDIFF(day, GETDATE(), tc.ExpiryDate) AS DaysRemaining
FROM TrainingCertificates tc
INNER JOIN Workers w ON w.Id = tc.WorkerId
INNER JOIN TrainingPrograms tp ON tp.Id = tc.ProgramId
WHERE tc.ExpiryDate BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
    AND tc.Status = 'VALID'
ORDER BY tc.ExpiryDate
```

### **Validate Provider Authorization**
```sql
-- Can Provider #1 deliver Program #5?
SELECT 
    pp.Id,
    pr.Name AS ProviderName,
    tp.Title AS ProgramTitle,
    pp.Active AS IsAuthorized,
    pp.ExpiryDate AS AccreditationExpiry,
    pp.Charges,
    pp.PricingStrategy,
    pp.Duration
FROM ProviderPrograms pp
INNER JOIN TrainingProviders pr ON pr.Id = pp.ProviderId
INNER JOIN TrainingPrograms tp ON tp.Id = pp.ProgramId
WHERE pp.ProviderId = 1 
    AND pp.ProgramId = 5
    AND pp.Active = 1
    AND (pp.ExpiryDate IS NULL OR pp.ExpiryDate > GETDATE())
```

### **Session Enrollment Status**
```sql
-- How many workers enrolled vs capacity?
SELECT 
    ts.Id AS SessionId,
    tp.Title AS Program,
    ts.StartDate,
    ts.Capacity,
    COUNT(te.Id) AS Enrolled,
    ts.Capacity - COUNT(te.Id) AS SpotsRemaining,
    SUM(CASE WHEN te.Status = 'ATTENDED' THEN 1 ELSE 0 END) AS Attended,
    SUM(CASE WHEN te.Outcome = 'PASS' THEN 1 ELSE 0 END) AS Passed
FROM TrainingSessions ts
INNER JOIN TrainingPrograms tp ON tp.Id = ts.ProgramId
LEFT JOIN TrainingEnrollments te ON te.SessionId = ts.Id
WHERE ts.Id = 1
GROUP BY ts.Id, tp.Title, ts.StartDate, ts.Capacity
```

## 📊 Important Business Rules

### **1. Provider Authorization Validation**
```
RULE: Provider can only conduct sessions for programs they're accredited for

CHECK: Before creating TrainingSession:
└─> Must exist: ProviderPrograms 
    WHERE ProviderId = X 
    AND ProgramId = Y 
    AND Active = 1
    AND (ExpiryDate IS NULL OR ExpiryDate > TODAY)
```

### **2. Prerequisite Enforcement**
```
RULE: Worker must complete prerequisites before enrolling

CHECK: Before creating TrainingEnrollment:
└─> Parse Program.Prerequisites JSON [1, 5, 7]
    └─> For each prerequisite program:
        Must exist: TrainingCertificates
        WHERE WorkerId = X
        AND ProgramId IN (1, 5, 7)
        AND Status = 'VALID'
```

### **3. Certificate Expiry Calculation**
```
RULE: Certificate expiry = IssueDate + Program.ValidityDays

WHEN: Creating TrainingCertificate after enrollment passes:
IssueDate = Session.EndDate (or enrollment.CompletedAt)
ExpiryDate = DATEADD(day, Program.ValidityDays, IssueDate)
```

### **4. Trade Eligibility**
```
RULE: Worker must have ALL mandatory training for trade

CHECK: When assigning worker to job requiring Trade X:
└─> Count mandatory programs for trade
    └─> Count valid certificates for those programs
        └─> If counts match → ELIGIBLE
            Else → INELIGIBLE
```

### **5. Session Capacity**
```
RULE: Cannot enroll more workers than capacity

CHECK: Before creating TrainingEnrollment:
Current_Enrollments = COUNT(TrainingEnrollments WHERE SessionId = X)
IF Current_Enrollments >= Session.Capacity THEN
    REJECT (or add to waiting list)
```

## 🎯 Summary

This training system provides:
- ✅ Complete training lifecycle management
- ✅ Provider accreditation tracking (2-level: general + program-specific)
- ✅ Flexible pricing per provider-program combination
- ✅ Attendance and outcome tracking
- ✅ Automatic certificate issuance and expiry monitoring
- ✅ Trade-based eligibility validation
- ✅ Prerequisite enforcement
- ✅ Capacity management

**Foundation is solid but needs tenant isolation fixes!**
