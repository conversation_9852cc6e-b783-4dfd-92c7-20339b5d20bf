# MinIO File Storage Integration Guide

## Overview

This document provides comprehensive guidance on the MinIO file storage integration implemented in the Workforce Management System. The integration provides a robust, scalable file storage solution with SQL metadata tracking, transaction-based consistency, and GraphQL API support.

## Features

- **Object Storage**: MinIO-based file storage with bucket organization
- **SQL Metadata**: Complete file metadata stored in SQL Server with audit trails
- **Transaction Consistency**: Atomic operations between storage and database
- **GraphQL Integration**: File upload/download mutations with automatic versioning
- **Stream Support**: Efficient streaming for large file operations
- **Health Monitoring**: Built-in health checks for MinIO connectivity
- **Automatic Bucket Management**: Startup bucket creation and validation
- **File Type Validation**: Configurable allowed file types and size limits

## Architecture

### Components

1. **MinioService**: Core service for file operations
2. **FileMetadata Entity**: SQL metadata storage with audit fields
3. **GraphQL Mutations**: API endpoints for file operations
4. **Health Checks**: Monitoring MinIO connectivity and bucket status
5. **PhotoService Integration**: Backward-compatible photo storage

### Bucket Structure

- `profile-picture`: Worker profile images
- `certification`: Training certificates and documents
- `signatures`: Digital signatures
- `temp`: Temporary files (auto-cleanup)
- `docs`: General documents and files

## Configuration

### appsettings.json

```json
{
  "MinIO": {
    "Endpoint": "localhost:9000",
    "AccessKey": "minioadmin",
    "SecretKey": "minioadmin",
    "UseSSL": false,
    "Region": "us-east-1",
    "Buckets": {
      "ProfilePicture": "profile-picture",
      "Certification": "certification",
      "Signatures": "signatures",
      "Temp": "temp",
      "Docs": "docs"
    },
    "Settings": {
      "MaxFileSize": 52428800,
      "DefaultExpiration": 7,
      "EnableVersioning": true,
      "AutoCreateBuckets": true
    }
  }
}
```

### Environment Variables (Production)

```bash
MinIO__AccessKey=your-production-access-key
MinIO__SecretKey=your-production-secret-key
MinIO__Endpoint=your-minio-server:9000
MinIO__UseSSL=true
```

## API Usage

### GraphQL Mutations

#### Upload File

```graphql
mutation UploadFile($input: FileUploadInput!) {
  uploadFile(input: $input) {
    success
    fileMetadata {
      id
      fileName
      contentType
      size
      bucketName
      objectKey
      createdAt
    }
    presignedUrl
    errorMessage
  }
}
```

**Variables:**
```json
{
  "input": {
    "file": "file-upload-object",
    "bucketName": "PROFILE_PICTURE",
    "description": "Worker profile picture",
    "folderPath": "workers/123",
    "isPublic": false,
    "expiresAt": null,
    "additionalMetadata": "{\"workerId\": 123}"
  }
}
```

#### Get Presigned URL

```graphql
mutation GetPresignedUrl($input: PresignedUrlInput!) {
  getPresignedUrl(input: $input) {
    success
    url
    expiresAt
    errorMessage
  }
}
```

#### Delete File

```graphql
mutation DeleteFile($fileId: Int!) {
  deleteFile(fileId: $fileId)
}
```

### C# Service Usage

```csharp
// Inject the service
private readonly IMinioService _minioService;

// Upload a file
var fileMetadata = await _minioService.UploadFileAsync(
    stream: fileStream,
    fileName: "document.pdf",
    bucketName: "docs",
    contentType: "application/pdf",
    description: "Important document",
    folderPath: "contracts/2024",
    isPublic: false,
    expiresAt: DateTime.UtcNow.AddDays(30));

// Get presigned URL for download
var downloadUrl = await _minioService.GetPresignedUrlAsync(
    fileMetadata, 
    expirationSeconds: 3600);

// Download file
var downloadStream = await _minioService.DownloadFileAsync(fileMetadata);

// Delete file
var success = await _minioService.DeleteFileAsync(
    fileMetadata.BucketName, 
    fileMetadata.ObjectKey);
```

## PhotoService Integration

The existing PhotoService has been extended to support MinIO as a storage backend:

```csharp
// Upload photo to MinIO
var photoUrl = await _photoService.UploadPhotoAsync(
    photoStream, 
    "worker_123.jpg", 
    PhotoStorageType.MinIO);

// Retrieve photo (automatically detects storage type)
var photoStream = await _photoService.GetPhotoAsync(photoUrl);

// Delete photo
await _photoService.DeletePhotoAsync(photoUrl);
```

## Health Monitoring

### Health Check Endpoint

```
GET /health
```

**Response:**
```json
{
  "status": "Healthy",
  "results": {
    "database": {
      "status": "Healthy"
    },
    "minio": {
      "status": "Healthy",
      "data": {
        "ConnectedBuckets": 5,
        "RequiredBuckets": 5,
        "ExistingBuckets": 5,
        "MissingBuckets": 0,
        "BasicOperationsTest": "Passed"
      }
    }
  }
}
```

## File Type Support

### Allowed File Types

- **Images**: JPEG, JPG, PNG, GIF, BMP, WEBP
- **Documents**: PDF
- **Microsoft Office**: DOC, DOCX, XLS, XLSX
- **Text**: CSV, TXT

### Size Limits

- Default maximum file size: 50MB
- Configurable via `MinIO:Settings:MaxFileSize`

## Security Considerations

1. **Access Control**: Use strong access keys in production
2. **SSL/TLS**: Enable SSL for production deployments
3. **Presigned URLs**: Limited-time access for secure file sharing
4. **File Validation**: Strict file type and size validation
5. **Audit Trail**: Complete audit logging for all file operations

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check MinIO server status and network connectivity
2. **Bucket Not Found**: Ensure AutoCreateBuckets is enabled or create manually
3. **Access Denied**: Verify access keys and bucket permissions
4. **File Too Large**: Check MaxFileSize configuration
5. **Invalid File Type**: Review AllowedFileType enum and validation logic

### Logging

Enable detailed logging for troubleshooting:

```json
{
  "Logging": {
    "LogLevel": {
      "GraphQLApi.Services.MinioService": "Debug",
      "GraphQLApi.HealthChecks.MinIOHealthCheck": "Debug"
    }
  }
}
```

## Performance Optimization

1. **Connection Pooling**: MinIO client uses connection pooling automatically
2. **Streaming**: Use streaming for large file operations
3. **Presigned URLs**: Offload download traffic from application server
4. **Bucket Organization**: Use folder paths for logical file organization
5. **Health Checks**: Monitor system health proactively

## Migration from Local Storage

To migrate existing local files to MinIO:

1. Update PhotoService usage to specify `PhotoStorageType.MinIO`
2. Use `TransferPhotoToHikvisionAsync` pattern for bulk migration
3. Update file URLs in database to MinIO format (`/minio/{fileId}`)
4. Verify file accessibility after migration

## Support and Maintenance

- Monitor health check endpoints regularly
- Review file storage usage and cleanup expired files
- Update access keys periodically
- Monitor MinIO server logs for performance issues
- Backup file metadata from SQL database regularly
