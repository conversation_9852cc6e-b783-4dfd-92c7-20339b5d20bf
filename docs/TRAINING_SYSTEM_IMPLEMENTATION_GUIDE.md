# Training System Implementation Guide

## Overview

The Training System is a comprehensive workforce training management solution built following the proven **Toolbox System** architectural patterns. It replaces training alerts with the existing notification system and provides complete training lifecycle management from program creation to certificate expiry.

> **📝 Recent Update (September 2025):** The training system was significantly refactored to improve provider certification tracking, program accreditation management, and enrollment handling. See [TRAINING_SYSTEM_REFACTORING_2025.md](./TRAINING_SYSTEM_REFACTORING_2025.md) for detailed migration information.

## 🎯 Core Objectives

✅ **Mirrors Toolbox Architecture**: Uses identical patterns for consistency and maintainability  
✅ **Notification Integration**: Replaces training alerts with the existing notification system  
✅ **Complete Lifecycle**: Covers programs, providers, sessions, attendance, and certificates  
✅ **Worker Eligibility**: Real-time trade eligibility based on current certifications  
✅ **Audit Trail**: Full audit capabilities like all other workforce systems  

## 📋 Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Core Models](#core-models)
4. [Services](#services)
5. [GraphQL API](#graphql-api)
6. [Notification System](#notification-system)
7. [Workflow Examples](#workflow-examples)
8. [Migration Guide](#migration-guide)
9. [Testing Guide](#testing-guide)
10. [Troubleshooting](#troubleshooting)

## System Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Training Programs] --> B[Training Sessions]
    B --> C[Enrollments]
    C --> D[Daily Attendance]
    D --> E[Session Completion]
    E --> F[Certificate Issuance]
    F --> G[Eligibility Tracking]
    G --> H[Notification System]
    
    I[Trade Requirements] --> G
    J[Training Providers] --> B
    
    style B fill:#e3f2fd
    style F fill:#fff3e0
    style H fill:#f3e5f5
```

### Component Structure

```
Training System/
├── Models (Shared/GraphQL/Models/Training/)
│   ├── TrainingProgram.cs         # Training programs with validity rules
│   ├── TrainingProvider.cs        # Authorized training providers
│   ├── ProviderCertification.cs   # Provider-level certifications (ISO, OSHA, etc.)
│   ├── ProviderProgramAccreditation.cs # Provider authorization per program
│   ├── TrainingSession.cs         # Training sessions (like Toolbox)
│   ├── TrainingEnrollment.cs      # Worker enrollments per session
│   ├── TrainingAttendance.cs      # Daily attendance tracking
│   ├── TrainingCertificate.cs     # Issued certificates with expiry
│   └── TradeRequirement.cs        # Trade-training requirements mapping
├── Services (GraphQLApi/Services/)
│   ├── ITrainingService.cs        # Main service interface
│   ├── TrainingService.cs         # Implementation (mirrors ToolboxService)
│   └── TrainingNotificationService.cs # Notification integration
├── GraphQL (GraphQLApi/GraphQL/)
│   ├── Types/TrainingInputTypes.cs # Input types for mutations
│   ├── Queries/TrainingQueries.cs  # Query resolvers
│   └── Mutations/TrainingMutations.cs # Mutation resolvers
└── Configuration (GraphQLApi/Data/Configurations/)
    ├── TrainingProgramConfiguration.cs
    ├── TrainingProviderConfiguration.cs
    ├── ProviderCertificationConfiguration.cs
    ├── ProviderProgramAccreditationConfiguration.cs
    ├── TrainingSessionConfiguration.cs
    ├── TrainingEnrollmentConfiguration.cs
    ├── TrainingAttendanceConfiguration.cs
    ├── TrainingCertificateConfiguration.cs
    └── TradeRequirementConfiguration.cs
```

## Database Schema

### Entity Relationship Diagram

```mermaid
erDiagram
    TrainingProgram ||--o{ TrainingSession : "has many"
    TrainingProvider ||--o{ TrainingSession : "conducts"
    TrainingProvider ||--o{ ProviderCertification : "has certifications"
    TrainingProvider ||--o{ ProviderProgramAccreditation : "accredited for"
    TrainingProgram ||--o{ ProviderProgramAccreditation : "accredits"
    TrainingSession ||--o{ TrainingEnrollment : "has enrollments"
    Worker ||--o{ TrainingEnrollment : "enrolls in"
    Worker ||--o{ TrainingCertificate : "receives"
    TrainingProgram ||--o{ TrainingCertificate : "issues"
    TrainingSession ||--o{ TrainingCertificate : "results in"
    Trade ||--o{ TradeRequirement : "requires"
    TrainingProgram ||--o{ TradeRequirement : "satisfies"
    Site ||--o{ TrainingSession : "hosts"
    
    TrainingProgram {
        int Id PK
        string Code UK
        string Title
        string Description
        int ValidityDays
        CertificateType CertificateType
        string Prerequisites "JSON array"
        bool Active
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        string DeletedBy
    }
    
    TrainingProvider {
        int Id PK
        string Name
        string Description
        string Contact
        decimal Charges
        bool Active
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        string DeletedBy
    }

    ProviderCertification {
        int Id PK
        int ProviderId FK
        string CertificationType
        string CertificateNumber
        DateTime IssueDate
        DateTime ExpiryDate "nullable"
        string IssuingAuthority "nullable"
        int CertificateFileId FK "nullable"
        bool Active
    }

    ProviderProgramAccreditation {
        int Id PK
        int ProviderId FK
        int ProgramId FK
        string CertificateNumber "nullable"
        DateTime IssueDate "nullable"
        DateTime ExpiryDate "nullable"
        string CertifyingOrganization "nullable"
        string Notes "nullable"
        bool Active
    }
    
    TrainingSession {
        int Id PK
        int ProgramId FK
        int ProviderId FK
        int SiteId FK "nullable"
        TrainingMode Mode
        string Location
        DateTime StartDate
        DateTime EndDate
        TrainingSessionStatus Status
        int Capacity
        string Notes "nullable"
        TrainingConductor Conductor "JSON"
        int SessionPictureFileId FK "nullable"
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        string DeletedBy
    }
    
    TrainingEnrollment {
        int Id PK
        int SessionId FK
        int WorkerId FK
        EnrollmentStatus Status "REGISTERED|ATTENDED|DID_NOT_ATTEND|WITHDRAWN"
        DateTime CompletedAt "nullable"
        TrainingOutcome Outcome "nullable, PENDING|PASS|FAIL|INCOMPLETE"
        string Notes "nullable"
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
    }
    
    TrainingCertificate {
        int Id PK
        int WorkerId FK
        int ProgramId FK
        string ProviderName "nullable - stored as text"
        int SessionId FK "nullable"
        string CertificateNo "nullable"
        DateTime IssueDate
        DateTime ExpiryDate
        CertificateStatus Status
        string FileUrl "nullable"
        int CertificateFileId FK "nullable"
        string Notes "nullable"
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        string DeletedBy
    }
    
    TradeRequirement {
        int Id PK
        int TradeId FK
        int ProgramId FK
        bool Mandatory
        string Notes "nullable"
        DateTime CreatedAt
        string CreatedBy
        DateTime UpdatedAt
        string UpdatedBy
        bool IsDeleted
        DateTime DeletedAt
        string DeletedBy
    }
```

### Key Database Features

1. **JSON Storage**: Conductor and Attendees stored as JSON (like Toolbox)
2. **File Integration**: Session pictures and certificate files via MinIO
3. **Soft Delete**: All entities support soft deletion with audit trail
4. **Indexes**: Optimized for common query patterns
5. **Constraints**: Unique constraints and foreign key relationships
6. **Audit Trail**: Complete audit fields on all entities

## Core Models

### 1. TrainingProgram
Defines what training is available, validity periods, and certificate types.

```csharp
public class TrainingProgram : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;           // e.g., "WH-001"
    public string Title { get; set; } = string.Empty;          // e.g., "Working at Height"
    public string Description { get; set; } = string.Empty;
    public int ValidityDays { get; set; }                      // Certificate validity
    public CertificateType CertificateType { get; set; }       // TEMPORARY, MEDICAL, PERMANENT
    public string? Prerequisites { get; set; }                 // JSON array of program IDs
    public bool Active { get; set; } = true;
    
    // Navigation Properties
    public ICollection<TrainingSession> Sessions { get; set; }
    public ICollection<TrainingCertificate> Certificates { get; set; }
    public ICollection<TradeRequirement> TradeRequirements { get; set; }
}
```

### 2. TrainingSession (Main Entity)
Represents a scheduled delivery of a training program.

```csharp
public class TrainingSession : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int ProgramId { get; set; }
    public int ProviderId { get; set; }
    public int? SiteId { get; set; }
    public TrainingMode Mode { get; set; }                     // ONLINE, ONSITE, HYBRID
    public string Location { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public TrainingSessionStatus Status { get; set; }          // DRAFT → SCHEDULED → IN_PROGRESS → COMPLETED → FINALIZED
    public int Capacity { get; set; }
    public string? Notes { get; set; }

    // JSON objects ()
    public TrainingConductor Conductor { get; set; } = null!;
    public ICollection<TrainingAttendee> Attendees { get; set; } = new List<TrainingAttendee>();

    // File integration
    public int? SessionPictureFileId { get; set; }
    public virtual FileMetadata? SessionPictureFile { get; set; }
}

public class TrainingConductor
{
    public int WorkerId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string SignatureFileId { get; set; } = string.Empty;
}

public class TrainingAttendee
{
    public int WorkerId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Designation { get; set; } = string.Empty;
    public string SignatureFileId { get; set; } = string.Empty;
    public TrainingOutcome Outcome { get; set; } = TrainingOutcome.PENDING;
    public string? Notes { get; set; }
}
```

### 3. TrainingCertificate
Represents issued certificates with validity tracking.

```csharp
public class TrainingCertificate : IAuditableEntity, ISoftDeletable
{
    public int Id { get; set; }
    public int WorkerId { get; set; }
    public int ProgramId { get; set; }
    public int ProviderId { get; set; }
    public int? SessionId { get; set; }                        // Link to issuing session
    public string CertificateNo { get; set; } = string.Empty;  // Unique certificate number
    public DateTime IssueDate { get; set; }
    public DateTime ExpiryDate { get; set; }
    public CertificateStatus Status { get; set; }              // ISSUED → VALID → EXPIRING_SOON → EXPIRED → REVOKED
    public string? FileUrl { get; set; }
    public int? CertificateFileId { get; set; }
    public string? Notes { get; set; }
}
```

## Services

### TrainingService (Main Service)

#### Key Methods

```csharp
// Session Management 
Task<TrainingSession> CreateSessionAsync(CreateTrainingSessionInput input);
Task AddAttendeesAsync(int sessionId, IEnumerable<int> workerIds);
Task UpdateSessionStatusAsync(int sessionId, TrainingSessionStatus status);
Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance);

// Certificate Management
Task<TrainingCertificate> IssueCertificateAsync(IssueCertificateInput input);
Task<IEnumerable<TrainingCertificate>> GetExpiringCertificatesAsync(int daysAhead = 30);

// Worker Eligibility
Task<WorkerEligibilityStatus> CheckWorkerEligibilityAsync(int workerId, int tradeId);

// Attendance Management (Simplified)
Task MarkAttendanceAsync(int enrollmentId, bool attended, string? notes = null);
Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsWithAttendanceAsync(int sessionId);

// Batch Enrollment
Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersByTradeAsync(int sessionId, int tradeId);
Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersBySiteAsync(int sessionId, Guid siteId);

// File Management
Task ClearSessionTempFolderAsync(int sessionId);
```

#### File Handling Pattern 

```csharp
private async Task<string> CopySignatureToTempAsync(int sourceFileId, DateTime sessionDate, int sessionId)
{
    // Get source file metadata
    var sourceFile = await context.FileMetadata.FirstOrDefaultAsync(f => f.Id == sourceFileId);
    if (sourceFile == null) return string.Empty;

    // Create temp folder path for the training session
    var tempFolderPath = $"training_session_{sessionId}_{sessionDate:yyyyMMdd}/signatures";

    // Upload to temp bucket with 30-day expiry
    var tempFileMetadata = await _minioService.UploadFileAsync(
        sourceStream,
        tempFileName,
        Shared.Constants.FileStorageConstants.BucketNames.TEMP,
        sourceFile.ContentType,
        $"Temporary copy of signature for training session {sessionDate:yyyyMMdd}",
        tempFolderPath,
        false, // Not public
        DateTime.UtcNow.AddDays(30) // Expire in 30 days
    );

    return tempFileMetadata.Id.ToString();
}
```

### TrainingNotificationService (Replaces Training Alerts)

Integrates with the existing notification system:

#### Key Notification Types

```csharp
// Certificate expiry notifications (replaces training alerts)
public async Task SendCertificateExpiryNotificationsAsync()
{
    var expiringCertificates = await _trainingService.GetExpiringCertificatesAsync(30);
    
    foreach (var cert in expiringCertificates)
    {
        var notificationEvent = new NotificationEvent
        {
            Type = "training.certificate.expiring",
            Title = "Training Certificate Expiring",
            Message = $"Your {cert.Program.Title} certificate expires in {daysUntilExpiry} days",
            Priority = daysUntilExpiry <= 7 ? NotificationPriority.High : NotificationPriority.Medium,
            Entity = "TrainingCertificate",
            EntityId = cert.Id.ToString(),
            ActionUrl = $"/training/certificates/{cert.Id}",
            ActionLabel = "Renew Certificate"
        };

        await _notificationService.PublishToUserAsync(notificationEvent, cert.WorkerId);
    }
}

// Session invitations
public async Task SendSessionInvitationAsync(int sessionId, int workerId);

// Session reminders
public async Task SendSessionReminderAsync(int sessionId);

// Certificate issued notifications
public async Task SendCertificateIssuedNotificationAsync(int certificateId);

// Trade eligibility status
public async Task SendTradeEligibilityNotificationAsync(int workerId, int tradeId, WorkerEligibilityStatus status);
```

## GraphQL API

### Queries

```graphql
# Get training sessions
query GetTrainingSessions {
  allTrainingSessions {
    id
    program { title }
    provider { name }
    status
    startDate
    attendees {
      workerId
      name
      outcome
    }
  }
}

# Get worker certificates
query GetWorkerCertificates($workerId: Int!) {
  workerCertificates(workerId: $workerId) {
    id
    program { title }
    certificateNo
    issueDate
    expiryDate
    status
    daysUntilExpiry
    isExpired
    isExpiringSoon
  }
}

# Check worker eligibility
query CheckWorkerEligibility($workerId: Int!, $tradeId: Int!) {
  checkWorkerEligibility(input: { workerId: $workerId, tradeId: $tradeId })
}
```

### Mutations

```graphql
# Create training session
mutation CreateTrainingSession($input: CreateTrainingSessionInput!) {
  createTrainingSession(input: $input) {
    id
    program { title }
    status
    conductor {
      name
      signatureFileId
    }
  }
}

# Add attendees to session
mutation AddTrainingAttendees($input: AddTrainingAttendeesInput!) {
  addTrainingAttendees(input: $input) {
    id
    attendees {
      workerId
      name
      designation
    }
  }
}

# Finalize session with outcomes
mutation FinalizeTrainingSession($input: FinalizeTrainingSessionInput!) {
  finalizeTrainingSession(input: $input) {
    id
    status
    attendees {
      workerId
      outcome
      notes
    }
  }
}

# Issue certificate
mutation IssueCertificate($input: IssueCertificateInput!) {
  issueCertificate(input: $input) {
    id
    certificateNo
    issueDate
    expiryDate
    status
  }
}
```

## Notification System

### Notification Types

| Type | Purpose | Priority | Trigger |
|------|---------|----------|---------|
| `training.certificate.expiring` | Certificate expiry warning | High (≤7 days), Medium (≤30 days) | Daily batch job |
| `training.certificate.expired` | Certificate has expired | High | Daily batch job |
| `training.certificate.issued` | New certificate issued | Low | Certificate issuance |
| `training.session.invitation` | Training session invitation | Medium | Worker enrollment |
| `training.session.reminder` | Session reminder | Medium | Day before session |
| `training.session.completed` | Session results | High (fail), Medium (pass) | Session finalization |
| `training.trade.eligibility` | Eligibility status change | High (not eligible), Medium (others) | Eligibility check |

### Notification Metadata

All training notifications include rich metadata for frontend handling:

```json
{
  "CertificateId": 123,
  "WorkerId": 456,
  "ProgramId": 789,
  "ProgramTitle": "Working at Height",
  "ExpiryDate": "2024-12-31T00:00:00Z",
  "DaysUntilExpiry": 15
}
```

## Workflow Examples

### 1. Complete Training Session Workflow 

```mermaid
sequenceDiagram
    participant Admin as Training Admin
    participant System as Training System
    participant Workers as Workers
    participant Notifications as Notification Service
    
    Admin->>System: Create Training Session
    System->>System: Set status to DRAFT
    
    Admin->>System: Add Attendees
    System->>System: Set status to SCHEDULED
    System->>Notifications: Send session invitations
    Notifications->>Workers: Invitation notifications
    
    Admin->>System: Start Session
    System->>System: Set status to IN_PROGRESS
    
    Admin->>System: Mark Worker Attendance
    System->>System: Update enrollment status (ATTENDED/DID_NOT_ATTEND)
    
    Admin->>System: Complete Session
    System->>System: Set status to COMPLETED
    
    Admin->>System: Finalize with Outcomes
    System->>System: Set attendee outcomes
    System->>System: Issue certificates for PASS outcomes
    System->>System: Set status to FINALIZED
    System->>Notifications: Send completion notifications
    System->>Notifications: Send certificate issued notifications
    Notifications->>Workers: Results and certificates
```

### 2. Simplified Enrollment and Attendance System

The training system implements **streamlined enrollment-based attendance tracking**:

#### Single-Level Tracking (`TrainingEnrollment`)
- **Registration**: Workers are registered for training sessions
- **Attendance Status**: Simple attended/did not attend after training completion
- **Batch Registration**: Support for registering by trade or site
- **Notes Support**: Record reasons for absence or special circumstances

#### Enrollment Status Flow
- **Status progression**: `REGISTERED` → `ATTENDED`/`DID_NOT_ATTEND`/`WITHDRAWN`

```mermaid
flowchart TD
    A[Worker Registration] --> B[REGISTERED Status]
    B --> C[Training Session Occurs]
    C --> D[Mark Attendance]
    D --> E{Attended?}
    E -->|Yes| F[ATTENDED Status]
    E -->|No| G[DID_NOT_ATTEND Status]
    F --> H[Session Finalization]
    G --> H
    H --> I{Training Outcome}
    I -->|Pass| J[Certificate Issued]
    I -->|Fail| K[No Certificate]
    I -->|Incomplete| L[Remedial Training]
```

#### Batch Registration Support

```mermaid
flowchart TD
    A[Training Session Created] --> B{Registration Method}
    B -->|Individual| C[Single Worker Registration]
    B -->|By Trade| D[Batch Register by Trade]
    B -->|By Site| E[Batch Register by Site]
    C --> F[Workers Registered]
    D --> F
    E --> F
    F --> G[Training Conducted]
    G --> H[Mark Attendance]
```

#### GraphQL Operations

```graphql
# Individual registration
mutation EnrollWorker($sessionId: Int!, $workerId: Int!) {
  enrollWorker(sessionId: $sessionId, workerId: $workerId) {
    id
    status
    worker { name }
  }
}

# Batch registration by trade
mutation BatchEnrollByTrade($input: BatchEnrollByTradeInput!) {
  batchEnrollWorkersByTrade(input: $input) {
    id
    status
    worker { name, trade { name } }
  }
}

# Batch registration by site
mutation BatchEnrollBySite($input: BatchEnrollBySiteInput!) {
  batchEnrollWorkersBySite(input: $input) {
    id
    status
    worker { name }
  }
}

# Mark attendance
mutation MarkAttendance($input: MarkTrainingAttendanceInput!) {
  markTrainingAttendance(input: $input)
}

# Get session enrollments with attendance
query GetSessionEnrollments($sessionId: Int!) {
  getSessionEnrollmentsWithAttendance(sessionId: $sessionId) {
    id
    status
    completedAt
    outcome
    notes
    worker {
      id
      name
      trade { name }
    }
  }
}
```

### 3. Certificate Expiry Management Workflow

```mermaid
sequenceDiagram
    participant BG as Background Job
    participant System as Training System
    participant Notifications as Notification Service
    participant Workers as Workers
    participant Supervisors as Supervisors
    
    BG->>System: Check expiring certificates (30 days)
    System->>BG: Return expiring certificates
    BG->>Notifications: Send expiry warnings
    Notifications->>Workers: Certificate expiry notifications
    
    BG->>System: Check expired certificates (daily)
    System->>BG: Return expired certificates
    BG->>System: Update certificate status to EXPIRED
    BG->>Notifications: Send expired notifications
    Notifications->>Workers: Certificate expired notifications
    Notifications->>Supervisors: Worker ineligibility alerts
    
    BG->>System: Update worker eligibility status
    System->>Notifications: Send eligibility notifications
    Notifications->>Workers: Eligibility status updates
```

### 3. Worker Eligibility Check Workflow

```mermaid
flowchart TD
    A[Worker assigned to task] --> B[Check required training for trade]
    B --> C[Get worker's current certificates]
    C --> D{All required certificates valid?}
    D -->|Yes| E[ELIGIBLE]
    D -->|No| F{Any certificates expiring soon?}
    F -->|Yes| G[ELIGIBLE_WITHIN_GRACE]
    F -->|No| H[NOT_ELIGIBLE]
    
    E --> I[Allow work assignment]
    G --> J[Allow with warning]
    H --> K[Block work assignment]
    
    G --> L[Send renewal reminder]
    H --> M[Send training requirement notification]
    
    style E fill:#c8e6c9
    style G fill:#fff3e0
    style H fill:#ffcdd2
```

## Migration Guide

### From Existing Training System

#### 1. Data Migration Strategy

```sql
-- Step 1: Create new training tables
-- (Run Entity Framework migrations)

-- Step 2: Migrate training programs
INSERT INTO TrainingPrograms (Code, Title, Description, ValidityDays, CertificateType, Active, CreatedAt, CreatedBy)
SELECT 
    CONCAT('LEGACY-', Id),
    Name,
    Description,
    365, -- Default validity
    'PERMANENT',
    Active,
    GETUTCDATE(),
    'MIGRATION'
FROM Trainings;

-- Step 3: Migrate worker training records to certificates
INSERT INTO TrainingCertificates (WorkerId, ProgramId, ProviderId, CertificateNo, IssueDate, ExpiryDate, Status, CreatedAt, CreatedBy)
SELECT 
    wt.WorkerId,
    tp.Id, -- New program ID
    1, -- Default provider
    CONCAT('MIGRATED-', wt.Id),
    wt.CompletionDate,
    DATEADD(year, 1, wt.CompletionDate),
    CASE WHEN DATEADD(year, 1, wt.CompletionDate) > GETUTCDATE() THEN 'VALID' ELSE 'EXPIRED' END,
    GETUTCDATE(),
    'MIGRATION'
FROM WorkerTrainings wt
JOIN TrainingPrograms tp ON tp.Code = CONCAT('LEGACY-', wt.TrainingId);
```

#### 2. Notification Migration

```csharp
// Replace training alert background job
public class LegacyTrainingAlertMigrationService
{
    public async Task MigrateTrainingAlertsToNotificationsAsync()
    {
        // Get existing training alerts
        var alerts = await GetLegacyTrainingAlertsAsync();
        
        foreach (var alert in alerts)
        {
            // Convert to notification
            var notificationEvent = new NotificationEvent
            {
                Type = "training.certificate.expiring",
                Title = "Training Certificate Expiring",
                Message = alert.Message,
                Priority = NotificationPriority.Medium,
                Entity = "TrainingCertificate",
                EntityId = alert.RecordId.ToString()
            };
            
            await _notificationService.PublishToUserAsync(notificationEvent, alert.WorkerId);
        }
        
        // Disable legacy alert system
        await DisableLegacyTrainingAlertsAsync();
    }
}
```

#### 3. Progressive Migration Steps

1. **Phase 1**: Deploy new training system alongside existing system
2. **Phase 2**: Migrate historical data to new tables
3. **Phase 3**: Update frontend to use new GraphQL APIs
4. **Phase 4**: Switch notification system from alerts to notifications
5. **Phase 5**: Remove legacy training tables and services

### Breaking Changes

⚠️ **Important**: This is a complete replacement of the existing training system. The old training tables and services should be considered deprecated.

#### What's Replaced:
- `Trainings` table → `TrainingPrograms` table
- `WorkerTrainings` table → `TrainingCertificates` table
- `training_alerts` table → Notification system integration
- Old training services → New `TrainingService`

#### Migration Checklist:
- [ ] Deploy new training system
- [ ] Run data migration scripts
- [ ] Update frontend components
- [ ] Switch to new GraphQL APIs
- [ ] Test notification integration
- [ ] Remove legacy code

## Testing Guide

### Unit Tests

```csharp
[TestClass]
public class TrainingServiceTests
{
    [TestMethod]
    public async Task CreateSessionAsync_Should_CreateSessionWithCorrectStatus()
    {
        // Arrange
        var input = new CreateTrainingSessionInput
        {
            ProgramId = 1,
            ProviderId = 1,
            ConductorId = 1,
            // ... other properties
        };

        // Act
        var session = await _trainingService.CreateSessionAsync(input);

        // Assert
        Assert.AreEqual(TrainingSessionStatus.DRAFT, session.Status);
        Assert.IsNotNull(session.Conductor);
    }

    [TestMethod]
    public async Task AddAttendeesAsync_Should_UpdateStatusToScheduled()
    {
        // Arrange
        var sessionId = 1;
        var workerIds = new[] { 1, 2, 3 };

        // Act
        await _trainingService.AddAttendeesAsync(sessionId, workerIds);

        // Assert
        var session = await _trainingService.GetSessionByIdAsync(sessionId);
        Assert.AreEqual(TrainingSessionStatus.SCHEDULED, session.Status);
        Assert.AreEqual(3, session.Attendees.Count);
    }
}
```

### Integration Tests

```csharp
[TestClass]
public class TrainingSystemIntegrationTests
{
    [TestMethod]
    public async Task CompleteTrainingWorkflow_Should_IssueValidCertificate()
    {
        // Arrange - Create session
        var session = await CreateTestSessionAsync();
        
        // Act - Complete full workflow
        await _trainingService.AddAttendeesAsync(session.Id, new[] { 1 });
        await _trainingService.UpdateSessionStatusAsync(session.Id, TrainingSessionStatus.IN_PROGRESS);
        await _trainingService.UpdateSessionStatusAsync(session.Id, TrainingSessionStatus.COMPLETED);
        
        var attendance = new[] { new TrainingAttendanceInput { WorkerId = 1, Outcome = TrainingOutcome.PASS } };
        await _trainingService.FinalizeSessionAsync(session.Id, attendance);
        
        // Assert - Certificate issued
        var certificates = await _trainingService.GetWorkerCertificatesAsync(1);
        var issuedCertificate = certificates.FirstOrDefault(c => c.SessionId == session.Id);
        
        Assert.IsNotNull(issuedCertificate);
        Assert.AreEqual(CertificateStatus.ISSUED, issuedCertificate.Status);
        Assert.IsTrue(issuedCertificate.ExpiryDate > DateTime.UtcNow);
    }
}
```

### GraphQL Tests

```csharp
[TestMethod]
public async Task TrainingQueries_Should_ReturnCorrectData()
{
    // Test GraphQL queries
    var query = @"
        query {
            allTrainingSessions {
                id
                status
                program { title }
                attendees { workerId outcome }
            }
        }";

    var result = await ExecuteGraphQLQueryAsync(query);
    Assert.IsTrue(result.Data != null);
}
```

## Troubleshooting

### Common Issues

#### 1. **Certificate Not Issuing After Session Completion**

**Symptoms**: Session finalized but no certificate created

**Causes**:
- Worker outcome not set to `PASS`
- Program validity days not configured
- File upload issues

**Solution**:
```csharp
// Check session attendee outcomes
var session = await _trainingService.GetSessionByIdAsync(sessionId);
var attendee = session.Attendees.FirstOrDefault(a => a.WorkerId == workerId);
if (attendee?.Outcome != TrainingOutcome.PASS)
{
    // Update outcome and re-finalize
}
```

#### 2. **Notifications Not Sending**

**Symptoms**: Training notifications not appearing in user interface

**Causes**:
- Notification service not registered in DI
- User notification preferences disabled
- Notification event bus issues

**Solution**:
```csharp
// Check service registration
services.AddScoped<TrainingNotificationService>();

// Verify notification preferences
var preferences = await _notificationService.GetUserPreferencesAsync(userId, "training.certificate.expiring");

// Test notification directly
await _notificationService.PublishToUserAsync(testEvent, userId);
```

#### 3. **Worker Eligibility Always Returns NOT_ELIGIBLE**

**Symptoms**: Workers showing as ineligible despite valid certificates

**Causes**:
- Trade requirements not configured
- Certificate status not updated
- Date comparison issues

**Solution**:
```csharp
// Check trade requirements
var requirements = await context.TradeRequirements
    .Where(tr => tr.TradeId == tradeId && tr.Mandatory)
    .ToListAsync();

// Verify certificate status
var certificates = await context.TrainingCertificates
    .Where(c => c.WorkerId == workerId && c.Status == CertificateStatus.VALID)
    .ToListAsync();
```

#### 4. **File Upload Issues**

**Symptoms**: Session pictures or certificates not uploading

**Causes**:
- MinIO service configuration
- File size limits
- Permission issues

**Solution**:
```csharp
// Check MinIO configuration
services.Configure<MinioConfiguration>(configuration.GetSection("Minio"));

// Verify file upload
try
{
    var result = await _minioService.UploadFileAsync(stream, fileName, bucket, contentType, description, path, false, null);
    _logger.LogInformation("File uploaded successfully: {FileId}", result.Id);
}
catch (Exception ex)
{
    _logger.LogError(ex, "File upload failed");
}
```

### Performance Optimization

#### 1. **Database Indexes**

Ensure proper indexing for common queries:

```sql
-- Most important indexes
CREATE INDEX IX_TrainingCertificates_WorkerId_ExpiryDate ON TrainingCertificates (WorkerId, ExpiryDate);
CREATE INDEX IX_TrainingCertificates_Status_ExpiryDate ON TrainingCertificates (Status, ExpiryDate);
CREATE INDEX IX_TrainingSessions_StartDate_Status ON TrainingSessions (StartDate, Status);
CREATE INDEX IX_TrainingEnrollments_SessionId_WorkerId ON TrainingEnrollments (SessionId, WorkerId);
```

#### 2. **Query Optimization**

Use proper includes and filtering:

```csharp
// Good - specific includes
var sessions = await context.TrainingSessions
    .Include(s => s.Program)
    .Include(s => s.Provider)
    .Where(s => s.StartDate >= DateTime.Today)
    .ToListAsync();

// Avoid - loading unnecessary data
var sessions = await context.TrainingSessions
    .Include(s => s.Program)
        .ThenInclude(p => p.Sessions) // Circular reference - avoid
    .ToListAsync();
```

#### 3. **Caching Strategy**

Cache frequently accessed data:

```csharp
public class CachedTrainingService : ITrainingService
{
    private readonly IMemoryCache _cache;
    private readonly ITrainingService _trainingService;

    public async Task<IEnumerable<TrainingProgram>> GetActiveProgramsAsync()
    {
        return await _cache.GetOrCreateAsync("active-programs", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            return await _trainingService.GetActiveProgramsAsync();
        });
    }
}
```

## Conclusion

The Training System provides a comprehensive, production-ready solution for workforce training management. By following the proven System patterns and integrating with the existing notification infrastructure, it ensures consistency, maintainability, and excellent user experience.

### Key Benefits:
✅ **Proven Architecture**: Identical patterns to successful Toolbox system  
✅ **Complete Integration**: Works seamlessly with existing workforce systems  
✅ **Scalable Design**: Handles complex training workflows and multi-day sessions  
✅ **Rich Notifications**: Real-time updates via existing notification system  
✅ **Audit Compliance**: Full audit trail for all training activities  
✅ **Performance Optimized**: Efficient database design and caching strategies  

The system is ready for production deployment and can be extended with additional features as needed.
