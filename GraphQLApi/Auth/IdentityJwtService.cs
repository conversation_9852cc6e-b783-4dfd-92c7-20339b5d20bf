using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Auth
{
    /// <summary>
    /// Enhanced JWT service with Identity integration and OWASP security measures
    /// Implements secure token generation, validation, and management
    /// Addresses OWASP A02 (Cryptographic Failures), A07 (Identification and Authentication Failures)
    /// </summary>
    public class IdentityJwtService : IJwtService
    {
        private readonly JwtSettings _jwtSettings;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly ILogger<IdentityJwtService> _logger;
        private readonly TokenValidationParameters _tokenValidationParameters;
        private readonly IServiceProvider _serviceProvider;

        public IdentityJwtService(
            IOptions<JwtSettings> jwtSettings,
            UserManager<ApplicationUser> userManager,
            IDbContextFactory<AppDbContext> dbContextFactory,
            ILogger<IdentityJwtService> logger,
            IServiceProvider serviceProvider)
        {
            _jwtSettings = jwtSettings.Value;
            _userManager = userManager;
            _dbContextFactory = dbContextFactory;
            _logger = logger;
            _serviceProvider = serviceProvider;

            _tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = _jwtSettings.ValidateIssuer,
                ValidateAudience = _jwtSettings.ValidateAudience,
                ValidateLifetime = _jwtSettings.ValidateLifetime,
                ValidateIssuerSigningKey = _jwtSettings.ValidateIssuerSigningKey,
                ValidIssuer = _jwtSettings.Issuer,
                ValidAudience = _jwtSettings.Audience,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey)),
                ClockSkew = TimeSpan.Zero, // Strict: No clock skew tolerance
                RequireExpirationTime = true,
                RequireSignedTokens = true,
                ValidateActor = true,
                ValidateTokenReplay = true,
                ValidAlgorithms = new[] { SecurityAlgorithms.HmacSha256 } // Restrict to secure algorithms
            };
        }


        public async Task<string> GenerateAccessTokenAsync(ApplicationUser user)
        {
            var jwtId = Guid.NewGuid().ToString();
            var claims = await BuildClaimsAsync(user, jwtId);

            // Add security stamp for enhanced security - critical for token invalidation
            var securityStamp = await _userManager.GetSecurityStampAsync(user);
            if (!string.IsNullOrEmpty(securityStamp))
            {
                claims.Add(new Claim("security_stamp", securityStamp));
            }

            // Add additional security claims
            claims.Add(new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64));
            claims.Add(new Claim("nbf", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64));
            claims.Add(new Claim("token_type", "access_token"));

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: claims,
                notBefore: DateTime.UtcNow,
                expires: DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes),
                signingCredentials: credentials
            );

            var tokenString = new JwtSecurityTokenHandler().WriteToken(token);

            _logger.LogDebug("Access token generated for user {UserId} with JTI {JwtId}", user.Id, jwtId);
            return tokenString;
        }


        public async Task<RefreshToken> GenerateRefreshTokenAsync(ApplicationUser user, string ipAddress, string userAgent)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            // Security: Check concurrent session limit
            var activeSessions = await context.UserSessions
                .Where(s => s.ApplicationUserId == user.Id && s.IsActive)
                .CountAsync();

            if (activeSessions >= _jwtSettings.MaxConcurrentSessions)
            {
                // Revoke oldest session for security
                var oldestSession = await context.UserSessions
                    .Where(s => s.ApplicationUserId == user.Id && s.IsActive)
                    .OrderBy(s => s.CreatedAt)
                    .FirstOrDefaultAsync();

                if (oldestSession != null)
                {
                    oldestSession.IsActive = false;
                    oldestSession.EndedAt = DateTime.UtcNow;
                    oldestSession.EndReason = "Concurrent session limit exceeded";
                    
                    _logger.LogInformation("Revoked oldest session for user {UserId} due to concurrent session limit", user.Id);
                }
            }

            // Generate cryptographically secure refresh token
            var refreshToken = new RefreshToken
            {
                Token = GenerateSecureToken(),
                JwtId = Guid.NewGuid().ToString(),
                ApplicationUserId = user.Id, // Use ApplicationUserId for Identity users
                ExpiresAt = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays),
                IpAddress = ipAddress,
                UserAgent = userAgent,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = user.Email ?? string.Empty
            };

            context.RefreshTokens.Add(refreshToken);
            await context.SaveChangesAsync();

            _logger.LogDebug("Refresh token generated for user {UserId} from IP {IpAddress}", user.Id, ipAddress);
            return refreshToken;
        }

        public async Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                
                // Pre-validation checks
                if (!tokenHandler.CanReadToken(token))
                {
                    _logger.LogWarning("Invalid JWT token format");
                    return null;
                }

                var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);

                if (validatedToken is not JwtSecurityToken jwtToken)
                {
                    _logger.LogWarning("Token is not a valid JWT");
                    return null;
                }

                // Security: Ensure algorithm is what we expect
                if (!jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    _logger.LogWarning("JWT token uses unexpected algorithm: {Algorithm}", jwtToken.Header.Alg);
                    return null;
                }

                // Additional security validation with Identity
                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var securityStampClaim = principal.FindFirst("security_stamp")?.Value;

                if (int.TryParse(userIdClaim, out var userId) && !string.IsNullOrEmpty(securityStampClaim))
                {
                    var user = await _userManager.FindByIdAsync(userId.ToString());
                    if (user == null)
                    {
                        _logger.LogWarning("User {UserId} not found during token validation", userId);
                        return null;
                    }

                    var currentSecurityStamp = await _userManager.GetSecurityStampAsync(user);
                    if (securityStampClaim != currentSecurityStamp)
                    {
                        _logger.LogWarning("Security stamp mismatch for user {UserId}. Token may be compromised.", userId);
                        return null;
                    }

                    // Check if user is still active and not locked
                    if (await _userManager.IsLockedOutAsync(user) || !user.IsActive)
                    {
                        _logger.LogWarning("User {UserId} account is locked or inactive", userId);
                        return null;
                    }
                }

                return principal;
            }
            catch (SecurityTokenExpiredException)
            {
                _logger.LogDebug("JWT token has expired");
                return null;
            }
            catch (SecurityTokenInvalidSignatureException)
            {
                _logger.LogWarning("JWT token has invalid signature");
                return null;
            }
            catch (SecurityTokenValidationException ex)
            {
                _logger.LogWarning("JWT token validation failed: {Error}", ex.Message);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during token validation");
                return null;
            }
        }

        public async Task<bool> ValidateRefreshTokenAsync(string token)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var refreshToken = await context.RefreshTokens
                .Include(rt => rt.ApplicationUser)
                .FirstOrDefaultAsync(rt => rt.Token == token);

            if (refreshToken == null)
            {
                _logger.LogWarning("Refresh token not found: {Token}", token[..8] + "...");
                return false;
            }

            var isValid = refreshToken.IsActive;
            if (!isValid)
            {
                _logger.LogWarning("Refresh token is not active for user {UserId}", refreshToken.ApplicationUserId);
            }

            return isValid;
        }

        public async Task<RefreshToken?> GetRefreshTokenAsync(string token)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.RefreshTokens
                .Include(rt => rt.ApplicationUser)
                .ThenInclude(u => u.Role)
                .Include(rt => rt.ApplicationUser)
                .ThenInclude(u => u.Tenant)
                .FirstOrDefaultAsync(rt => rt.Token == token);
        }

        public async Task RevokeRefreshTokenAsync(string token, string reason)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var refreshToken = await context.RefreshTokens
                .FirstOrDefaultAsync(rt => rt.Token == token);

            if (refreshToken != null)
            {
                refreshToken.IsRevoked = true;
                refreshToken.RevokedAt = DateTime.UtcNow;
                refreshToken.RevokedReason = reason;
                refreshToken.UpdatedAt = DateTime.UtcNow;

                await context.SaveChangesAsync();

                _logger.LogInformation("Refresh token revoked for user {UserId}. Reason: {Reason}", 
                    refreshToken.ApplicationUserId, reason);
            }
        }

        public async Task RevokeAllUserTokensAsync(int userId, string reason)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var activeTokens = await context.RefreshTokens
                .Where(rt => rt.ApplicationUserId == userId && !rt.IsRevoked && rt.ExpiresAt > DateTime.UtcNow)
                .ToListAsync();

            foreach (var token in activeTokens)
            {
                token.IsRevoked = true;
                token.RevokedAt = DateTime.UtcNow;
                token.RevokedReason = reason;
                token.UpdatedAt = DateTime.UtcNow;
            }

            await context.SaveChangesAsync();

            _logger.LogInformation("Revoked {TokenCount} refresh tokens for user {UserId}. Reason: {Reason}", 
                activeTokens.Count, userId, reason);
        }

        public async Task CleanupExpiredTokensAsync()
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var expiredTokens = await context.RefreshTokens
                .Where(rt => rt.ExpiresAt <= DateTime.UtcNow)
                .ToListAsync();

            context.RefreshTokens.RemoveRange(expiredTokens);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cleaned up {Count} expired refresh tokens", expiredTokens.Count);
        }

        public string? GetUserIdFromToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                if (!tokenHandler.CanReadToken(token))
                {
                    return null;
                }

                var jwt = tokenHandler.ReadJwtToken(token);
                return jwt.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting user ID from token");
                return null;
            }
        }

        public string? GetJwtIdFromToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                if (!tokenHandler.CanReadToken(token))
                {
                    return null;
                }

                var jwt = tokenHandler.ReadJwtToken(token);
                return jwt.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting JWT ID from token");
                return null;
            }
        }

        public bool IsTokenExpired(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                if (!tokenHandler.CanReadToken(token))
                {
                    return true;
                }

                var jwt = tokenHandler.ReadJwtToken(token);
                return jwt.ValidTo <= DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking token expiration");
                return true;
            }
        }

        private async Task<List<Claim>> BuildClaimsAsync(ApplicationUser user, string jwtId)
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Email, user.Email ?? string.Empty),
                new(ClaimTypes.Name, user.FullName),
                new(ClaimTypes.GivenName, user.FirstName),
                new(ClaimTypes.Surname, user.LastName),
                new(JwtRegisteredClaimNames.Jti, jwtId),
                new(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new("tenant_id", user.TenantId.ToString()),
                new("role_id", user.RoleId?.ToString() ?? "0"),
                new("role_name", user.Role?.Name ?? "SuperAdmin"),
                new("email_verified", user.EmailConfirmed.ToString().ToLower()),
                new("phone_verified", user.PhoneNumberConfirmed.ToString().ToLower())
            };

            // Load the Role navigation property if not already loaded and RoleId exists
            if (user.Role == null && user.RoleId.HasValue)
            {
                _logger.LogInformation("Attempting to load role for user {UserId} with RoleId {RoleId}",
                    user.Id, user.RoleId);
                using var context = await _dbContextFactory.CreateDbContextAsync();
                user.Role = await context.CustomRoles.FindAsync(user.RoleId.Value);
                _logger.LogInformation("Loaded role for user {UserId}: {RoleName} (ID: {RoleId})",
                    user.Id, user.Role?.Name ?? "null", user.RoleId);
            }
            else
            {
                _logger.LogInformation("Role loading skipped for user {UserId}. Role is null: {RoleIsNull}, RoleId: {RoleId}",
                    user.Id, user.Role == null, user.RoleId);
            }

            // Add existing permission claims from custom Role
            _logger.LogInformation("Building claims for user {UserId}. Role: {RoleName}, RoleId: {RoleId}",
                user.Id, user.Role?.Name ?? "null", user.RoleId);

            if (user.Role != null)
            {
                _logger.LogInformation("Adding permission claims. Worker permissions: {WorkerPermissions}",
                    user.Role.WorkerPermissions);
                claims.Add(new Claim("worker_permissions", user.Role.WorkerPermissions.ToString()));
                claims.Add(new Claim("site_permissions", user.Role.SitePermissions.ToString()));
                claims.Add(new Claim("training_permissions", user.Role.TrainingPermissions.ToString()));
                claims.Add(new Claim("document_permissions", user.Role.DocumentPermissions.ToString()));
                claims.Add(new Claim("ppe_permissions", user.Role.PPEPermissions.ToString()));
                claims.Add(new Claim("role_management_permissions", user.Role.RoleManagementPermissions.ToString()));
                claims.Add(new Claim("report_permissions", user.Role.ReportPermissions.ToString()));
            }
            else
            {
                _logger.LogWarning("No role found for user {UserId} (RoleId: {RoleId}). Permission claims will not be added.",
                    user.Id, user.RoleId);
            }

            // Add Identity-based roles
            var identityRoles = await _userManager.GetRolesAsync(user);
            foreach (var role in identityRoles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add user claims from Identity
            var userClaims = await _userManager.GetClaimsAsync(user);
            claims.AddRange(userClaims);

            return claims;
        }

        /// <summary>
        /// Generates a cryptographically secure random token
        /// Uses RNGCryptoServiceProvider for maximum security
        /// </summary>
        private static string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[64]; // 512 bits of entropy
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes);
        }
    }
}
