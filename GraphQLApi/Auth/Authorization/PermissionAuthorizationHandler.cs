using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using GraphQLApi.Services;
using Shared.DTOs;

namespace GraphQLApi.Auth.Authorization
{
    public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
    {
        private readonly ILogger<PermissionAuthorizationHandler> _logger;
        private readonly INotificationService _notificationService;

        public PermissionAuthorizationHandler(ILogger<PermissionAuthorizationHandler> logger, INotificationService notificationService)
        {
            _logger = logger;
            _notificationService = notificationService;
        }

        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            PermissionRequirement requirement)
        {
            var user = context.User;
            if (!user.Identity?.IsAuthenticated == true)
            {
                _logger.LogWarning("User is not authenticated");
                return Task.CompletedTask;
            }

            var hasPermission = CheckPermission(user, requirement);
            
            if (hasPermission)
            {
                context.Succeed(requirement);
                _logger.LogDebug("Permission granted for {Resource}.{Permission} at {Level} level", 
                    requirement.Resource, requirement.Permission, requirement.Level);
            }
            else
            {
                var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                _logger.LogWarning("Permission denied for {Resource}.{Permission} at {Level} level for user {UserId}",
                    requirement.Resource, requirement.Permission, requirement.Level, userId);

                // Fire-and-forget notification (do not block auth flow)
                _ = _notificationService.PublishAsync(new NotificationEvent(
                    Type: "authz_denied",
                    Title: "Permission denied",
                    Message: $"User {userId ?? "unknown"} denied {requirement.Resource}.{requirement.Permission} ({requirement.Level})",
                    Entity: requirement.Resource,
                    Operation: "Denied"
                ));
            }

            return Task.CompletedTask;
        }

        private bool CheckPermission(ClaimsPrincipal user, PermissionRequirement requirement)
        {
            try
            {
                _logger.LogInformation("User claims: {Claims}", 
                    string.Join(", ", user.Claims.Select(c => $"{c.Type}={c.Value}")));

                var permissionClaimName = GetPermissionClaimName(requirement.Resource);
                var permissionClaim = user.FindFirst(permissionClaimName);
                
                _logger.LogInformation("Checking permission claim: {ClaimName}, Value: {ClaimValue}", 
                    permissionClaimName, permissionClaim?.Value ?? "null");

                if (permissionClaim == null || !byte.TryParse(permissionClaim.Value, out var permissionValue))
                {
                    _logger.LogWarning("Permission claim not found or invalid: {ClaimName}", permissionClaimName);
                    return false;
                }

                var requiredFlag = GetRequiredPermissionFlag(requirement.Permission, requirement.Level);
                _logger.LogInformation("Required permission flag: {Flag}, User permission value: {UserValue}", 
                    requiredFlag, permissionValue);

                return (permissionValue & requiredFlag) == requiredFlag;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission for {Resource}.{Permission}", 
                    requirement.Resource, requirement.Permission);
                return false;
            }
        }

        private static string GetPermissionClaimName(string resource)
        {
            return resource.ToLowerInvariant() switch
            {
                "workers" => "worker_permissions",
                "sites" => "site_permissions",
                "trainings" => "training_permissions",
                "docs" => "document_permissions",
                "ppe" => "ppe_permissions",
                "rolemanagement" => "role_management_permissions",
                "reports" => "report_permissions",
                _ => throw new ArgumentException($"Unknown resource: {resource}")
            };
        }

        private static byte GetRequiredPermissionFlag(string permission, string level)
        {
            var baseFlag = permission.ToLowerInvariant() switch
            {
                "create" => 1,
                "read" => 2,
                "update" => 4,
                "delete" => 8,
                _ => throw new ArgumentException($"Unknown permission: {permission}")
            };

            // Company level permissions are shifted by 4 bits (16, 32, 64, 128)
            return level.ToLowerInvariant() switch
            {
                "site" => (byte)baseFlag,
                "company" => (byte)(baseFlag << 4),
                _ => throw new ArgumentException($"Unknown level: {level}")
            };
        }
    }
}
