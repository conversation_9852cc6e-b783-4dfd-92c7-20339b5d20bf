using Microsoft.AspNetCore.Authorization;

namespace GraphQLApi.Auth.Authorization
{
    public class PermissionRequirement : IAuthorizationRequirement
    {
        public string Resource { get; }
        public string Permission { get; }
        public string Level { get; }

        public PermissionRequirement(string resource, string permission, string level = "Site")
        {
            Resource = resource;
            Permission = permission;
            Level = level;
        }
    }

    public static class PermissionRequirements
    {
        // Worker permissions
        public static readonly PermissionRequirement CreateWorkerSite = new("Worker", "Create", "Site");
        public static readonly PermissionRequirement ReadWorkerSite = new("Worker", "Read", "Site");
        public static readonly PermissionRequirement UpdateWorkerSite = new("Worker", "Update", "Site");
        public static readonly PermissionRequirement DeleteWorkerSite = new("Worker", "Delete", "Site");
        public static readonly PermissionRequirement CreateWorkerCompany = new("Worker", "Create", "Company");
        public static readonly PermissionRequirement ReadWorkerCompany = new("Worker", "Read", "Company");
        public static readonly PermissionRequirement UpdateWorkerCompany = new("Worker", "Update", "Company");
        public static readonly PermissionRequirement DeleteWorkerCompany = new("Worker", "Delete", "Company");

        // Site permissions
        public static readonly PermissionRequirement CreateSiteSite = new("Site", "Create", "Site");
        public static readonly PermissionRequirement ReadSiteSite = new("Site", "Read", "Site");
        public static readonly PermissionRequirement UpdateSiteSite = new("Site", "Update", "Site");
        public static readonly PermissionRequirement DeleteSiteSite = new("Site", "Delete", "Site");
        public static readonly PermissionRequirement CreateSiteCompany = new("Site", "Create", "Company");
        public static readonly PermissionRequirement ReadSiteCompany = new("Site", "Read", "Company");
        public static readonly PermissionRequirement UpdateSiteCompany = new("Site", "Update", "Company");
        public static readonly PermissionRequirement DeleteSiteCompany = new("Site", "Delete", "Company");

        // Training permissions
        public static readonly PermissionRequirement CreateTrainingSite = new("Training", "Create", "Site");
        public static readonly PermissionRequirement ReadTrainingSite = new("Training", "Read", "Site");
        public static readonly PermissionRequirement UpdateTrainingSite = new("Training", "Update", "Site");
        public static readonly PermissionRequirement DeleteTrainingSite = new("Training", "Delete", "Site");
        public static readonly PermissionRequirement CreateTrainingCompany = new("Training", "Create", "Company");
        public static readonly PermissionRequirement ReadTrainingCompany = new("Training", "Read", "Company");
        public static readonly PermissionRequirement UpdateTrainingCompany = new("Training", "Update", "Company");
        public static readonly PermissionRequirement DeleteTrainingCompany = new("Training", "Delete", "Company");

        // Document permissions
        public static readonly PermissionRequirement CreateDocumentSite = new("Document", "Create", "Site");
        public static readonly PermissionRequirement ReadDocumentSite = new("Document", "Read", "Site");
        public static readonly PermissionRequirement UpdateDocumentSite = new("Document", "Update", "Site");
        public static readonly PermissionRequirement DeleteDocumentSite = new("Document", "Delete", "Site");
        public static readonly PermissionRequirement CreateDocumentCompany = new("Document", "Create", "Company");
        public static readonly PermissionRequirement ReadDocumentCompany = new("Document", "Read", "Company");
        public static readonly PermissionRequirement UpdateDocumentCompany = new("Document", "Update", "Company");
        public static readonly PermissionRequirement DeleteDocumentCompany = new("Document", "Delete", "Company");

        // PPE permissions
        public static readonly PermissionRequirement CreatePPESite = new("PPE", "Create", "Site");
        public static readonly PermissionRequirement ReadPPESite = new("PPE", "Read", "Site");
        public static readonly PermissionRequirement UpdatePPESite = new("PPE", "Update", "Site");
        public static readonly PermissionRequirement DeletePPESite = new("PPE", "Delete", "Site");
        public static readonly PermissionRequirement CreatePPECompany = new("PPE", "Create", "Company");
        public static readonly PermissionRequirement ReadPPECompany = new("PPE", "Read", "Company");
        public static readonly PermissionRequirement UpdatePPECompany = new("PPE", "Update", "Company");
        public static readonly PermissionRequirement DeletePPECompany = new("PPE", "Delete", "Company");

        // Role Management permissions
        public static readonly PermissionRequirement CreateRoleSite = new("RoleManagement", "Create", "Site");
        public static readonly PermissionRequirement ReadRoleSite = new("RoleManagement", "Read", "Site");
        public static readonly PermissionRequirement UpdateRoleSite = new("RoleManagement", "Update", "Site");
        public static readonly PermissionRequirement DeleteRoleSite = new("RoleManagement", "Delete", "Site");
        public static readonly PermissionRequirement CreateRoleCompany = new("RoleManagement", "Create", "Company");
        public static readonly PermissionRequirement ReadRoleCompany = new("RoleManagement", "Read", "Company");
        public static readonly PermissionRequirement UpdateRoleCompany = new("RoleManagement", "Update", "Company");
        public static readonly PermissionRequirement DeleteRoleCompany = new("RoleManagement", "Delete", "Company");

        // Report permissions
        public static readonly PermissionRequirement CreateReportSite = new("Report", "Create", "Site");
        public static readonly PermissionRequirement ReadReportSite = new("Report", "Read", "Site");
        public static readonly PermissionRequirement UpdateReportSite = new("Report", "Update", "Site");
        public static readonly PermissionRequirement DeleteReportSite = new("Report", "Delete", "Site");
        public static readonly PermissionRequirement CreateReportCompany = new("Report", "Create", "Company");
        public static readonly PermissionRequirement ReadReportCompany = new("Report", "Read", "Company");
        public static readonly PermissionRequirement UpdateReportCompany = new("Report", "Update", "Company");
        public static readonly PermissionRequirement DeleteReportCompany = new("Report", "Delete", "Company");
    }
}
