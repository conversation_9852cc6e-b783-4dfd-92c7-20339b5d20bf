using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Auth.Authorization
{
    /// <summary>
    /// Tenant-based authorization requirement
    /// Ensures users can only access resources within their tenant context
    /// Addresses OWASP A01 (Broken Access Control)
    /// </summary>
    public class TenantRequirement : IAuthorizationRequirement { }

    /// <summary>
    /// Authorization handler for tenant-based access control
    /// Validates that users operate within their assigned tenant boundaries
    /// </summary>
    public class TenantAuthorizationHandler : AuthorizationHandler<TenantRequirement>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<TenantAuthorizationHandler> _logger;

        public TenantAuthorizationHandler(
            UserManager<ApplicationUser> userManager,
            ILogger<TenantAuthorizationHandler> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            TenantRequirement requirement)
        {
            var user = context.User;
            if (!user.Identity?.IsAuthenticated == true)
            {
                _logger.LogWarning("Unauthenticated user attempted tenant access");
                return;
            }

            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var tenantIdClaim = user.FindFirst("tenant_id")?.Value;

            if (string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(tenantIdClaim))
            {
                _logger.LogWarning("User {UserId} missing tenant or user ID claims", userIdClaim ?? "unknown");
                return;
            }

            if (!int.TryParse(tenantIdClaim, out var claimedTenantId) || !int.TryParse(userIdClaim, out var userId))
            {
                _logger.LogWarning("Invalid tenant or user ID format for user {UserId}", userIdClaim);
                return;
            }

            // Validate tenant assignment against database
            var applicationUser = await _userManager.FindByIdAsync(userId.ToString());
            if (applicationUser == null)
            {
                _logger.LogWarning("User {UserId} not found in database", userId);
                return;
            }

            if (applicationUser.TenantId != claimedTenantId)
            {
                _logger.LogWarning("Tenant ID mismatch for user {UserId}. Claimed: {ClaimedTenant}, Actual: {ActualTenant}",
                    userId, claimedTenantId, applicationUser.TenantId);
                return;
            }

            // Additional tenant validation (e.g., tenant status, subscription)
            // This could include checking if tenant is active, subscription is valid, etc.

            context.Succeed(requirement);
            _logger.LogDebug("Tenant authorization succeeded for user {UserId} in tenant {TenantId}", userId, claimedTenantId);
        }
    }
}
