using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using System.Security.Claims;
using System.Net;

namespace GraphQLApi.Auth.Authorization
{
    /// <summary>
    /// Rate limiting requirement for API endpoints
    /// Prevents abuse and brute force attacks
    /// Addresses OWASP A01 (Broken Access Control), A07 (Identification and Authentication Failures)
    /// </summary>
    public class RateLimitRequirement : IAuthorizationRequirement 
    {
        public int MaxRequests { get; set; } = 100; // Default 100 requests
        public TimeSpan TimeWindow { get; set; } = TimeSpan.FromMinutes(1); // Per minute
        public string? Endpoint { get; set; }
    }

    /// <summary>
    /// Authorization handler for rate limiting
    /// Implements sliding window rate limiting per user/IP combination
    /// </summary>
    public class RateLimitAuthorizationHandler : AuthorizationHandler<RateLimitRequirement>
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitAuthorizationHandler> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RateLimitAuthorizationHandler(
            IMemoryCache cache,
            ILogger<RateLimitAuthorizationHandler> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _cache = cache;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            RateLimitRequirement requirement)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                context.Succeed(requirement); // Allow if no HTTP context
                return Task.CompletedTask;
            }

            var user = context.User;
            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var clientIp = GetClientIpAddress(httpContext);
            var endpoint = requirement.Endpoint ?? httpContext.Request.Path.Value ?? "unknown";

            // Create rate limit key combining user ID and IP for security
            var rateLimitKey = $"rate_limit:{userIdClaim ?? "anonymous"}:{clientIp}:{endpoint}";

            // Get current request count
            var requestData = _cache.Get<RateLimitData>(rateLimitKey) ?? new RateLimitData();

            // Clean old requests outside the time window
            var cutoffTime = DateTime.UtcNow.Subtract(requirement.TimeWindow);
            requestData.Requests.RemoveAll(r => r < cutoffTime);

            // Check if rate limit exceeded
            if (requestData.Requests.Count >= requirement.MaxRequests)
            {
                _logger.LogWarning("Rate limit exceeded for {User} from {IP} on {Endpoint}. {RequestCount}/{MaxRequests} requests in {TimeWindow}",
                    userIdClaim ?? "anonymous", clientIp, endpoint, requestData.Requests.Count, requirement.MaxRequests, requirement.TimeWindow);

                // Log potential abuse
                if (requestData.Requests.Count > requirement.MaxRequests * 2)
                {
                    _logger.LogError("Potential abuse detected for {User} from {IP} on {Endpoint}. {RequestCount} requests in {TimeWindow}",
                        userIdClaim ?? "anonymous", clientIp, endpoint, requestData.Requests.Count, requirement.TimeWindow);
                }

                return Task.CompletedTask; // Fail authorization
            }

            // Add current request
            requestData.Requests.Add(DateTime.UtcNow);

            // Update cache
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = requirement.TimeWindow,
                SlidingExpiration = TimeSpan.FromMinutes(5),
                Priority = CacheItemPriority.Low
            };
            _cache.Set(rateLimitKey, requestData, cacheOptions);

            context.Succeed(requirement);
            
            _logger.LogDebug("Rate limit check passed for {User} from {IP} on {Endpoint}. {RequestCount}/{MaxRequests} requests",
                userIdClaim ?? "anonymous", clientIp, endpoint, requestData.Requests.Count, requirement.MaxRequests);

            return Task.CompletedTask;
        }

        private static string GetClientIpAddress(HttpContext httpContext)
        {
            // Check for forwarded IP first (load balancers, proxies)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (ips.Length > 0)
                {
                    var ip = ips[0].Trim();
                    if (IPAddress.TryParse(ip, out _))
                    {
                        return ip;
                    }
                }
            }

            // Check for real IP (some proxies use this)
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp) && IPAddress.TryParse(realIp, out _))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return httpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }

    /// <summary>
    /// Data structure for tracking rate limit information
    /// </summary>
    public class RateLimitData
    {
        public List<DateTime> Requests { get; set; } = new List<DateTime>();
    }
}
