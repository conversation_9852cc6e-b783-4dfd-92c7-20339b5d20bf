using HotChocolate.Authorization;

namespace GraphQLApi.Auth.Authorization
{
    public class AuthorizePermissionAttribute : AuthorizeAttribute
    {
        public AuthorizePermissionAttribute(string resource, string permission, string level = "Site")
        {
            Policy = $"{resource}.{permission}.{level}";
        }
    }

    // Convenience attributes for common permissions
    public class AuthorizeWorkerCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeWorkerCreateAttribute(string level = "Site") : base("Worker", "Create", level) { }
    }

    public class AuthorizeWorkerReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeWorkerReadAttribute(string level = "Site") : base("Worker", "Read", level) { }
    }

    public class AuthorizeWorkerUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeWorkerUpdateAttribute(string level = "Site") : base("Worker", "Update", level) { }
    }

    public class AuthorizeWorkerDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeWorkerDeleteAttribute(string level = "Site") : base("Worker", "Delete", level) { }
    }

    public class AuthorizeSiteCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeSiteCreateAttribute(string level = "Site") : base("Site", "Create", level) { }
    }

    public class AuthorizeSiteReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeSiteReadAttribute(string level = "Site") : base("Site", "Read", level) { }
    }

    public class AuthorizeSiteUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeSiteUpdateAttribute(string level = "Site") : base("Site", "Update", level) { }
    }

    public class AuthorizeSiteDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeSiteDeleteAttribute(string level = "Site") : base("Site", "Delete", level) { }
    }

    public class AuthorizeTrainingCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeTrainingCreateAttribute(string level = "Site") : base("Training", "Create", level) { }
    }

    public class AuthorizeTrainingReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeTrainingReadAttribute(string level = "Site") : base("Training", "Read", level) { }
    }

    public class AuthorizeTrainingUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeTrainingUpdateAttribute(string level = "Site") : base("Training", "Update", level) { }
    }

    public class AuthorizeTrainingDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeTrainingDeleteAttribute(string level = "Site") : base("Training", "Delete", level) { }
    }

    public class AuthorizeDocumentCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeDocumentCreateAttribute(string level = "Site") : base("Document", "Create", level) { }
    }

    public class AuthorizeDocumentReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeDocumentReadAttribute(string level = "Site") : base("Document", "Read", level) { }
    }

    public class AuthorizeDocumentUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeDocumentUpdateAttribute(string level = "Site") : base("Document", "Update", level) { }
    }

    public class AuthorizeDocumentDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeDocumentDeleteAttribute(string level = "Site") : base("Document", "Delete", level) { }
    }

    public class AuthorizePPECreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizePPECreateAttribute(string level = "Site") : base("PPE", "Create", level) { }
    }

    public class AuthorizePPEReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizePPEReadAttribute(string level = "Site") : base("PPE", "Read", level) { }
    }

    public class AuthorizePPEUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizePPEUpdateAttribute(string level = "Site") : base("PPE", "Update", level) { }
    }

    public class AuthorizePPEDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizePPEDeleteAttribute(string level = "Site") : base("PPE", "Delete", level) { }
    }

    public class AuthorizeRoleCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeRoleCreateAttribute(string level = "Site") : base("RoleManagement", "Create", level) { }
    }

    public class AuthorizeRoleReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeRoleReadAttribute(string level = "Site") : base("RoleManagement", "Read", level) { }
    }

    public class AuthorizeRoleUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeRoleUpdateAttribute(string level = "Site") : base("RoleManagement", "Update", level) { }
    }

    public class AuthorizeRoleDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeRoleDeleteAttribute(string level = "Site") : base("RoleManagement", "Delete", level) { }
    }

    public class AuthorizeReportCreateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeReportCreateAttribute(string level = "Site") : base("Report", "Create", level) { }
    }

    public class AuthorizeReportReadAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeReportReadAttribute(string level = "Site") : base("Report", "Read", level) { }
    }

    public class AuthorizeReportUpdateAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeReportUpdateAttribute(string level = "Site") : base("Report", "Update", level) { }
    }

    public class AuthorizeReportDeleteAttribute : AuthorizePermissionAttribute
    {
        public AuthorizeReportDeleteAttribute(string level = "Site") : base("Report", "Delete", level) { }
    }
}
