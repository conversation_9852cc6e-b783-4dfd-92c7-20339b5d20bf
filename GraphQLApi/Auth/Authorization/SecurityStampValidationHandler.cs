using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Auth.Authorization
{
    /// <summary>
    /// Security stamp validation requirement
    /// Ensures tokens are invalidated when security-sensitive changes occur
    /// Addresses OWASP A07 (Identification and Authentication Failures)
    /// </summary>
    public class SecurityStampRequirement : IAuthorizationRequirement { }

    /// <summary>
    /// Authorization handler for security stamp validation
    /// Validates that the user's security stamp matches the current one in the database
    /// This prevents use of stale tokens after password changes, role modifications, etc.
    /// </summary>
    public class SecurityStampValidationHandler : AuthorizationHandler<SecurityStampRequirement>
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<SecurityStampValidationHandler> _logger;

        public SecurityStampValidationHandler(
            UserManager<ApplicationUser> userManager,
            ILogger<SecurityStampValidationHandler> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            SecurityStampRequirement requirement)
        {
            var user = context.User;
            if (!user.Identity?.IsAuthenticated == true)
            {
                _logger.LogWarning("Unauthenticated user attempted security stamp validation");
                return;
            }

            var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var securityStampClaim = user.FindFirst("security_stamp")?.Value;

            if (string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(securityStampClaim))
            {
                _logger.LogWarning("User {UserId} missing security stamp or user ID claims", userIdClaim ?? "unknown");
                return;
            }

            if (!int.TryParse(userIdClaim, out var userId))
            {
                _logger.LogWarning("Invalid user ID format: {UserId}", userIdClaim);
                return;
            }

            var applicationUser = await _userManager.FindByIdAsync(userId.ToString());
            if (applicationUser == null)
            {
                _logger.LogWarning("User {UserId} not found during security stamp validation", userId);
                return;
            }

            var currentSecurityStamp = await _userManager.GetSecurityStampAsync(applicationUser);
            if (string.IsNullOrEmpty(currentSecurityStamp))
            {
                _logger.LogWarning("User {UserId} has no security stamp in database", userId);
                return;
            }

            if (securityStampClaim != currentSecurityStamp)
            {
                _logger.LogWarning("Security stamp validation failed for user {UserId}. Token may be compromised or stale.", userId);
                
                // Additional security logging
                _logger.LogWarning("Security stamp mismatch details - User: {UserId}, Token Stamp: {TokenStamp}, Current Stamp: {CurrentStamp}",
                    userId, securityStampClaim, currentSecurityStamp);
                
                return;
            }

            // Check if user account is still active and not locked
            if (await _userManager.IsLockedOutAsync(applicationUser))
            {
                _logger.LogWarning("User {UserId} account is locked out", userId);
                return;
            }

            if (!applicationUser.IsActive)
            {
                _logger.LogWarning("User {UserId} account is inactive", userId);
                return;
            }

            context.Succeed(requirement);
            _logger.LogDebug("Security stamp validation succeeded for user {UserId}", userId);
        }
    }
}
