{
    // Permission Matrix for CRUD operations at Site and Company levels
    // Each resource has its own separate bitwise sequence for granular control
    // This allows assigning specific permissions like "delete workers at site level but not company level"

    "WorkerPermissions": {
        "Site": [
            {
                "name": "ViewWorkersSite",
                "description": "View workers at site level",
                "value": 1
            },
            {
                "name": "CreateWorkersSite",
                "description": "Create workers at site level",
                "value": 2
            },
            {
                "name": "UpdateWorkersSite",
                "description": "Update workers at site level",
                "value": 4
            },
            {
                "name": "DeleteWorkersSite",
                "description": "Delete workers at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewWorkersCompany",
                "description": "View workers at company level",
                "value": 16
            },
            {
                "name": "CreateWorkersCompany",
                "description": "Create workers at company level",
                "value": 32
            },
            {
                "name": "UpdateWorkersCompany",
                "description": "Update workers at company level",
                "value": 64
            },
            {
                "name": "DeleteWorkersCompany",
                "description": "Delete workers at company level",
                "value": 128
            }
        ]
    },
    "SitePermissions": {
        "Site": [
            {
                "name": "ViewSitesSite",
                "description": "View site information at site level",
                "value": 1
            },
            {
                "name": "CreateSitesSite",
                "description": "Create site information at site level",
                "value": 2
            },
            {
                "name": "UpdateSitesSite",
                "description": "Update site information at site level",
                "value": 4
            },
            {
                "name": "DeleteSitesSite",
                "description": "Delete site information at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewSitesCompany",
                "description": "View all sites at company level",
                "value": 16
            },
            {
                "name": "CreateSitesCompany",
                "description": "Create new sites at company level",
                "value": 32
            },
            {
                "name": "UpdateSitesCompany",
                "description": "Update sites at company level",
                "value": 64
            },
            {
                "name": "DeleteSitesCompany",
                "description": "Delete sites at company level",
                "value": 128
            }
        ]
    },
    "TrainingPermissions": {
        "Site": [
            {
                "name": "ViewTrainingsSite",
                "description": "View trainings at site level",
                "value": 1
            },
            {
                "name": "CreateTrainingsSite",
                "description": "Create trainings at site level",
                "value": 2
            },
            {
                "name": "UpdateTrainingsSite",
                "description": "Update trainings at site level",
                "value": 4
            },
            {
                "name": "DeleteTrainingsSite",
                "description": "Delete trainings at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewTrainingsCompany",
                "description": "View all trainings at company level",
                "value": 16
            },
            {
                "name": "CreateTrainingsCompany",
                "description": "Create trainings at company level",
                "value": 32
            },
            {
                "name": "UpdateTrainingsCompany",
                "description": "Update trainings at company level",
                "value": 64
            },
            {
                "name": "DeleteTrainingsCompany",
                "description": "Delete trainings at company level",
                "value": 128
            }
        ]
    },
    "DocumentPermissions": {
        "Site": [
            {
                "name": "ViewDocsSite",
                "description": "View documents at site level",
                "value": 1
            },
            {
                "name": "CreateDocsSite",
                "description": "Create documents at site level",
                "value": 2
            },
            {
                "name": "UpdateDocsSite",
                "description": "Update documents at site level",
                "value": 4
            },
            {
                "name": "DeleteDocsSite",
                "description": "Delete documents at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewDocsCompany",
                "description": "View all documents at company level",
                "value": 16
            },
            {
                "name": "CreateDocsCompany",
                "description": "Create documents at company level",
                "value": 32
            },
            {
                "name": "UpdateDocsCompany",
                "description": "Update documents at company level",
                "value": 64
            },
            {
                "name": "DeleteDocsCompany",
                "description": "Delete documents at company level",
                "value": 128
            }
        ]
    },
    "PPEPermissions": {
        "Site": [
            {
                "name": "ViewPPESite",
                "description": "View PPE inventory at site level",
                "value": 1
            },
            {
                "name": "CreatePPESite",
                "description": "Create PPE records at site level",
                "value": 2
            },
            {
                "name": "UpdatePPESite",
                "description": "Update PPE records at site level",
                "value": 4
            },
            {
                "name": "DeletePPESite",
                "description": "Delete PPE records at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewPPECompany",
                "description": "View all PPE inventory at company level",
                "value": 16
            },
            {
                "name": "CreatePPECompany",
                "description": "Create PPE records at company level",
                "value": 32
            },
            {
                "name": "UpdatePPECompany",
                "description": "Update PPE records at company level",
                "value": 64
            },
            {
                "name": "DeletePPECompany",
                "description": "Delete PPE records at company level",
                "value": 128
            }
        ]
    },
    "RoleManagementPermissions": {
        "Site": [
            {
                "name": "ViewRolesSite",
                "description": "View user roles at site level",
                "value": 1
            },
            {
                "name": "CreateRolesSite",
                "description": "Create user roles at site level",
                "value": 2
            },
            {
                "name": "UpdateRolesSite",
                "description": "Update user roles at site level",
                "value": 4
            },
            {
                "name": "DeleteRolesSite",
                "description": "Delete user roles at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewRolesCompany",
                "description": "View all user roles at company level",
                "value": 16
            },
            {
                "name": "CreateRolesCompany",
                "description": "Create user roles at company level",
                "value": 32
            },
            {
                "name": "UpdateRolesCompany",
                "description": "Update user roles at company level",
                "value": 64
            },
            {
                "name": "DeleteRolesCompany",
                "description": "Delete user roles at company level",
                "value": 128
            }
        ]
    },
    "ReportPermissions": {
        "Site": [
            {
                "name": "ViewReportsSite",
                "description": "View reports at site level",
                "value": 1
            },
            {
                "name": "CreateReportsSite",
                "description": "Generate reports at site level",
                "value": 2
            },
            {
                "name": "UpdateReportsSite",
                "description": "Update reports at site level",
                "value": 4
            },
            {
                "name": "DeleteReportsSite",
                "description": "Delete reports at site level",
                "value": 8
            }
        ],
        "Company": [
            {
                "name": "ViewReportsCompany",
                "description": "View all reports at company level",
                "value": 16
            },
            {
                "name": "CreateReportsCompany",
                "description": "Generate reports at company level",
                "value": 32
            },
            {
                "name": "UpdateReportsCompany",
                "description": "Update reports at company level",
                "value": 64
            },
            {
                "name": "DeleteReportsCompany",
                "description": "Delete reports at company level",
                "value": 128
            }
        ]
    },
    "RoleTemplates": {
        "SiteEngineer": {
            "description": "Site-level engineer with limited permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 5,     // View (1) + Update (4)
                    "Company": 0
                },
                "SitePermissions": {
                    "Site": 5,     // View (1) + Update (4)
                    "Company": 0
                },
                "TrainingPermissions": {
                    "Site": 7,     // View (1) + Create (2) + Update (4)
                    "Company": 0
                },
                "DocumentPermissions": {
                    "Site": 7,     // View (1) + Create (2) + Update (4)
                    "Company": 0
                },
                "PPEPermissions": {
                    "Site": 5,     // View (1) + Update (4)
                    "Company": 0
                },
                "RoleManagementPermissions": {
                    "Site": 0,
                    "Company": 0
                },
                "ReportPermissions": {
                    "Site": 3,     // View (1) + Create (2)
                    "Company": 0
                }
            }
        },
        "SiteHSE": {
            "description": "Site HSE officer with safety-focused permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 7,     // View (1) + Create (2) + Update (4)
                    "Company": 0
                },
                "SitePermissions": {
                    "Site": 5,     // View (1) + Update (4)
                    "Company": 0
                },
                "TrainingPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "DocumentPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "PPEPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "RoleManagementPermissions": {
                    "Site": 0,
                    "Company": 0
                },
                "ReportPermissions": {
                    "Site": 7,     // View (1) + Create (2) + Update (4)
                    "Company": 0
                }
            }
        },
        "SiteManager": {
            "description": "Site manager with full site-level permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "SitePermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "TrainingPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "DocumentPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "PPEPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                },
                "RoleManagementPermissions": {
                    "Site": 7,     // View (1) + Create (2) + Update (4)
                    "Company": 0
                },
                "ReportPermissions": {
                    "Site": 15,    // View (1) + Create (2) + Update (4) + Delete (8)
                    "Company": 0
                }
            }
        },
        "MainHSE": {
            "description": "Main HSE with company-wide safety permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 0,
                    "Company": 112  // View (16) + Create (32) + Update (64)
                },
                "SitePermissions": {
                    "Site": 0,
                    "Company": 80   // View (16) + Update (64)
                },
                "TrainingPermissions": {
                    "Site": 0,
                    "Company": 240  // View (16) + Create (32) + Update (64) + Delete (128)
                },
                "DocumentPermissions": {
                    "Site": 0,
                    "Company": 240  // View (16) + Create (32) + Update (64) + Delete (128)
                },
                "PPEPermissions": {
                    "Site": 0,
                    "Company": 240  // View (16) + Create (32) + Update (64) + Delete (128)
                },
                "RoleManagementPermissions": {
                    "Site": 0,
                    "Company": 0
                },
                "ReportPermissions": {
                    "Site": 0,
                    "Company": 112  // View (16) + Create (32) + Update (64)
                }
            }
        },
        "LineManager": {
            "description": "Line manager with read-only company-wide access",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "SitePermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "TrainingPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "DocumentPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "PPEPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "RoleManagementPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "ReportPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                }
            }
        },
        "Accountant": {
            "description": "Accountant with financial reporting permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "SitePermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "TrainingPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "DocumentPermissions": {
                    "Site": 0,
                    "Company": 16   // View (16)
                },
                "PPEPermissions": {
                    "Site": 0,
                    "Company": 0
                },
                "RoleManagementPermissions": {
                    "Site": 0,
                    "Company": 0
                },
                "ReportPermissions": {
                    "Site": 0,
                    "Company": 112  // View (16) + Create (32) + Update (64)
                }
            }
        },
        "Admin": {
            "description": "System administrator with full permissions",
            "permissions": {
                "WorkerPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "SitePermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "TrainingPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "DocumentPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "PPEPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "RoleManagementPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                },
                "ReportPermissions": {
                    "Site": 255,   // All permissions
                    "Company": 255
                }
            }
        }
    }
}