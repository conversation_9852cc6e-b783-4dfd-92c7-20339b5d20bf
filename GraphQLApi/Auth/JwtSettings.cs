namespace GraphQLApi.Auth
{
    public class JwtSettings
    {
        public const string SectionName = "JwtSettings";

        public string SecretKey { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int AccessTokenExpirationMinutes { get; set; } = 15;
        public int RefreshTokenExpirationDays { get; set; } = 7;
        public int SessionTimeoutMinutes { get; set; } = 60;
        public bool RequireHttpsMetadata { get; set; } = true;
        public bool ValidateIssuer { get; set; } = true;
        public bool ValidateAudience { get; set; } = true;
        public bool ValidateLifetime { get; set; } = true;
        public bool ValidateIssuerSigningKey { get; set; } = true;
        public int ClockSkewSeconds { get; set; } = 5;
        public int MaxConcurrentSessions { get; set; } = 3;
        public bool EnableSessionTracking { get; set; } = true;
        public bool EnableAuditLogging { get; set; } = true;
        public bool EnableStrictIpValidation { get; set; } = false; // Optional strict IP validation
    }
}
