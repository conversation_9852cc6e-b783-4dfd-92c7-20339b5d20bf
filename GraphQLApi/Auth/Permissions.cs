﻿using System;

namespace GraphQLApi.Auth
{
    /*
     * Separate permission categories for each resource type
    
     * Role hierarchy:
     * 1. Admin - Full access to everything
     * 2. Line Manager - Read-only company-wide access
     * 3. Accountant - Limited company access with financial reporting
     * 4. Main HSE - Company-wide safety and compliance
     * 5. Site HSE - Site-level safety and compliance
     * 6. Site Manager - Full site-level management
     * 7. Site Engineer - Limited site-level operations
     */
    public class Permissions
    {
        // Separate permission enums for each resource type
        [Flags]
        public enum WorkerPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum SitePermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum TrainingPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum DocumentPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum PPEPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum RoleManagementPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        [Flags]
        public enum ReportPermissions : byte
        {
            None = 0,
            ViewSite = 1,
            CreateSite = 2,
            UpdateSite = 4,
            DeleteSite = 8,
            ViewCompany = 16,
            CreateCompany = 32,
            UpdateCompany = 64,
            DeleteCompany = 128
        }

        // User permission set containing all resource permissions
        public class UserPermissions
        {
            public WorkerPermissions Workers { get; set; } = WorkerPermissions.None;
            public SitePermissions Sites { get; set; } = SitePermissions.None;
            public TrainingPermissions Trainings { get; set; } = TrainingPermissions.None;
            public DocumentPermissions Documents { get; set; } = DocumentPermissions.None;
            public PPEPermissions PPE { get; set; } = PPEPermissions.None;
            public RoleManagementPermissions RoleManagement { get; set; } = RoleManagementPermissions.None;
            public ReportPermissions Reports { get; set; } = ReportPermissions.None;
        }

        // Predefined role permission sets
        public static class RolePermissions
        {
            public static readonly UserPermissions SiteEngineer = new UserPermissions
            {
                Workers = WorkerPermissions.ViewSite | WorkerPermissions.UpdateSite,
                Sites = SitePermissions.ViewSite | SitePermissions.UpdateSite,
                Trainings = TrainingPermissions.ViewSite | TrainingPermissions.CreateSite | TrainingPermissions.UpdateSite,
                Documents = DocumentPermissions.ViewSite | DocumentPermissions.CreateSite | DocumentPermissions.UpdateSite,
                PPE = PPEPermissions.ViewSite | PPEPermissions.UpdateSite,
                RoleManagement = RoleManagementPermissions.None,
                Reports = ReportPermissions.ViewSite | ReportPermissions.CreateSite
            };

            public static readonly UserPermissions SiteHSE = new UserPermissions
            {
                Workers = WorkerPermissions.ViewSite | WorkerPermissions.CreateSite | WorkerPermissions.UpdateSite,
                Sites = SitePermissions.ViewSite | SitePermissions.UpdateSite,
                Trainings = TrainingPermissions.ViewSite | TrainingPermissions.CreateSite | TrainingPermissions.UpdateSite | TrainingPermissions.DeleteSite,
                Documents = DocumentPermissions.ViewSite | DocumentPermissions.CreateSite | DocumentPermissions.UpdateSite | DocumentPermissions.DeleteSite,
                PPE = PPEPermissions.ViewSite | PPEPermissions.CreateSite | PPEPermissions.UpdateSite | PPEPermissions.DeleteSite,
                RoleManagement = RoleManagementPermissions.None,
                Reports = ReportPermissions.ViewSite | ReportPermissions.CreateSite | ReportPermissions.UpdateSite
            };

            public static readonly UserPermissions SiteManager = new UserPermissions
            {
                Workers = WorkerPermissions.ViewSite | WorkerPermissions.CreateSite | WorkerPermissions.UpdateSite | WorkerPermissions.DeleteSite,
                Sites = SitePermissions.ViewSite | SitePermissions.CreateSite | SitePermissions.UpdateSite | SitePermissions.DeleteSite,
                Trainings = TrainingPermissions.ViewSite | TrainingPermissions.CreateSite | TrainingPermissions.UpdateSite | TrainingPermissions.DeleteSite,
                Documents = DocumentPermissions.ViewSite | DocumentPermissions.CreateSite | DocumentPermissions.UpdateSite | DocumentPermissions.DeleteSite,
                PPE = PPEPermissions.ViewSite | PPEPermissions.CreateSite | PPEPermissions.UpdateSite | PPEPermissions.DeleteSite,
                RoleManagement = RoleManagementPermissions.ViewSite | RoleManagementPermissions.CreateSite | RoleManagementPermissions.UpdateSite,
                Reports = ReportPermissions.ViewSite | ReportPermissions.CreateSite | ReportPermissions.UpdateSite | ReportPermissions.DeleteSite
            };

            public static readonly UserPermissions MainHSE = new UserPermissions
            {
                Workers = WorkerPermissions.ViewCompany | WorkerPermissions.CreateCompany | WorkerPermissions.UpdateCompany,
                Sites = SitePermissions.ViewCompany | SitePermissions.UpdateCompany,
                Trainings = TrainingPermissions.ViewCompany | TrainingPermissions.CreateCompany | TrainingPermissions.UpdateCompany | TrainingPermissions.DeleteCompany,
                Documents = DocumentPermissions.ViewCompany | DocumentPermissions.CreateCompany | DocumentPermissions.UpdateCompany | DocumentPermissions.DeleteCompany,
                PPE = PPEPermissions.ViewCompany | PPEPermissions.CreateCompany | PPEPermissions.UpdateCompany | PPEPermissions.DeleteCompany,
                RoleManagement = RoleManagementPermissions.None,
                Reports = ReportPermissions.ViewCompany | ReportPermissions.CreateCompany | ReportPermissions.UpdateCompany
            };

            public static readonly UserPermissions LineManager = new UserPermissions
            {
                Workers = WorkerPermissions.ViewCompany,
                Sites = SitePermissions.ViewCompany,
                Trainings = TrainingPermissions.ViewCompany,
                Documents = DocumentPermissions.ViewCompany,
                PPE = PPEPermissions.ViewCompany,
                RoleManagement = RoleManagementPermissions.ViewCompany,
                Reports = ReportPermissions.ViewCompany
            };

            public static readonly UserPermissions Accountant = new UserPermissions
            {
                Workers = WorkerPermissions.ViewCompany,
                Sites = SitePermissions.ViewCompany,
                Trainings = TrainingPermissions.ViewCompany,
                Documents = DocumentPermissions.ViewCompany,
                PPE = PPEPermissions.None,
                RoleManagement = RoleManagementPermissions.None,
                Reports = ReportPermissions.ViewCompany | ReportPermissions.CreateCompany | ReportPermissions.UpdateCompany
            };

            public static readonly UserPermissions Admin = new UserPermissions
            {
                Workers = (WorkerPermissions)255,  // All permissions
                Sites = (SitePermissions)255,
                Trainings = (TrainingPermissions)255,
                Documents = (DocumentPermissions)255,
                PPE = (PPEPermissions)255,
                RoleManagement = (RoleManagementPermissions)255,
                Reports = (ReportPermissions)255
            };
        }

        // Helper methods for permission checking
        public static class PermissionChecker
        {
            // Check specific resource permission
            public static bool HasWorkerPermission(UserPermissions userPermissions, WorkerPermissions requiredPermission)
            {
                return (userPermissions.Workers & requiredPermission) == requiredPermission;
            }

            public static bool HasSitePermission(UserPermissions userPermissions, SitePermissions requiredPermission)
            {
                return (userPermissions.Sites & requiredPermission) == requiredPermission;
            }

            public static bool HasTrainingPermission(UserPermissions userPermissions, TrainingPermissions requiredPermission)
            {
                return (userPermissions.Trainings & requiredPermission) == requiredPermission;
            }

            public static bool HasDocumentPermission(UserPermissions userPermissions, DocumentPermissions requiredPermission)
            {
                return (userPermissions.Documents & requiredPermission) == requiredPermission;
            }

            public static bool HasPPEPermission(UserPermissions userPermissions, PPEPermissions requiredPermission)
            {
                return (userPermissions.PPE & requiredPermission) == requiredPermission;
            }

            public static bool HasRoleManagementPermission(UserPermissions userPermissions, RoleManagementPermissions requiredPermission)
            {
                return (userPermissions.RoleManagement & requiredPermission) == requiredPermission;
            }

            public static bool HasReportPermission(UserPermissions userPermissions, ReportPermissions requiredPermission)
            {
                return (userPermissions.Reports & requiredPermission) == requiredPermission;
            }

            // Convenience methods for common permission checks
            public static bool CanViewWorkers(UserPermissions userPermissions, bool isCompanyLevel = false)
            {
                var requiredPermission = isCompanyLevel ? WorkerPermissions.ViewCompany : WorkerPermissions.ViewSite;
                return HasWorkerPermission(userPermissions, requiredPermission);
            }

            public static bool CanDeleteWorkers(UserPermissions userPermissions, bool isCompanyLevel = false)
            {
                var requiredPermission = isCompanyLevel ? WorkerPermissions.DeleteCompany : WorkerPermissions.DeleteSite;
                return HasWorkerPermission(userPermissions, requiredPermission);
            }

            public static bool CanManageTrainings(UserPermissions userPermissions, bool isCompanyLevel = false)
            {
                var viewPermission = isCompanyLevel ? TrainingPermissions.ViewCompany : TrainingPermissions.ViewSite;
                var createPermission = isCompanyLevel ? TrainingPermissions.CreateCompany : TrainingPermissions.CreateSite;
                var updatePermission = isCompanyLevel ? TrainingPermissions.UpdateCompany : TrainingPermissions.UpdateSite;

                return HasTrainingPermission(userPermissions, viewPermission) &&
                       HasTrainingPermission(userPermissions, createPermission) &&
                       HasTrainingPermission(userPermissions, updatePermission);
            }

            // Generic permission checker
            public static bool HasPermission<T>(T userPermissions, T requiredPermission) where T : Enum
            {
                var userValue = Convert.ToInt32(userPermissions);
                var requiredValue = Convert.ToInt32(requiredPermission);
                return (userValue & requiredValue) == requiredValue;
            }
        }
    }
}
