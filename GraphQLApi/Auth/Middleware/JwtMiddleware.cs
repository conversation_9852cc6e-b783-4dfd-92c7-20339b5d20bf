using System.Security.Claims;
using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Auth.Middleware
{
    public class JwtMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<JwtMiddleware> _logger;

        public JwtMiddleware(RequestDelegate next, ILogger<JwtMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IJwtService jwtService, IDbContextFactory<AppDbContext> dbContextFactory)
        {
            var token = ExtractTokenFromRequest(context);

            if (!string.IsNullOrEmpty(token))
            {
                await AttachUserToContextAsync(context, token, jwtService, dbContextFactory);
            }

            await _next(context);
        }

        private static string? ExtractTokenFromRequest(HttpContext context)
        {
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();

            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                return authHeader.Substring("Bearer ".Length).Trim();
            }

            // Also check for token in cookies for web clients
            return context.Request.Cookies["access_token"];
        }

        private async Task AttachUserToContextAsync(
            HttpContext context,
            string token,
            IJwtService jwtService,
            IDbContextFactory<AppDbContext> dbContextFactory)
        {
            try
            {
                var principal = await jwtService.ValidateTokenAsync(token);
                if (principal == null)
                {
                    _logger.LogWarning("Invalid JWT token provided");
                    return;
                }

                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!int.TryParse(userIdClaim, out var userId))
                {
                    _logger.LogWarning("Invalid user ID in JWT token");
                    return;
                }

                using var dbContext = await dbContextFactory.CreateDbContextAsync();
                var user = await dbContext.Users
                    .Include(u => u.Role)
                    .Include(u => u.Tenant)
                    .FirstOrDefaultAsync(u => u.Id == userId &&
                                            u.Status == UserStatus.Active &&
                                            !u.IsDeleted &&
                                            (!u.LockedOutUntil.HasValue || u.LockedOutUntil <= DateTime.UtcNow));

                if (user == null)
                {
                    _logger.LogWarning("User {UserId} not found or inactive", userId);
                    return;
                }

                // Convert to User for backward compatibility
                var compatUser = user;
                
                // Update last activity
                await UpdateUserActivityAsync(dbContext, compatUser, context);

                // Set the user in context
                context.User = principal;
                context.Items["User"] = compatUser;
                context.Items["UserId"] = userId;
                context.Items["TenantId"] = user.TenantId;
                context.Items["RoleId"] = user.RoleId;

                _logger.LogDebug("User {UserId} authenticated successfully", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing JWT token");
            }
        }

        private async Task UpdateUserActivityAsync(AppDbContext dbContext, ApplicationUser user, HttpContext context)
        {
            try
            {
                var ipAddress = GetClientIpAddress(context);

                // Update user's last activity
                user.LastLoginAt = DateTime.UtcNow;
                user.LastLoginIp = ipAddress;
                user.UpdatedAt = DateTime.UtcNow;
                user.UpdatedBy = user.Email;

                // Update or create session
                var sessionId = context.Request.Headers["X-Session-Id"].FirstOrDefault() ??
                               context.Request.Cookies["session_id"];

                if (!string.IsNullOrEmpty(sessionId))
                {
                    var session = await dbContext.UserSessions
                        .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.ApplicationUserId == user.Id);

                    if (session != null && session.IsValid)
                    {
                        session.LastActivityAt = DateTime.UtcNow;
                        session.UpdatedAt = DateTime.UtcNow;
                    }
                }

                await dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user activity for user {UserId}", user.Id);
            }
        }

        private static string GetClientIpAddress(HttpContext context)
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }
            return ipAddress ?? "Unknown";
        }

    }
}
