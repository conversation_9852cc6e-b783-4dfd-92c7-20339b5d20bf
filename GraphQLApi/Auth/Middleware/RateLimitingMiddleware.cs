using Microsoft.Extensions.Caching.Memory;
using System.Net;

namespace GraphQLApi.Auth.Middleware
{
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingMiddleware> _logger;

        // Rate limiting configuration
        private readonly Dictionary<string, RateLimitRule> _rules = new()
        {
            { "/graphql", new RateLimitRule { MaxRequests = 100, WindowMinutes = 1 } },
            { "/auth/login", new RateLimitRule { MaxRequests = 5, WindowMinutes = 15 } },
            { "/auth/register", new RateLimitRule { MaxRequests = 3, WindowMinutes = 60 } },
            { "/auth/refresh", new RateLimitRule { MaxRequests = 10, WindowMinutes = 1 } }
        };

        public RateLimitingMiddleware(RequestDelegate next, IMemoryCache cache, ILogger<RateLimitingMiddleware> logger)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var endpoint = GetEndpoint(context);
            if (endpoint != null && _rules.TryGetValue(endpoint, out var rule))
            {
                var clientId = GetClientIdentifier(context);
                var key = $"rate_limit_{endpoint}_{clientId}";

                var requestCount = _cache.Get<int>(key);
                if (requestCount >= rule.MaxRequests)
                {
                    _logger.LogWarning("Rate limit exceeded for {ClientId} on {Endpoint}. Count: {Count}", 
                        clientId, endpoint, requestCount);

                    context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                    context.Response.Headers.Add("Retry-After", (rule.WindowMinutes * 60).ToString());
                    await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                    return;
                }

                // Increment counter
                var newCount = requestCount + 1;
                var expiry = TimeSpan.FromMinutes(rule.WindowMinutes);
                _cache.Set(key, newCount, expiry);

                // Add rate limit headers
                context.Response.Headers.Add("X-RateLimit-Limit", rule.MaxRequests.ToString());
                context.Response.Headers.Add("X-RateLimit-Remaining", (rule.MaxRequests - newCount).ToString());
                context.Response.Headers.Add("X-RateLimit-Reset", DateTimeOffset.UtcNow.Add(expiry).ToUnixTimeSeconds().ToString());
            }

            await _next(context);
        }

        private string? GetEndpoint(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLowerInvariant();
            
            // Check for exact matches first
            if (path != null && _rules.ContainsKey(path))
                return path;

            // Check for GraphQL endpoint
            if (path == "/graphql" || path == "/api/graphql")
                return "/graphql";

            // Check for auth endpoints based on GraphQL mutations
            if (path == "/graphql" && context.Request.Method == "POST")
            {
                // This is a simplified check - in a real implementation you might want to
                // parse the GraphQL query to determine the specific operation
                return "/graphql";
            }

            return null;
        }

        private string GetClientIdentifier(HttpContext context)
        {
            // Try to get user ID if authenticated
            var userId = context.User?.FindFirst("sub")?.Value;
            if (!string.IsNullOrEmpty(userId))
                return $"user_{userId}";

            // Fall back to IP address
            var ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            
            // Check for forwarded IP (if behind proxy)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                ipAddress = forwardedFor.Split(',')[0].Trim();
            }

            return $"ip_{ipAddress}";
        }
    }

    public class RateLimitRule
    {
        public int MaxRequests { get; set; }
        public int WindowMinutes { get; set; }
    }

    public static class RateLimitingMiddlewareExtensions
    {
        public static IApplicationBuilder UseRateLimiting(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RateLimitingMiddleware>();
        }
    }
}
