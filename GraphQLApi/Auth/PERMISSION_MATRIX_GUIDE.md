# Permission Matrix Guide

## Overview

This document describes the comprehensive permission matrix implemented for the workforce management system. The permission system uses **separate bitwise sequences for each resource type**, allowing granular control such as "delete workers at site level but not company level".

## Permission Structure

### Separate Resource Categories
- **Each resource has its own independent bitwise sequence**
- **Workers**, **Sites**, **Trainings**, **Documents**, **PPE**, **Role Management**, and **Reports** are completely separate
- This allows fine-grained permission control per resource type
- You can assign specific permissions like "delete workers at site level" without affecting other resources

### Bitwise Implementation
- Each resource uses values 1, 2, 4, 8 for site-level operations (View, Create, Update, Delete)
- Each resource uses values 16, 32, 64, 128 for company-level operations (View, Create, Update, Delete)
- Permissions can be combined using bitwise OR operations
- Permission checking uses bitwise AND operations
- This allows for efficient storage and fast permission checks

### Resource Categories

The permission matrix covers the following **separate** resource types:

1. **WorkerPermissions** - Employee management and workforce operations
2. **SitePermissions** - Site information and management
3. **TrainingPermissions** - Training programs and certifications
4. **DocumentPermissions** - Document management and file operations
5. **PPEPermissions** - Personal Protective Equipment inventory
6. **RoleManagementPermissions** - User roles and permission management
7. **ReportPermissions** - Report creation and management

### Permission Levels

Each resource has permissions at two organizational levels:

- **Site Level**: Operations limited to a specific construction site (values 1-8)
- **Company Level**: Operations across all sites within the company (values 16-128)

### CRUD Operations

For each resource and level combination, four standard operations are defined:

- **View/Read**: Permission to view and read data (1 for site, 16 for company)
- **Create**: Permission to create new records (2 for site, 32 for company)
- **Update**: Permission to modify existing records (4 for site, 64 for company)
- **Delete**: Permission to remove records (8 for site, 128 for company)

## Permission Values (Each Resource Independent)

### All Resources Follow This Pattern:
- **Site Level**:
  - View = 1, Create = 2, Update = 4, Delete = 8
- **Company Level**:
  - View = 16, Create = 32, Update = 64, Delete = 128

### Examples:
- **WorkerPermissions**: Site View=1, Site Delete=8, Company View=16, Company Delete=128
- **TrainingPermissions**: Site View=1, Site Delete=8, Company View=16, Company Delete=128
- **DocumentPermissions**: Site View=1, Site Delete=8, Company View=16, Company Delete=128
- And so on for all resource types...

## Predefined Roles

### Site Engineer
**Scope**: Site-level operations with limited permissions
**Permissions**:
- View and update workers at site level
- View and update site information
- View, create, and update trainings
- View, create, and update documents
- View and update PPE inventory
- View and create reports

### Site HSE Officer
**Scope**: Site-level safety and compliance operations
**Permissions**:
- View, create, and update workers
- View and update site information
- Full CRUD on trainings (including delete)
- Full CRUD on documents (including delete)
- Full CRUD on PPE (including delete)
- View, create, and update reports

### Site Manager
**Scope**: Full site-level management
**Permissions**:
- Full CRUD on all site-level resources
- Limited role management at site level
- Cannot delete reports (view, create, update only)

### Main HSE
**Scope**: Company-wide safety and compliance
**Permissions**:
- Company-level view, create, update for workers
- Company-level view and update for sites
- Full company-level CRUD for trainings, docs, and PPE
- Company-level view, create, update for reports

### Line Manager
**Scope**: Company-wide read-only access
**Permissions**:
- View-only access to all company-level resources

### Accountant
**Scope**: Financial reporting and limited company access
**Permissions**:
- View access to workers, sites, trainings, docs
- View, create, and update reports for financial analysis

### Admin
**Scope**: Full system access
**Permissions**: All permissions (represented by "*")

## Usage Examples

### C# Implementation

```csharp
// Check if user has permission
bool canViewWorkers = Permissions.HasPermission(userPermissions, PermissionFlags.ViewWorkersSite);

// Check multiple permissions
bool canManageTraining = Permissions.HasAllPermissions(userPermissions, 
    PermissionFlags.ViewTrainingsSite, 
    PermissionFlags.CreateTrainingsSite, 
    PermissionFlags.UpdateTrainingsSite);

// Assign role permissions
var siteEngineerPermissions = Permissions.RolePermissions.SiteEngineer;
```

### GraphQL Authorization

```csharp
[Authorize]
[RequirePermission(PermissionFlags.ViewWorkersSite)]
public async Task<List<Worker>> GetSiteWorkers(int siteId)
{
    // Implementation
}
```

## Security Considerations

1. **Principle of Least Privilege**: Users should only have the minimum permissions necessary for their role
2. **Site Isolation**: Site-level permissions should be restricted to specific sites
3. **Audit Trail**: All permission changes should be logged
4. **Regular Review**: Permissions should be reviewed regularly and updated as needed

## Migration Notes

When migrating from the old permission system:
1. Map existing permissions to new bitwise values
2. Update all authorization checks to use new permission flags
3. Test thoroughly to ensure no functionality is broken
4. Update user roles to use predefined role templates

## Future Enhancements

1. **Dynamic Permissions**: Allow runtime permission configuration
2. **Time-based Permissions**: Implement temporary permissions with expiration
3. **Conditional Permissions**: Add context-aware permission checking
4. **Permission Inheritance**: Implement hierarchical permission inheritance
