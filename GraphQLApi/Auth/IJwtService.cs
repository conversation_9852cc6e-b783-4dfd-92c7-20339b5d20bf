using System.Security.Claims;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Auth
{
    public interface IJwtService
    {
        Task<string> GenerateAccessTokenAsync(ApplicationUser user);
        Task<RefreshToken> GenerateRefreshTokenAsync(ApplicationUser user, string ipAddress, string userAgent);
        Task<ClaimsPrincipal?> ValidateTokenAsync(string token);
        Task<bool> ValidateRefreshTokenAsync(string token);
        Task<RefreshToken?> GetRefreshTokenAsync(string token);
        Task RevokeRefreshTokenAsync(string token, string reason);
        Task RevokeAllUserTokensAsync(int userId, string reason);
        Task CleanupExpiredTokensAsync();
        string? GetUserIdFromToken(string token);
        string? GetJwtIdFromToken(string token);
        bool IsTokenExpired(string token);
    }
}
