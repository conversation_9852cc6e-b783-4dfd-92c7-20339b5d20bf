{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=WorkforceManagementNew;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "HikvisionApi": {"BaseUrl": "https://your-hikvision-api-url", "ApiKey": "your-api-key-here"}, "PhotoStorage": {"LocalPath": "wwwroot/photos"}, "Resend": {"ApiKey": "re_ZFwQmuPR_5FE1ciLtocdbHkavXe9H9vDb", "FromEmail": "<EMAIL>", "BaseUrl": "https://api.resend.com"}, "Notifications": {"InAppEnabled": true, "EmailEnabled": true, "SmsEnabled": false, "NotificationRules": [{"Entity": "Training", "Operation": "Modified", "Type": "training_status_changed", "TitleTemplate": "Training Status Changed", "MessageTemplate": "Training '{{entity}}' status has been changed to {{operation}}", "Fields": ["Status"], "Condition": {"Field": "Status", "EqualTo": "Expired"}, "Channels": ["InApp", "Email"], "Recipients": ["role:<PERSON><PERSON>", "role:Manager"], "Priority": "High"}, {"Entity": "Worker", "Operation": "Added", "Type": "worker_added", "TitleTemplate": "New Worker Added", "MessageTemplate": "A new worker has been added to the system", "Channels": ["InApp"], "Recipients": ["role:<PERSON><PERSON>", "role:HR"], "Priority": "Medium"}, {"Entity": "Task", "Operation": "Modified", "Type": "task_overdue", "TitleTemplate": "Task Overdue", "MessageTemplate": "Task '{{entity}}' is now overdue", "Fields": ["Status"], "Condition": {"Field": "Status", "EqualTo": "Overdue"}, "Channels": ["InApp", "Email"], "Recipients": ["role:<PERSON><PERSON>", "role:Manager"], "Priority": "Critical"}]}, "MinIO": {"Endpoint": "localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "UseSSL": false, "Region": "us-east-1", "Buckets": {"ProfilePicture": "profile-picture", "Certification": "certification", "Signatures": "signatures", "Temp": "temp", "Docs": "docs", "Inspections": "inspections"}, "Settings": {"MaxFileSize": 52428800, "DefaultExpiration": 7, "EnableVersioning": true, "AutoCreateBuckets": true}}, "JwtSettings": {"SecretKey": "my-super-secret-key-that-is-long-enough-for-jwt-hs256-algorithm-requirements", "Issuer": "WorkforceAPI", "Audience": "WorkforceApp", "AccessTokenExpirationMinutes": 30, "RefreshTokenExpirationDays": 7, "SessionTimeoutMinutes": 60, "RequireHttpsMetadata": false, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewSeconds": 5, "MaxConcurrentSessions": 3, "EnableSessionTracking": true, "EnableAuditLogging": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}