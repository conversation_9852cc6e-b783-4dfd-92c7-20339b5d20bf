using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Configuration;
using Shared.DTOs;

namespace GraphQLApi.Data.Notifications
{
    public sealed class NotificationSaveChangesInterceptor : SaveChangesInterceptor
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly NotificationOptions _options;
        private readonly ILogger<NotificationSaveChangesInterceptor> _logger;

        public NotificationSaveChangesInterceptor(
            IServiceScopeFactory serviceScopeFactory,
            IOptions<NotificationOptions> options,
            ILogger<NotificationSaveChangesInterceptor> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _options = options.Value;
            _logger = logger;
        }

        public override ValueTask<int> SavedChangesAsync(SaveChangesCompletedEventData eventData, int result, CancellationToken cancellationToken = default)
        {
            try
            {
                var ctx = eventData.Context;
                if (ctx == null || result <= 0) return base.SavedChangesAsync(eventData, result, cancellationToken);

                var entries = ctx.ChangeTracker.Entries()
                    .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified || e.State == EntityState.Deleted)
                    .ToList();

                if (_options.NotificationRules?.Count > 0)
                {
                    // Create a scope to resolve scoped services
                    using var scope = _serviceScopeFactory.CreateScope();
                    var notificationService = scope.ServiceProvider.GetRequiredService<Services.INotificationService>();

                    foreach (var entry in entries)
                    {
                        var entityName = entry.Entity.GetType().Name;
                        var operation = entry.State.ToString();

                        foreach (var rule in _options.NotificationRules)
                        {
                            if (!string.Equals(rule.Entity, entityName, StringComparison.OrdinalIgnoreCase)) continue;
                            if (!string.Equals(rule.Operation, operation, StringComparison.OrdinalIgnoreCase)) continue;

                            if (entry.State == EntityState.Modified && rule.Fields is { Count: > 0 })
                            {
                                if (!rule.Fields.Any(f => entry.Property(f)?.IsModified == true)) continue;
                            }

                            if (rule.Condition is not null)
                            {
                                var prop = entry.Property(rule.Condition.Field);
                                if (prop == null) continue;
                                var current = prop.CurrentValue?.ToString();
                                if (rule.Condition.EqualTo is not null && !string.Equals(current, rule.Condition.EqualTo, StringComparison.OrdinalIgnoreCase)) continue;
                                if (rule.Condition.NotEqualTo is not null && string.Equals(current, rule.Condition.NotEqualTo, StringComparison.OrdinalIgnoreCase)) continue;
                            }

                            var metadata = new Dictionary<string, string>
                            {
                                ["entity"] = entityName,
                                ["operation"] = operation
                            };

                            var title = rule.TitleTemplate.Replace("{{entity}}", entityName).Replace("{{operation}}", operation);
                            var message = rule.MessageTemplate.Replace("{{entity}}", entityName).Replace("{{operation}}", operation);

                            _ = notificationService.PublishAsync(new NotificationEvent(
                                rule.Type ?? $"{entityName}_{operation}",
                                title,
                                message,
                                entityName,
                                operation,
                                metadata
                            ), cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Notification interceptor error");
            }

            return base.SavedChangesAsync(eventData, result, cancellationToken);
        }
    }
}

