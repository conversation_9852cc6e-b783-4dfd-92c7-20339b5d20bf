using Microsoft.EntityFrameworkCore;

namespace GraphQLApi.Data
{
    /// <summary>
    /// Manual DbContext factory implementation to avoid service lifetime conflicts
    /// </summary>
    public class ManualDbContextFactory : IDbContextFactory<AppDbContext>
    {
        private readonly DbContextOptions<AppDbContext> _options;
        private readonly IServiceProvider _serviceProvider;

        public ManualDbContextFactory(DbContextOptions<AppDbContext> options, IServiceProvider serviceProvider)
        {
            _options = options;
            _serviceProvider = serviceProvider;
        }

        public AppDbContext CreateDbContext()
        {
            return new AppDbContext(_options);
        }
    }
}
