using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Auth;
using Shared.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace GraphQLApi.Data
{
    public static class AuthSeedData
    {
        public static async Task SeedAsync(AppDbContext context, IServiceProvider? serviceProvider = null)
        {
            var logger = serviceProvider?.GetService<ILogger<Program>>();
            
            try
            {
                // 1. Create default tenant if it doesn't exist
                // Create default tenant if it doesn't exist
                var defaultTenant = await context.Tenants.FirstOrDefaultAsync(t => t.Subdomain == "default");
                if (defaultTenant == null)
                {
                    // Create default tenant with auto-generated ID
                    defaultTenant = new Tenant
                    {
                        Name = "Default Organization",
                        Subdomain = "default",
                        Status = TenantStatus.Active,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System"
                    };

                    context.Tenants.Add(defaultTenant);
                    await context.SaveChangesAsync();
                    
                    // Refresh the entity from database to get the generated ID
                    await context.Entry(defaultTenant).ReloadAsync();
                    logger?.LogInformation("✅ Created default tenant");
                }

                // 2. Create minimal legacy role if needed (for backward compatibility)
                var legacyAdminRole = await context.CustomRoles
                    .FirstOrDefaultAsync(r => r.Name == "Super Administrator" && r.TenantId == defaultTenant.Id);
                    
                if (legacyAdminRole == null)
                {
                    // Create legacy admin role with auto-generated ID
                    legacyAdminRole = new Role
                    {
                        Name = "Super Administrator",
                        Description = "Full system access with all permissions",
                        IsSystemRole = true,
                        // Set all permissions (255 = all bits set, giving all permissions)
                        WorkerPermissions = 255,
                        SitePermissions = 255,
                        TrainingPermissions = 255,
                        DocumentPermissions = 255,
                        PPEPermissions = 255,
                        RoleManagementPermissions = 255,
                        ReportPermissions = 255,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System"
                    };
                    
                    legacyAdminRole.TenantId = defaultTenant.Id;

                    context.CustomRoles.Add(legacyAdminRole);
                    await context.SaveChangesAsync();
                    
                    // Refresh the entity from database to get the generated ID
                    await context.Entry(legacyAdminRole).ReloadAsync();
                    logger?.LogInformation("✅ Created legacy admin role");
                }

                // 3. Create admin user with Identity if it doesn't exist
                if (serviceProvider != null)
                {
                    var userManager = serviceProvider.GetService<UserManager<ApplicationUser>>();
                    var roleManager = serviceProvider.GetService<RoleManager<ApplicationRole>>();
                    
                    if (userManager != null && roleManager != null)
                    {
                        // Create SuperAdmin role if it doesn't exist
                        if (!await roleManager.RoleExistsAsync("SuperAdmin"))
                        {
                            var role = new ApplicationRole
                    {
                        Name = "SuperAdmin",
                        NormalizedName = "SUPERADMIN",
                        TenantId = defaultTenant.Id,
                        Description = "Super Administrator",
                        IsSystemRole = true
                    };
                            await roleManager.CreateAsync(role);
                            logger?.LogInformation("✅ Created SuperAdmin role");
                        }

                        // Create admin user if it doesn't exist
                        var adminEmail = "<EMAIL>";
                        var existingAdmin = await userManager.FindByEmailAsync(adminEmail);
                        
                        if (existingAdmin == null)
                        {
                            var adminUser = new ApplicationUser
                            {
                                Email = adminEmail,
                                NormalizedEmail = adminEmail.ToUpperInvariant(),
                                UserName = adminEmail,
                                NormalizedUserName = adminEmail.ToUpperInvariant(),
                                FirstName = "System",
                                LastName = "Administrator",
                                EmailConfirmed = true,
                                LockoutEnabled = false,
                                TenantId = defaultTenant.Id,
                                Status = Shared.GraphQL.Models.Auth.UserStatus.Active,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System"
                            };

                            var result = await userManager.CreateAsync(adminUser, "AdminPassword@123!");
                            if (result.Succeeded)
                            {
                                // Add to SuperAdmin identity role
                                await userManager.AddToRoleAsync(adminUser, "SuperAdmin");
                                
                                // Update the RoleId after user is created
                                adminUser.RoleId = legacyAdminRole.Id;
                                await userManager.UpdateAsync(adminUser);
                                
                                logger?.LogInformation("✅ Created admin user: {Email}", adminEmail);
                            }
                            else
                            {
                                logger?.LogError("❌ Failed to create admin user: {Errors}", 
                                    string.Join(", ", result.Errors.Select(e => e.Description)));
                            }
                        }
                        else
                        {
                            // Update existing admin user to ensure it's properly configured
                            if (existingAdmin.LockoutEnabled)
                            {
                                existingAdmin.LockoutEnabled = false;
                                await userManager.UpdateAsync(existingAdmin);
                                logger?.LogInformation("✅ Updated admin user lockout settings: {Email}", adminEmail);
                            }
                            
                            // Ensure the user has the legacy role
                            if (existingAdmin.RoleId != legacyAdminRole.Id)
                            {
                                existingAdmin.Role = legacyAdminRole;
                                existingAdmin.RoleId = legacyAdminRole.Id;
                                await userManager.UpdateAsync(existingAdmin);
                                logger?.LogInformation("✅ Updated admin user role: {Email}", adminEmail);
                            }
                        }
                    }
                }

                logger?.LogInformation("🎉 Authentication seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "❌ Authentication seeding failed");
                throw;
            }
        }
    }
}