using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    /// <summary>
    /// Entity configuration for WorkerTraining join table
    /// </summary>
    public class WorkerTrainingConfiguration : IEntityTypeConfiguration<WorkerTraining>
    {
        public void Configure(EntityTypeBuilder<WorkerTraining> builder)
        {
            builder.ToTable("WorkerTraining");

            // Composite primary key
            builder.HasKey(wt => new { wt.WorkerId, wt.TrainingId });

            // Foreign key relationships
            builder.HasOne(wt => wt.Worker)
                .WithMany(w => w.WorkerTrainings)
                .HasForeignKey(wt => wt.WorkerId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(wt => wt.Training)
                .WithMany(t => t.WorkerTrainings)
                .HasForeignKey(wt => wt.TrainingId)
                .OnDelete(DeleteBehavior.NoAction);

            // Properties
            builder.Property(wt => wt.Notes)
                .HasMaxLength(1000);

            builder.Property(wt => wt.AssignedDate)
                .IsRequired()
                .HasColumnType("datetime2");

            // Audit fields
            builder.Property(wt => wt.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(wt => wt.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(wt => wt.UpdatedAt)
                .HasColumnType("datetime2");

            builder.Property(wt => wt.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(wt => wt.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(wt => wt.DeletedAt)
                .HasColumnType("datetime2");

            builder.Property(wt => wt.DeletedBy)
                .HasMaxLength(100);
                        builder.HasMany(p => p.DocumentFiles)
                .WithMany()
                .UsingEntity("WorkerTrainingDocuments");
            // Indexes for performance
            builder.HasIndex(wt => wt.WorkerId)
                .HasDatabaseName("IX_WorkerTraining_WorkerId");

            builder.HasIndex(wt => wt.TrainingId)
                .HasDatabaseName("IX_WorkerTraining_TrainingId");

            builder.HasIndex(wt => wt.AssignedDate)
                .HasDatabaseName("IX_WorkerTraining_AssignedDate");

            builder.HasIndex(wt => wt.IsDeleted)
                .HasDatabaseName("IX_WorkerTraining_IsDeleted");

            builder.HasIndex(wt => wt.CreatedAt)
                .HasDatabaseName("IX_WorkerTraining_CreatedAt");
        }
    }
}
