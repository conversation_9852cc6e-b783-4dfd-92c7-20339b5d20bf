using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;
using System.Text.Json;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace GraphQLApi.Data.Configurations
{
    public class JobConfiguration : IEntityTypeConfiguration<Job>
    {
        public void Configure(EntityTypeBuilder<Job> builder)
        {
            builder.HasKey(j => j.Id);

            builder.Property(j => j.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(j => j.Description)
                .HasMaxLength(1000);

            builder.Property(j => j.Location)
                .HasMaxLength(200);

            builder.Property(j => j.PPEs)
                .HasConversion(
                    v => string.Join(",", v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .ToList()
                )
                .Metadata.SetValueComparer(
                    new ValueComparer<List<string>>(
                        (h1, h2) => (h1 == null && h2 == null) || (h1 != null && h2 != null && h1.SequenceEqual(h2)),
                        h => h == null ? 0 : h.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        v => v == null ? new List<string>() : v.ToList()
                    )
                );

            builder.Property(j => j.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(j => j.RequiredPermits)
                .HasConversion(
                    v => string.Join(",", v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(e => (PermitType)Enum.Parse(typeof(PermitType), e))
                    .ToHashSet()
                )
                .Metadata.SetValueComparer(
                    new ValueComparer<HashSet<PermitType>>(
                        (h1, h2) => (h1 == null && h2 == null) || (h1 != null && h2 != null && h1.SetEquals(h2)),
                        h => h == null ? 0 : h.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        v => v == null ? new HashSet<PermitType>() : v.ToHashSet()
                    )
                );

            builder.Property(j => j.PrecautionsRequired)
                .HasConversion(
                    v => string.Join(",", v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .ToHashSet()
                )
                .Metadata.SetValueComparer(
                    new ValueComparer<HashSet<string>>(
                        (h1, h2) => (h1 == null && h2 == null) || (h1 != null && h2 != null && h1.SetEquals(h2)),
                        h => h == null ? 0 : h.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                        v => v == null ? new HashSet<string>() : v.ToHashSet()
                    )
                );
            
            builder.Property(j => j.TimeForCompletion)
                .HasConversion<long>(
                    v => v.Ticks,
                    v => TimeSpan.FromTicks(v)
                );
            builder.Property(j => j.StartDate)
                .IsRequired();

            builder.Property(j => j.DueDate);

            builder.Property(j => j.ExcavationFields)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<ExcavationFields>(v, (JsonSerializerOptions)null!) ?? new ExcavationFields()
                )
                .HasColumnType("nvarchar(max)");

            builder.Property(j => j.HotWorkFields)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<HotWorkFields>(v, (JsonSerializerOptions)null!) ?? new HotWorkFields()
                )
                .HasColumnType("nvarchar(max)");

            builder.Property(j => j.WorkAtHeightFields)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null!),
                    v => JsonSerializer.Deserialize<WorkAtHeightFields>(v, (JsonSerializerOptions)null!) ?? new WorkAtHeightFields()
                )
                .HasColumnType("nvarchar(max)");

            // Configure relationships
            builder.HasOne(j => j.Category)
                .WithMany(c => c.Jobs)
                .HasForeignKey(j => j.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            // Use NoAction to avoid multiple cascade paths with Worker table
            builder.HasOne(j => j.RequestedBy)
                .WithMany()
                .HasForeignKey(j => j.RequestedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.BlockedBy)
                .WithMany()
                .HasForeignKey(j => j.BlockedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ReviewedBy)
                .WithMany()
                .HasForeignKey(j => j.ReviewedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ApprovedBy)
                .WithMany()
                .HasForeignKey(j => j.ApprovedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.FinishedBy)
                .WithMany()
                .HasForeignKey(j => j.FinishedById)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(j => j.ChiefEngineer)
                .WithMany()
                .HasForeignKey(j => j.ChiefEngineerId)
                .OnDelete(DeleteBehavior.NoAction);

            // Many-to-many relationship with Workers
            // builder.HasMany(j => j.Workers)
            //     .WithMany()
            //     .UsingEntity("JobWorkers");
            builder.HasMany(j => j.RequiredTrades)
                .WithMany()
                .UsingEntity("JobTrades");
            
            builder.HasMany(j => j.RequiredTrainings)
                .WithMany()
                .UsingEntity("JobTrainings");

            // One-to-many relationships
            builder.HasMany(j => j.Hazards)
                .WithOne(h => h.Job)
                .HasForeignKey(h => h.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(j => j.Documents)
                .WithMany()
                .UsingEntity("JobDocuments");

            // One-to-many relationship with Permits
            builder.HasMany(j => j.Permits)
                .WithOne(p => p.Job)
                .HasForeignKey(p => p.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields
            builder.Property(j => j.CreatedAt)
                .IsRequired();

            builder.Property(j => j.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(j => j.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(j => j.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(j => j.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(j => j.Status);
            builder.HasIndex(j => j.StartDate);
            builder.HasIndex(j => j.DueDate);
            builder.HasIndex(j => j.ChiefEngineerId);
            builder.HasIndex(j => j.CategoryId);
        }
    }
}
