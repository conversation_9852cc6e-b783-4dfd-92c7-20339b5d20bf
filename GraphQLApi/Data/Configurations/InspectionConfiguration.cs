using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    /// <summary>
    /// Entity configuration for Inspection
    /// </summary>
    public class InspectionConfiguration : IEntityTypeConfiguration<Inspection>
    {
        public void Configure(EntityTypeBuilder<Inspection> builder)
        {
            builder.ToTable("Inspections");

            // Primary key
            builder.HasKey(i => i.Id);
            builder.Property(i => i.Id)
                .UseIdentityColumn();

            // Properties
            builder.Property(i => i.Approved)
                .IsRequired();

            builder.Property(i => i.Comments)
                .HasMaxLength(2000);

            // Foreign key relationships
            builder.Property(i => i.InspectedById)
                .IsRequired();

            builder.HasOne(i => i.InspectedBy)
                .WithMany()
                .HasForeignKey(i => i.InspectedById)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Property(i => i.SignatureFileId)
                .IsRequired(false);

            builder.HasOne(i => i.SignatureFile)
                .WithMany()
                .HasForeignKey(i => i.SignatureFileId)
                .OnDelete(DeleteBehavior.SetNull);

            // One-to-many relationship with InspectionItems
            builder.HasMany(i => i.InspectionItems)
                .WithOne(ii => ii.Inspection)
                .HasForeignKey(ii => ii.InspectionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields
            builder.Property(i => i.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(i => i.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(i => i.UpdatedAt)
                .HasColumnType("datetime2");

            builder.Property(i => i.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(i => i.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(i => i.DeletedAt)
                .HasColumnType("datetime2");

            builder.Property(i => i.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(i => i.InspectedById)
                .HasDatabaseName("IX_Inspections_InspectedById");

            builder.HasIndex(i => i.SignatureFileId)
                .HasDatabaseName("IX_Inspections_SignatureFileId");

            builder.HasIndex(i => i.IsDeleted)
                .HasDatabaseName("IX_Inspections_IsDeleted");

            builder.HasIndex(i => i.CreatedAt)
                .HasDatabaseName("IX_Inspections_CreatedAt");
        }
    }
}
