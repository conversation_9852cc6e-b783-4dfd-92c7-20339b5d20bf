using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingProviderConfiguration : IEntityTypeConfiguration<TrainingProvider>
    {
        public void Configure(EntityTypeBuilder<TrainingProvider> builder)
        {
            builder.ToTable("TrainingProviders");
            builder.HasQueryFilter(tp => !tp.IsDeleted);

            builder.HasKey(tp => tp.Id);
            builder.Property(tp => tp.Id).UseIdentityColumn();

            builder.Property(tp => tp.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(tp => tp.Description)
                .HasMaxLength(1000);

            builder.Property(tp => tp.Contact)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(tp => tp.Tenant)
                .WithMany()
                .HasForeignKey(tp => tp.TenantId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(tp => tp.Name);
            builder.HasIndex(tp => tp.Active);
            builder.HasIndex(tp => tp.TenantId);
        }
    }
}


