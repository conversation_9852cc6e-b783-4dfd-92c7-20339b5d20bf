using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;
using System.Text.Json;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingSessionConfiguration : IEntityTypeConfiguration<TrainingSession>
    {
        public void Configure(EntityTypeBuilder<TrainingSession> builder)
        {
            builder.ToTable("TrainingSessions");
            builder.HasQueryFilter(ts => !ts.IsDeleted);

            builder.HasKey(ts => ts.Id);
            builder.Property(ts => ts.Id).UseIdentityColumn();

            builder.Property(ts => ts.Location)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(ts => ts.Cost)
                .HasMaxLength(500);

            builder.Property(ts => ts.Notes)
                .HasMaxLength(2000);

            // JSON columns (like toolbox)
            builder.Property(ts => ts.Conductor)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<TrainingConductor>(v, (JsonSerializerOptions?)null)!)
                .HasColumnType("nvarchar(max)");

            // Relationships
            builder.HasOne(ts => ts.Program)
                .WithMany(p => p.Sessions)
                .HasForeignKey(ts => ts.ProgramId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ts => ts.Provider)
                .WithMany(p => p.Sessions)
                .HasForeignKey(ts => ts.ProviderId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(ts => ts.Site)
                .WithMany()
                .HasForeignKey(ts => ts.SiteId)
                .OnDelete(DeleteBehavior.SetNull);

            

            // File relationship
            builder.HasOne(ts => ts.SessionPictureFile)
                .WithMany()
                .HasForeignKey(ts => ts.SessionPictureFileId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes for better query performance
            builder.HasIndex(ts => ts.StartDate);
            builder.HasIndex(ts => ts.Status);
            builder.HasIndex(ts => new { ts.ProgramId, ts.StartDate });
            builder.HasIndex(ts => new { ts.ProviderId, ts.StartDate });
        }
    }
}


