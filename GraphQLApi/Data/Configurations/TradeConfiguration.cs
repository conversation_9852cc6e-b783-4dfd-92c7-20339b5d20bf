using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class TradeConfiguration : IEntityTypeConfiguration<Trade>
    {
        public void Configure(EntityTypeBuilder<Trade> builder)
        {
            builder.ToTable("Trades");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Description)
                .HasMaxLength(500);

            // Unique constraint on Name
            builder.HasIndex(e => e.Name)
                .IsUnique();

            // Many-to-many relationship with Workers
            builder.HasMany(e => e.Workers)
                .WithMany(w => w.Trades)
                .UsingEntity(
                    "WorkerTrade",
                    l => l.<PERSON>(typeof(Worker)).WithMany().Has<PERSON><PERSON><PERSON><PERSON><PERSON>("WorkerId").HasPrincipal<PERSON><PERSON>(nameof(Worker.Id)),
                    r => r.<PERSON>(typeof(Trade)).WithMany().HasForeignKey("TradeId").HasPrincipalKey(nameof(Trade.Id)),
                    j => j.HasKey("WorkerId", "TradeId"));
        }
    }
}
