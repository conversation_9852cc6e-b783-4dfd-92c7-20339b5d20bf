using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingEnrollmentConfiguration : IEntityTypeConfiguration<TrainingEnrollment>
    {
        public void Configure(EntityTypeBuilder<TrainingEnrollment> builder)
        {
            builder.ToTable("TrainingEnrollments");

            builder.<PERSON><PERSON>ey(te => te.Id);
            builder.Property(te => te.Id).UseIdentityColumn();

            builder.Property(te => te.Notes)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(te => te.Session)
                .WithMany(s => s.Enrollments)
                .HasForeignKey(te => te.SessionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(te => te.Worker)
                .WithMany()
                .HasForeignKey(te => te.WorkerId)
                .OnDelete(DeleteBehavior.Restrict); //TODO: CHange to cascade. 

            // Indexes
            builder.HasIndex(te => te.SessionId);
            builder.HasIndex(te => te.WorkerId);
            builder.HasIndex(te => new { te.SessionId, te.WorkerId }).IsUnique(); // One enrollment per worker per session
            builder.HasIndex(te => te.Status);
        }
    }
}


