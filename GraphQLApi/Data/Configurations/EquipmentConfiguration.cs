using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class EquipmentConfiguration : IEntityTypeConfiguration<Equipment>
    {
        public void Configure(EntityTypeBuilder<Equipment> builder)
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(e => e.Description)
                .HasMaxLength(1000);

            builder.Property(e => e.SerialNumber)
                .HasMaxLength(100);

            builder.HasIndex(e => e.SerialNumber)
                .IsUnique()
                .HasFilter("[SerialNumber] IS NOT NULL");

            builder.Property(e => e.Model)
                .HasMaxLength(100);

            builder.Property(e => e.Manufacturer)
                .HasMaxLength(100);

            builder.Property(e => e.Location)
                .HasMaxLength(200);

            builder.Property(e => e.Status)
                .HasMaxLength(50);

            builder.Property(e => e.PurchasePrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(e => e.Category)
                .HasMaxLength(100);

            // Audit fields
            builder.Property(e => e.CreatedAt)
                .IsRequired();

            builder.Property(e => e.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(e => e.DeletedBy)
                .HasMaxLength(100);
        }
    }
}
