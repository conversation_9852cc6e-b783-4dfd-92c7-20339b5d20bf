using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Data.Configurations
{
    public class IncidentConfiguration : IEntityTypeConfiguration<Incident>
    {
        public void Configure(EntityTypeBuilder<Incident> builder)
        {
            builder.ToTable("Incidents");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(e => e.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(e => e.Location)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(e => e.Status)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(e => e.ReportedBy)
                .HasMaxLength(100);

            builder.Property(e => e.InvestigatedBy)
                .HasMaxLength(100);

            builder.Property(e => e.Resolution)
                .HasMaxLength(1000);

            // Soft delete fields
            builder.Property(e => e.IsDeleted)
                .HasDefaultValue(false);

            builder.Property(e => e.DeletedBy)
                .HasMaxLength(100);

            // Many-to-many relationship with Workers
            builder.HasMany(e => e.Workers)
                .WithMany(w => w.Incidents)
                .UsingEntity(j => j.ToTable("IncidentWorkers"));

            // Indexes for better query performance
            builder.HasIndex(e => e.Status);
            builder.HasIndex(e => e.IncidentDate);
            builder.HasIndex(e => e.IsDeleted);
        }
    }
}
