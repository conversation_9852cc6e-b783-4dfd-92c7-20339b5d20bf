using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class ProviderCertificationConfiguration : IEntityTypeConfiguration<ProviderCertification>
    {
        public void Configure(EntityTypeBuilder<ProviderCertification> builder)
        {
            builder.ToTable("ProviderCertifications");
            builder.HasQueryFilter(pc => !pc.IsDeleted);

            builder.HasKey(pc => pc.Id);
            builder.Property(pc => pc.Id).UseIdentityColumn();

            builder.Property(pc => pc.CertificationType)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(pc => pc.CertificateNumber)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(pc => pc.IssuingAuthority)
                .HasMaxLength(200);

            builder.Property(pc => pc.DocumentUrl)
                .HasMaxLength(1000);

            builder.Property(pc => pc.Notes)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(pc => pc.Provider)
                .WithMany(p => p.Certifications)
                .HasForeignKey(pc => pc.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(pc => pc.CertificateFile)
                .WithMany()
                .HasForeignKey(pc => pc.CertificateFileId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(pc => pc.ProviderId);
            builder.HasIndex(pc => pc.CertificationType);
            builder.HasIndex(pc => pc.CertificateNumber);
            builder.HasIndex(pc => pc.ExpiryDate);
            builder.HasIndex(pc => pc.Active);
        }
    }
}

