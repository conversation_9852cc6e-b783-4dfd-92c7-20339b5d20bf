using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    /// <summary>
    /// Entity configuration for InspectionItem
    /// </summary>
    public class InspectionItemConfiguration : IEntityTypeConfiguration<InspectionItem>
    {
        public void Configure(EntityTypeBuilder<InspectionItem> builder)
        {
            builder.ToTable("InspectionItems");

            // Primary key
            builder.HasKey(i => i.Id);
            builder.Property(i => i.Id)
                .UseIdentityColumn();

            // Required properties
            builder.Property(i => i.Description)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(i => i.IsTrue)
                .IsRequired();

            builder.Property(i => i.Remarks)
                .HasMaxLength(1000);

            // Foreign key relationships
            builder.Property(i => i.InspectionId)
                .IsRequired();

            builder.HasOne(i => i.Inspection)
                .WithMany(ins => ins.InspectionItems)
                .HasForeignKey(i => i.InspectionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Many-to-many relationship with FileMetadata for images
            builder.HasMany(i => i.ImageFiles)
                .WithMany()
                .UsingEntity<Dictionary<string, object>>(
                    "InspectionItemImages",
                    j => j.HasOne<FileMetadata>().WithMany().HasForeignKey("FileMetadataId"),
                    j => j.HasOne<InspectionItem>().WithMany().HasForeignKey("InspectionItemId"),
                    j =>
                    {
                        j.HasKey("InspectionItemId", "FileMetadataId");
                        j.ToTable("InspectionItemImages");
                    });

            // Audit fields
            builder.Property(i => i.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(i => i.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(i => i.UpdatedAt)
                .HasColumnType("datetime2");

            builder.Property(i => i.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(i => i.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(i => i.DeletedAt)
                .HasColumnType("datetime2");

            builder.Property(i => i.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(i => i.InspectionId)
                .HasDatabaseName("IX_InspectionItems_InspectionId");

            builder.HasIndex(i => i.IsDeleted)
                .HasDatabaseName("IX_InspectionItems_IsDeleted");
        }
    }
}
