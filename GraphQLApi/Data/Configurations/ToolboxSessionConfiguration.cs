using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class ToolboxSessionConfiguration : IEntityTypeConfiguration<ToolboxSession>
    {
        public void Configure(EntityTypeBuilder<ToolboxSession> builder)
        {
            builder.ToTable("ToolboxSessions");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Topic)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(e => e.Conductor)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.PhotoUrl)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(e => e.Notes)
                .HasMaxLength(1000);

            // Indexes for better query performance
            builder.HasIndex(e => e.SessionTime);
            builder.HasIndex(e => e.Conductor);
        }
    }

    public class ToolboxAttendanceConfiguration : IEntityTypeConfiguration<ToolboxAttendance>
    {
        public void Configure(EntityTypeBuilder<ToolboxAttendance> builder)
        {
            builder.ToTable("ToolboxAttendances");
            builder.HasQueryFilter(a => !a.Worker.IsDeleted);

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Notes)
                .HasMaxLength(500);

            builder.HasOne(e => e.ToolboxSession)
                .WithMany(s => s.Attendances)
                .HasForeignKey(e => e.ToolboxSessionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(e => e.Worker)
                .WithMany()
                .HasForeignKey(e => e.WorkerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes for better query performance
            builder.HasIndex(e => e.ToolboxSessionId);
            builder.HasIndex(e => e.WorkerId);
            builder.HasIndex(e => new { e.ToolboxSessionId, e.WorkerId }).IsUnique();
        }
    }
} 