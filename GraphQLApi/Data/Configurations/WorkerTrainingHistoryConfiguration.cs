using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Data.Configurations
{
    public class WorkerTrainingHistoryConfiguration : IEntityTypeConfiguration<WorkerTrainingHistory>
    {
        public void Configure(EntityTypeBuilder<WorkerTrainingHistory> builder)
        {
            builder.ToTable("WorkerTrainingHistory");
            builder.HasQueryFilter(e => !e.Worker.IsDeleted);

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.CompletionDate)
                .IsRequired()
                .HasColumnType("datetime2");

            builder.Property(e => e.ExpiryDate)
                .HasColumnType("datetime2");

            builder.Property(e => e.Status)
                .HasConversion<string>()
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(e => e.Notes)
                .HasMaxLength(1000);

            builder.Property(e => e.CertificateUrl)
                .HasMaxLength(500);

            builder.Property(e => e.Score)
                .HasPrecision(5, 2)
                .HasColumnType("decimal(5,2)");

            // Relationships
            builder.HasOne(e => e.Worker)
                .WithMany(w => w.TrainingHistory)
                .HasForeignKey(e => e.WorkerId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(e => e.Training)
                .WithMany(t => t.TrainingHistory)
                .HasForeignKey(e => e.TrainingId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes for better query performance
            builder.HasIndex(e => e.WorkerId);
            builder.HasIndex(e => e.TrainingId);
            builder.HasIndex(e => e.CompletionDate);
            builder.HasIndex(e => e.ExpiryDate);
            builder.HasIndex(e => e.Status);
            builder.HasIndex(e => new { e.WorkerId, e.TrainingId, e.CompletionDate });
        }
    }
}
