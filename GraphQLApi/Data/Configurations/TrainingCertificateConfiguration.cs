using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingCertificateConfiguration : IEntityTypeConfiguration<TrainingCertificate>
    {
        public void Configure(EntityTypeBuilder<TrainingCertificate> builder)
        {
            builder.ToTable("TrainingCertificates");
            builder.HasQueryFilter(tc => !tc.IsDeleted);

            builder.HasKey(tc => tc.Id);
            builder.Property(tc => tc.Id).UseIdentityColumn();

            builder.Property(tc => tc.CertificateNo)
                .HasMaxLength(100); // Now nullable

            builder.Property(tc => tc.ProviderName)
                .HasMaxLength(200); // Nullable provider name

            builder.Property(tc => tc.FileUrl)
                .HasMaxLength(1000);

            builder.Property(tc => tc.Notes)
                .HasMaxLength(1000);

            // Relationships
            builder.HasOne(tc => tc.Worker)
                .WithMany()
                .HasForeignKey(tc => tc.WorkerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(tc => tc.Program)
                .WithMany(p => p.Certificates)
                .HasForeignKey(tc => tc.ProgramId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(tc => tc.Session)
                .WithMany()
                .HasForeignKey(tc => tc.SessionId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasOne(tc => tc.CertificateFile)
                .WithMany()
                .HasForeignKey(tc => tc.CertificateFileId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes
            builder.HasIndex(tc => tc.CertificateNo); // No longer unique since it's nullable
            builder.HasIndex(tc => tc.WorkerId);
            builder.HasIndex(tc => tc.ExpiryDate);
            builder.HasIndex(tc => tc.Status);
            builder.HasIndex(tc => new { tc.WorkerId, tc.ProgramId });
        }
    }
}


