using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class WorkerConfiguration : IEntityTypeConfiguration<Worker>
    {
        public void Configure(EntityTypeBuilder<Worker> builder)
        {
            builder.ToTable("Workers");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Company)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.NationalId)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(e => e.Gender)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(e => e.PhoneNumber)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(e => e.Email)
                .HasMaxLength(100);

            builder.Property(e => e.MpesaNumber)
                .HasMaxLength(20);

            // File metadata relationships
            builder.Property(e => e.ProfilePictureFileId)
                .IsRequired(false);

            builder.Property(e => e.SignatureFileId)
                .IsRequired(false);

            builder.HasMany(p => p.DocumentFiles)
                .WithMany()
                .UsingEntity("WorkerDocuments");

            builder.HasOne(e => e.ProfilePictureFile)
                .WithMany()
                .HasForeignKey(e => e.ProfilePictureFileId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(e => e.SignatureFile)
                .WithMany()
                .HasForeignKey(e => e.SignatureFileId)
                .OnDelete(DeleteBehavior.NoAction);

            // builder.HasMany(e => e.DocumentFiles)
            //     .WithMany()
            //     .OnDelete(DeleteBehavior.NoAction);

            // Soft delete fields
            builder.Property(e => e.IsDeleted)
                .HasDefaultValue(false);

            builder.Property(e => e.DeletedBy)
                .HasMaxLength(100);

            // Indexes for better query performance
            builder.HasIndex(e => e.NationalId)
                .IsUnique();

            builder.HasIndex(e => e.Email);
            builder.HasIndex(e => e.Company);
            builder.HasIndex(e => e.IsDeleted);
        }
    }
}