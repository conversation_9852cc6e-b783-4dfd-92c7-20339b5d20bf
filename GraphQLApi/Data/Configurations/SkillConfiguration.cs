using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class SkillConfiguration : IEntityTypeConfiguration<Skill>
    {
        public void Configure(EntityTypeBuilder<Skill> builder)
        {
            builder.ToTable("Skills");

            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .UseIdentityColumn();

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.Description)
                .HasMaxLength(500);

            // Unique constraint on Name
            builder.HasIndex(e => e.Name)
                .IsUnique();

            // Many-to-many relationship with Workers
            builder.HasMany(e => e.Workers)
                .WithMany(w => w.Skills)
                .UsingEntity(
                    "WorkerSkill",
                    l => l.<PERSON>(typeof(Worker)).WithMany().<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("WorkerId").HasPrin<PERSON><PERSON><PERSON><PERSON>(nameof(Worker.Id)),
                    r => r.<PERSON>(typeof(Skill)).WithMany().HasForeign<PERSON>ey("SkillId").HasPrincipalKey(nameof(Skill.Id)),
                    j => j.HasKey("WorkerId", "SkillId"));
        }
    }
}
