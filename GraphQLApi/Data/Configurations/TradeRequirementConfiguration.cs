using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class TradeRequirementConfiguration : IEntityTypeConfiguration<TradeRequirement>
    {
        public void Configure(EntityTypeBuilder<TradeRequirement> builder)
        {
            builder.ToTable("TradeRequirements");
            builder.HasQueryFilter(tr => !tr.IsDeleted);

            builder.HasKey(tr => tr.Id);
            builder.Property(tr => tr.Id).UseIdentityColumn();

            builder.Property(tr => tr.Notes)
                .HasMaxLength(500);

            // Relationships
            builder.HasOne(tr => tr.Trade)
                .WithMany()
                .HasForeignKey(tr => tr.TradeId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(tr => tr.Program)
                .WithMany(p => p.TradeRequirements)
                .HasForeignKey(tr => tr.ProgramId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes
            builder.HasIndex(tr => tr.TradeId);
            builder.HasIndex(tr => tr.ProgramId);
            builder.HasIndex(tr => new { tr.TradeId, tr.ProgramId }).IsUnique(); // One requirement per trade-program combination
            builder.HasIndex(tr => tr.Mandatory);
        }
    }
}


