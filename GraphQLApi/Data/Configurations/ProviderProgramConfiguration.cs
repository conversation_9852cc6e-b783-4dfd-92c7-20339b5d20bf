using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class ProviderProgramConfiguration : IEntityTypeConfiguration<ProviderProgram>
    {
        public void Configure(EntityTypeBuilder<ProviderProgram> builder)
        {
            builder.ToTable("ProviderPrograms");
            builder.HasQueryFilter(pp => !pp.IsDeleted);

            builder.HasKey(pp => pp.Id);
            builder.Property(pp => pp.Id).UseIdentityColumn();

            builder.Property(pp => pp.CertificateNumber)
                .HasMaxLength(100);

            builder.Property(pp => pp.CertifyingOrganization)
                .HasMaxLength(200);

            builder.Property(pp => pp.Notes)
                .HasMaxLength(1000);

            // Pricing configuration
            builder.Property(pp => pp.PricingStrategy)
                .IsRequired()
                .HasConversion<string>(); // Store enum as string in DB

            builder.Property(pp => pp.Charges)
                .HasPrecision(18, 2) // Standard decimal precision for currency
                .IsRequired();

            builder.Property(pp => pp.Duration)
                .IsRequired();

            // Relationships
            builder.HasOne(pp => pp.Provider)
                .WithMany(p => p.Programs)
                .HasForeignKey(pp => pp.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(pp => pp.Program)
                .WithMany(p => p.Providers)
                .HasForeignKey(pp => pp.ProgramId)
                .OnDelete(DeleteBehavior.Cascade);

            // Indexes
            builder.HasIndex(pp => pp.ProviderId);
            builder.HasIndex(pp => pp.ProgramId);
            builder.HasIndex(pp => new { pp.ProviderId, pp.ProgramId });
            builder.HasIndex(pp => pp.ExpiryDate);
            builder.HasIndex(pp => pp.Active);
        }
    }
}

