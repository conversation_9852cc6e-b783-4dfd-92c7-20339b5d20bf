using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Data.Configurations
{
    public class TrainingProgramConfiguration : IEntityTypeConfiguration<TrainingProgram>
    {
        public void Configure(EntityTypeBuilder<TrainingProgram> builder)
        {
            builder.ToTable("TrainingPrograms");
            builder.HasQueryFilter(tp => !tp.IsDeleted);

            builder.HasKey(tp => tp.Id);
            builder.Property(tp => tp.Id).UseIdentityColumn();

            builder.Property(tp => tp.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(tp => tp.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(tp => tp.Description)
                .HasMaxLength(1000);

            builder.Property(tp => tp.Prerequisites)
                .HasMaxLength(2000); // JSON array of program IDs

            // Indexes for better query performance
            builder.HasIndex(tp => tp.Code).IsUnique();
            builder.HasIndex(tp => tp.Active);
            builder.HasIndex(tp => tp.CertificateType);
        }
    }
}


