using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.GraphQL.Models;

namespace GraphQLApi.Data.Configurations
{
    public class ControlMeasureConfiguration : IEntityTypeConfiguration<ControlMeasure>
    {
        public void Configure(EntityTypeBuilder<ControlMeasure> builder)
        {
            builder.HasKey(cm => cm.Id);

            builder.Property(cm => cm.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(cm => cm.Closed)
                .IsRequired()
                .HasDefaultValue(false);

            // Configure relationship with Hazard (compulsory)
            builder.HasOne(cm => cm.Hazard)
                .WithMany(h => h.ControlMeasures)
                .HasForeignKey(cm => cm.HazardId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit fields
            builder.Property(cm => cm.CreatedAt)
                .IsRequired();

            builder.Property(cm => cm.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cm => cm.UpdatedBy)
                .HasMaxLength(100);

            // Soft delete fields
            builder.Property(cm => cm.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(cm => cm.DeletedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(cm => cm.HazardId);
        }
    }
}
