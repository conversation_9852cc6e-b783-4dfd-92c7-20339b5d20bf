using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Auth;
using Shared.GraphQL.Models.Notifications;
using Shared.GraphQL.Models.Permits;
using Shared.GraphQL.Models.Training;
using Shared.GraphQL.Models.Certificates;
using System.Reflection;
using Shared.Interfaces;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata;
using System.ComponentModel.DataAnnotations;
using Shared.Errors;

namespace GraphQLApi.Data
{
    public class AppDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, int,
        ApplicationUserClaim, ApplicationUserRole, ApplicationUserLogin,
        ApplicationRoleClaim, ApplicationUserToken>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<Site> Sites { get; set; }
        public DbSet<Worker> Workers { get; set; }
        public DbSet<WorkerAttendance> WorkerAttendances { get; set; }
        public DbSet<ToolboxSession> ToolboxSessions { get; set; }
        public DbSet<ToolboxAttendance> ToolboxAttendances { get; set; }
        public DbSet<LegacyTraining> Trainings { get; set; }
        public DbSet<WorkerTraining> WorkerTrainings { get; set; }
        public DbSet<Trade> Trades { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<WorkerTrainingHistory> WorkerTrainingHistory { get; set; }
        
        // Certificates DbSet
        public DbSet<Certificate> Certificates { get; set; }
        // public DbSet<Shared.GraphQL.Models.Task> Tasks { get; set; }
        public DbSet<Equipment> Equipment { get; set; }
        public DbSet<Incident> Incidents { get; set; }
        public DbSet<FileMetadata> FileMetadata { get; set; }
        public DbSet<DocumentFile> DocumentFiles { get; set; }

        // Job System
        public DbSet<Job> Jobs { get; set; }
        public DbSet<Hazard> Hazards { get; set; }
        public DbSet<ControlMeasure> ControlMeasures { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Toolbox> Toolboxes { get; set; }

        // Permit System
        public DbSet<Permit> Permits { get; set; }
        public DbSet<GeneralWorkPermit> GeneralWorkPermits { get; set; }
        public DbSet<ExcavationWorkPermit> ExcavationWorkPermits { get; set; }
        public DbSet<WorkAtHeightPermit> WorkAtHeightPermits { get; set; }
        public DbSet<ConfinedSpacePermit> ConfinedSpacePermits { get; set; }
        public DbSet<HotWorkPermit> HotWorkPermits { get; set; }

        // Inspection System
        public DbSet<Inspection> Inspections { get; set; }
        public DbSet<InspectionItem> InspectionItems { get; set; }

        // Notification entities
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<NotificationDelivery> NotificationDeliveries { get; set; }
        public DbSet<NotificationPreference> NotificationPreferences { get; set; }

        // Training System (NEW - following toolbox patterns)
        public DbSet<TrainingProgram> TrainingPrograms { get; set; }
        public DbSet<TrainingProvider> TrainingProviders { get; set; }
        public DbSet<TrainingSession> TrainingSessions { get; set; }
        public DbSet<TrainingEnrollment> TrainingEnrollments { get; set; }
        public DbSet<TrainingCertificate> TrainingCertificates { get; set; }
        public DbSet<TradeRequirement> TradeRequirements { get; set; }
        public DbSet<ProviderCertification> ProviderCertifications { get; set; }
        public DbSet<ProviderProgram> ProviderPrograms { get; set; }

        // Authentication and Authorization
        // Identity entities are automatically configured by IdentityDbContext
        // Custom authentication entities
        public DbSet<Role> CustomRoles { get; set; } // Keep for permission management
        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<UserAuditLog> UserAuditLogs { get; set; }
        
        // Data protection keys for enhanced security
        public DbSet<Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.DataProtectionKey> DataProtectionKeys { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure Identity entities first
            base.OnModelCreating(modelBuilder);
            
            // Apply custom configurations
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Configure Identity and custom authentication entities
            ConfigureIdentityEntities(modelBuilder);
            ConfigureCustomAuthEntities(modelBuilder);

            // Apply global query filters for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ISoftDeletable).IsAssignableFrom(entityType.ClrType) &&
                    entityType.BaseType == null) // Only apply to root entities in inheritance hierarchy
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(GetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)
                        ?.MakeGenericMethod(entityType.ClrType);
                    var filter = method?.Invoke(null, Array.Empty<object>());
                    entityType.SetQueryFilter((LambdaExpression)filter!);
                }
            }
            modelBuilder.Entity<LegacyTraining>()
    .HasIndex(u => u.Name)
    .IsUnique();
            modelBuilder.Entity<Trade>()
    .HasIndex(u => u.Name)
    .IsUnique();
            modelBuilder.Entity<Skill>()
    .HasIndex(u => u.Name)
    .IsUnique();

            ConfigureNotificationEntities(modelBuilder);
            base.OnModelCreating(modelBuilder);
        }

        private void ConfigureIdentityEntities(ModelBuilder modelBuilder)
        {
            // Configure Identity table names and relationships
            modelBuilder.Entity<ApplicationUser>(entity =>
            {
                entity.ToTable("Users");
                entity.HasIndex(e => new { e.TenantId, e.Email }).IsUnique();
                entity.HasIndex(e => e.TenantId);

                entity.HasOne(e => e.Role)
                    .WithMany(r => r.ApplicationUsers)
                    .HasForeignKey(e => e.RoleId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Tenant)
                    .WithMany(t => t.ApplicationUsers)
                    .HasForeignKey(e => e.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Configure Identity properties with enhanced security
                entity.Property(e => e.Email).HasMaxLength(255);
                entity.Property(e => e.NormalizedEmail).HasMaxLength(255);
                entity.Property(e => e.UserName).HasMaxLength(255);
                entity.Property(e => e.NormalizedUserName).HasMaxLength(255);
                entity.Property(e => e.SecurityStamp).HasMaxLength(255);
                entity.Property(e => e.ConcurrencyStamp).HasMaxLength(255);
            });

            modelBuilder.Entity<ApplicationRole>(entity =>
            {
                entity.ToTable("AspNetRoles");
                entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();

                entity.HasOne(e => e.Tenant)
                    .WithMany()
                    .HasForeignKey(e => e.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure other Identity tables with proper names
            modelBuilder.Entity<ApplicationUserClaim>().ToTable("AspNetUserClaims");
            modelBuilder.Entity<ApplicationUserRole>().ToTable("AspNetUserRoles");
            modelBuilder.Entity<ApplicationUserLogin>().ToTable("AspNetUserLogins");
            modelBuilder.Entity<ApplicationUserToken>().ToTable("AspNetUserTokens");
            modelBuilder.Entity<ApplicationRoleClaim>().ToTable("AspNetRoleClaims");
        }

        private void ConfigureCustomAuthEntities(ModelBuilder modelBuilder)
        {
            // Keep your existing Role configuration for permission management
            modelBuilder.Entity<Role>(entity =>
            {
                entity.ToTable("CustomRoles"); // Rename to avoid conflict with Identity roles
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).UseIdentityColumn();
                entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();

                entity.HasOne(e => e.Tenant)
                    .WithMany(t => t.Roles)
                    .HasForeignKey(e => e.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Tenant configuration
            modelBuilder.Entity<Tenant>(entity =>
            {
                entity.ToTable("Tenants");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).UseIdentityColumn();
                entity.HasIndex(e => e.Subdomain).IsUnique();
            });

            // RefreshToken configuration
            modelBuilder.Entity<RefreshToken>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Token).IsUnique();
                entity.HasIndex(e => e.JwtId);
                entity.HasIndex(e => e.ApplicationUserId);

                // ApplicationUser relationship (required)
                entity.HasOne(e => e.ApplicationUser)
                    .WithMany(u => u.RefreshTokens)
                    .HasForeignKey(e => e.ApplicationUserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserSession configuration
            modelBuilder.Entity<UserSession>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.SessionId).IsUnique();
                entity.HasIndex(e => e.ApplicationUserId);

                entity.HasOne(e => e.ApplicationUser)
                    .WithMany(u => u.Sessions)
                    .HasForeignKey(e => e.ApplicationUserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserAuditLog configuration
            modelBuilder.Entity<UserAuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.ApplicationUserId);
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(e => e.ApplicationUser)
                    .WithMany(u => u.AuditLogs)
                    .HasForeignKey(e => e.ApplicationUserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Site configuration - add tenant relationship
            modelBuilder.Entity<Site>(entity =>
            {
                entity.HasOne(e => e.Tenant)
                    .WithMany(t => t.Sites)
                    .HasForeignKey(e => e.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }

        private static LambdaExpression GetSoftDeleteFilter<TEntity>() where TEntity : class, ISoftDeletable
        {
            Expression<Func<TEntity, bool>> filter = x => !x.IsDeleted;
            return filter;
        }

        public override int SaveChanges()
        {
            // ValidateEntities();
            UpdateAuditFields();
            HandleSoftDelete();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // ValidateEntities();
            UpdateAuditFields();
            HandleSoftDelete();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is IAuditableEntity && (
                    e.State == EntityState.Added ||
                    e.State == EntityState.Modified));

            foreach (var entityEntry in entries)
            {
                var entity = (IAuditableEntity)entityEntry.Entity;

                if (entityEntry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                    entity.CreatedBy ??= "System";
                }

                entity.UpdatedAt = DateTime.UtcNow;
                entity.UpdatedBy ??= "System";
            }
        }

        private void HandleSoftDelete()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is ISoftDeletable && e.State == EntityState.Deleted);

            foreach (var entityEntry in entries)
            {
                var entity = (ISoftDeletable)entityEntry.Entity;
                entityEntry.State = EntityState.Modified;
                entity.IsDeleted = true;
                entity.DeletedAt = DateTime.UtcNow;
                entity.DeletedBy ??= "System";

                // Cascade soft delete to related child entities
                CascadeSoftDelete(entityEntry, new HashSet<object>(), 0);
            }
        }

        private const int MaxCascadeDepth = 10;

        // private void CascadeSoftDelete(
        //     Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry parentEntry,
        //     HashSet<object>? visited = null,
        //     int depth = 0)
        // {
        //     if (depth > MaxCascadeDepth)
        //         return;

        //     visited ??= new HashSet<object>();
        //     var entity = parentEntry.Entity;

        //     // Prevent cycles
        //     if (!visited.Add(entity))
        //         return;

        //     var navigations = parentEntry.Navigations;

        //     foreach (var navigationEntry in navigations)
        //     {
        //         // Ensure navigation is loaded
        //         if (!navigationEntry.IsLoaded)
        //         {
        //             navigationEntry.Load();
        //         }

        //         if (!navigationEntry.Metadata.IsCollection)
        //         {
        //             var childEntity = navigationEntry.CurrentValue;
        //             if (childEntity is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
        //             {
        //                 var childEntry = ChangeTracker.Entries()
        //                     .FirstOrDefault(e => e.Entity == childEntity);
        //                 if (childEntry != null)
        //                 {
        //                     childEntry.State = EntityState.Modified;
        //                     softDeletableChild.IsDeleted = true;
        //                     softDeletableChild.DeletedAt = DateTime.UtcNow;
        //                     softDeletableChild.DeletedBy ??= "System";
        //                     // Recursively cascade with updated parameters
        //                     CascadeSoftDelete(childEntry, visited, depth + 1);
        //                 }
        //             }
        //         }
        //         else
        //         {
        //             var children = navigationEntry.CurrentValue as System.Collections.IEnumerable;
        //             if (children != null)
        //             {
        //                 foreach (var child in children)
        //                 {
        //                     if (child is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
        //                     {
        //                         var childEntry = ChangeTracker.Entries()
        //                             .FirstOrDefault(e => e.Entity == child);
        //                         if (childEntry != null)
        //                         {
        //                             childEntry.State = EntityState.Modified;
        //                             softDeletableChild.IsDeleted = true;
        //                             softDeletableChild.DeletedAt = DateTime.UtcNow;
        //                             softDeletableChild.DeletedBy ??= "System";
        //                             // Recursively cascade
        //                             CascadeSoftDelete(childEntry);
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
        private void CascadeSoftDelete(
    Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry parentEntry,
    HashSet<object>? visited = null,
    int depth = 0)
        {
            if (depth > MaxCascadeDepth)
                return;

            visited ??= new HashSet<object>();
            var entity = parentEntry.Entity;

            // Prevent cycles
            if (!visited.Add(entity))
                return;

            // ✅ Optional: Log the entity being processed
            Console.WriteLine($"[CascadeSoftDelete] Visiting {entity.GetType().Name} (depth {depth})");

            var navigations = parentEntry.Navigations;

            foreach (var navigationEntry in navigations)
            {
                try
                {
                    if (navigationEntry.Metadata?.PropertyInfo == null)
                        continue;

                    // ✅ Log the navigation being accessed
                    Console.WriteLine($"[CascadeSoftDelete] Inspecting navigation: {navigationEntry.Metadata.Name}");

                    if (!navigationEntry.IsLoaded && navigationEntry.Metadata is INavigation)
                    {
                        navigationEntry.Load();
                        Console.WriteLine($"[CascadeSoftDelete] Loaded navigation: {navigationEntry.Metadata.Name}");
                    }

                    var value = navigationEntry.CurrentValue;
                    if (value == null)
                        continue;

                    if (!navigationEntry.Metadata.IsCollection)
                    {
                        if (value is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                        {
                            var childEntry = ChangeTracker.Entries()
                                .FirstOrDefault(e => e.Entity == value);
                            if (childEntry != null)
                            {
                                Console.WriteLine($"[CascadeSoftDelete] Soft deleting child entity: {value.GetType().Name}");
                                childEntry.State = EntityState.Modified;
                                softDeletableChild.IsDeleted = true;
                                softDeletableChild.DeletedAt = DateTime.UtcNow;
                                softDeletableChild.DeletedBy ??= "System";
                                CascadeSoftDelete(childEntry, visited, depth + 1);
                            }
                        }
                    }
                    else
                    {
                        if (value is System.Collections.IEnumerable children)
                        {
                            foreach (var child in children)
                            {
                                if (child is ISoftDeletable softDeletableChild && !softDeletableChild.IsDeleted)
                                {
                                    var childEntry = ChangeTracker.Entries()
                                        .FirstOrDefault(e => e.Entity == child);
                                    if (childEntry != null)
                                    {
                                        Console.WriteLine($"[CascadeSoftDelete] Soft deleting child in collection: {child.GetType().Name}");
                                        childEntry.State = EntityState.Modified;
                                        softDeletableChild.IsDeleted = true;
                                        softDeletableChild.DeletedAt = DateTime.UtcNow;
                                        softDeletableChild.DeletedBy ??= "System";
                                        CascadeSoftDelete(childEntry, visited, depth + 1);
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CascadeSoftDelete] Error in navigation '{navigationEntry.Metadata?.Name}': {ex.Message}");
                }
            }
        }

        private void ValidateEntities()
        {
            var validationErrors = new List<ValidationResult>();
            foreach (var entr in ChangeTracker.Entries<IValidatableObject>())
            {
                var entity = entr.Entity;
                var context = new ValidationContext(entity);
                Validator.TryValidateObject(entity, context, validationErrors, true);
            }
            if (validationErrors.Count > 0)
            {
                throw new MultipleValidationException(validationErrors);
            }
        }

        private void ConfigureNotificationEntities(ModelBuilder modelBuilder)
        {
            // Notification configuration
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.TenantId);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Tenant)
                    .WithMany()
                    .HasForeignKey(e => e.TenantId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.Property(e => e.Metadata)
                    .HasColumnType("nvarchar(max)");
            });

            // NotificationDelivery configuration
            modelBuilder.Entity<NotificationDelivery>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.NotificationId);
                entity.HasIndex(e => e.Channel);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(e => e.Notification)
                    .WithMany(n => n.Deliveries)
                    .HasForeignKey(e => e.NotificationId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // NotificationPreference configuration
            modelBuilder.Entity<NotificationPreference>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.UserId, e.NotificationType }).IsUnique();

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}