using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Shared.DTOs;
using Shared.GraphQL.Models.Notifications;

namespace GraphQLApi.Services
{
    public class NotificationService : INotificationService
    {
        private readonly NotificationEventBus _bus;
        private readonly NotificationManagementService _managementService;
        private readonly IRecipientResolutionService _recipientResolutionService;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(
            NotificationEventBus bus,
            NotificationManagementService managementService,
            IRecipientResolutionService recipientResolutionService,
            ILogger<NotificationService> logger)
        {
            _bus = bus;
            _managementService = managementService;
            _recipientResolutionService = recipientResolutionService;
            _logger = logger;
        }

        public Task PublishAsync(NotificationEvent evt, CancellationToken ct = default)
        {
            var ok = _bus.TryEnqueue(evt);
            if (!ok)
            {
                _logger.LogWarning("Notification dropped: {Type}", evt.Type);
            }
            return Task.CompletedTask;
        }

        public async Task PublishToRecipientsAsync(NotificationEvent evt, List<NotificationRecipient> recipients, CancellationToken ct = default)
        {
            // Create enhanced event with recipients
            var enhancedEvent = evt with { Recipients = recipients.Select(r => $"userid:{r.UserId}").ToList() };
            await PublishAsync(enhancedEvent, ct);
        }

        public async Task PublishToUserAsync(NotificationEvent evt, int userId, CancellationToken ct = default)
        {
            var recipients = await _recipientResolutionService.ResolveRecipientsByUserIdAsync(userId);
            await PublishToRecipientsAsync(evt, recipients, ct);
        }

        public async Task PublishToRoleAsync(NotificationEvent evt, string roleName, int tenantId, CancellationToken ct = default)
        {
            var recipients = await _recipientResolutionService.ResolveRecipientsByRoleAsync(roleName, tenantId);
            await PublishToRecipientsAsync(evt, recipients, ct);
        }

        public async Task<List<Notification>> GetUserNotificationsAsync(int userId, int skip = 0, int take = 50, bool unreadOnly = false)
        {
            return await _managementService.GetUserNotificationsAsync(userId, skip, take, unreadOnly);
        }

        public async Task<int> GetUnreadCountAsync(int userId)
        {
            return await _managementService.GetUnreadCountAsync(userId);
        }

        public async Task MarkAsReadAsync(int notificationId, int userId)
        {
            await _managementService.MarkAsReadAsync(notificationId, userId);
        }

        public async Task MarkAllAsReadAsync(int userId)
        {
            await _managementService.MarkAllAsReadAsync(userId);
        }

        public async Task<NotificationPreference?> GetUserPreferencesAsync(int userId, string notificationType)
        {
            return await _managementService.GetUserPreferencesAsync(userId, notificationType);
        }

        public async Task UpdateUserPreferencesAsync(int userId, string notificationType, NotificationPreference preferences)
        {
            await _managementService.UpdateUserPreferencesAsync(userId, notificationType, preferences);
        }
    }
}

