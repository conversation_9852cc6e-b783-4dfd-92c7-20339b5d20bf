using Shared.DTOs;

namespace GraphQLApi.Services.Notifications
{
    /// <summary>
    /// Composes and sends notifications for various events
    /// Uses composition pattern instead of inheritance
    /// </summary>
    public interface INotificationComposer
    {
        // Core composition methods
        Task ComposeAndSendAsync(string eventType, object data);
        Task ComposeAndSendAsync(string eventType, object data, NotificationOptions options);
        Task ComposeAndSendToMultipleAsync(string eventType, object data, IEnumerable<int> userIds);
        Task ComposeAndSendToRoleAsync(string eventType, object data, string roleName, int tenantId);

        // Batch operations
        Task ComposeBatchNotificationsAsync(IEnumerable<BatchNotificationRequest> requests);

        // Template-based composition
        Task ComposeFromTemplateAsync(string templateName, object templateData, int userId);
    }

    /// <summary>
    /// Options for notification composition
    /// </summary>
    public class NotificationOptions
    {
        public NotificationPriority? Priority { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionLabel { get; set; }
        public Dictionary<string, object>? AdditionalMetadata { get; set; }
        public bool? SuppressDuplicates { get; set; }
    }

    /// <summary>
    /// Batch notification request
    /// </summary>
    public class BatchNotificationRequest
    {
        public string EventType { get; set; } = string.Empty;
        public object Data { get; set; } = new();
        public int UserId { get; set; }
        public NotificationOptions? Options { get; set; }
    }

    /// <summary>
    /// Common notification data structures
    /// </summary>
    public class CertificateNotificationData
    {
        public int CertificateId { get; set; }
        public int WorkerId { get; set; }
        public string CertificateType { get; set; } = string.Empty;
        public string CertificateNo { get; set; } = string.Empty;
        public DateTime ExpiryDate { get; set; }
        public int DaysUntilExpiry { get; set; }
        public string? ProgramTitle { get; set; }
        public string? ProviderName { get; set; }
    }

    public class SessionNotificationData
    {
        public int SessionId { get; set; }
        public int WorkerId { get; set; }
        public string ProgramTitle { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public string Location { get; set; } = string.Empty;
        public string Mode { get; set; } = string.Empty;
        public string? ConductorName { get; set; }
    }

    public class EligibilityNotificationData
    {
        public int WorkerId { get; set; }
        public int TradeId { get; set; }
        public string TradeName { get; set; } = string.Empty;
        public string EligibilityStatus { get; set; } = string.Empty;
        public List<string> MissingCertificates { get; set; } = new();
        public List<string> ExpiringSoonCertificates { get; set; } = new();
    }
}





