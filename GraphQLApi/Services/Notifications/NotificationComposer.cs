using Shared.DTOs;
using System.Text.Json;
using System.Collections.Generic;

namespace GraphQLApi.Services.Notifications
{
    /// <summary>
    /// Composes notifications for various events using composition pattern
    /// Replaces training alerts and other notification systems
    /// </summary>
    public class NotificationComposer : INotificationComposer
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationComposer> _logger;

        public NotificationComposer(
            INotificationService notificationService,
            ILogger<NotificationComposer> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task ComposeAndSendAsync(string eventType, object data)
        {
            await ComposeAndSendAsync(eventType, data, new NotificationOptions());
        }

        public async Task ComposeAndSendAsync(string eventType, object data, NotificationOptions options)
        {
            try
            {
                var composition = await ComposeNotificationAsync(eventType, data, options);
                if (composition == null) return;

                await _notificationService.PublishToUserAsync(composition.Event, composition.UserId);
                
                _logger.LogInformation("Composed and sent notification of type {EventType} to user {UserId}", 
                    eventType, composition.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compose and send notification of type {EventType}", eventType);
                throw;
            }
        }

        public async Task ComposeAndSendToMultipleAsync(string eventType, object data, IEnumerable<int> userIds)
        {
            var tasks = userIds.Select(userId => ComposeAndSendToUserAsync(eventType, data, userId));
            await Task.WhenAll(tasks);
        }

        public async Task ComposeAndSendToRoleAsync(string eventType, object data, string roleName, int tenantId)
        {
            try
            {
                var notificationEvent = await ComposeNotificationEventAsync(eventType, data, new NotificationOptions());
                if (notificationEvent == null) return;

                await _notificationService.PublishToRoleAsync(notificationEvent, roleName, tenantId);
                
                _logger.LogInformation("Composed and sent notification of type {EventType} to role {RoleName} in tenant {TenantId}", 
                    eventType, roleName, tenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compose and send notification of type {EventType} to role {RoleName}", 
                    eventType, roleName);
                throw;
            }
        }

        public async Task ComposeBatchNotificationsAsync(IEnumerable<BatchNotificationRequest> requests)
        {
            var tasks = requests.Select(request => 
                ComposeAndSendToUserAsync(request.EventType, request.Data, request.UserId, request.Options));
            
            await Task.WhenAll(tasks);
        }

        public async Task ComposeFromTemplateAsync(string templateName, object templateData, int userId)
        {
            // Template-based composition (can be extended later)
            var eventType = $"template.{templateName}";
            await ComposeAndSendToUserAsync(eventType, templateData, userId);
        }

        private async Task ComposeAndSendToUserAsync(string eventType, object data, int userId, NotificationOptions? options = null)
        {
            try
            {
                var notificationEvent = await ComposeNotificationEventAsync(eventType, data, options ?? new NotificationOptions());
                if (notificationEvent == null) return;

                await _notificationService.PublishToUserAsync(notificationEvent, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compose and send notification of type {EventType} to user {UserId}", 
                    eventType, userId);
            }
        }

        private async Task<NotificationComposition?> ComposeNotificationAsync(string eventType, object data, NotificationOptions options)
        {
            var notificationEvent = await ComposeNotificationEventAsync(eventType, data, options);
            if (notificationEvent == null) return null;

            var userId = ExtractUserIdFromData(eventType, data);
            if (userId == 0)
            {
                _logger.LogWarning("Could not extract user ID from notification data for event type {EventType}", eventType);
                return null;
            }

            return new NotificationComposition
            {
                Event = notificationEvent,
                UserId = userId
            };
        }

        private async Task<NotificationEvent?> ComposeNotificationEventAsync(string eventType, object data, NotificationOptions options)
        {
            return eventType switch
            {
                "certificate.expiring" => ComposeCertificateExpiringNotification(data, options),
                "certificate.expired" => ComposeCertificateExpiredNotification(data, options),
                "certificate.issued" => ComposeCertificateIssuedNotification(data, options),
                "certificate.revoked" => ComposeCertificateRevokedNotification(data, options),
                "session.invitation" => ComposeSessionInvitationNotification(data, options),
                "session.reminder" => ComposeSessionReminderNotification(data, options),
                "session.completed" => ComposeSessionCompletedNotification(data, options),
                "session.cancelled" => ComposeSessionCancelledNotification(data, options),
                "eligibility.updated" => ComposeEligibilityUpdatedNotification(data, options),
                "eligibility.warning" => ComposeEligibilityWarningNotification(data, options),
                _ => await ComposeGenericNotificationAsync(eventType, data, options)
            };
        }

        #region Certificate Notifications

        private NotificationEvent ComposeCertificateExpiringNotification(object data, NotificationOptions options)
        {
            var certData = ConvertToType<CertificateNotificationData>(data);
            
            var priority = options.Priority ?? (certData.DaysUntilExpiry <= 7 
                ? NotificationPriority.High 
                : NotificationPriority.Medium);

            var metadata = new Dictionary<string, string>
            {
                ["certificateId"] = certData.CertificateId.ToString(),
                ["certificateType"] = certData.CertificateType,
                ["certificateNo"] = certData.CertificateNo,
                ["expiryDate"] = certData.ExpiryDate.ToString("o"),
                ["daysUntilExpiry"] = certData.DaysUntilExpiry.ToString(),
                ["programTitle"] = certData.ProgramTitle ?? string.Empty,
                ["providerName"] = certData.ProviderName ?? string.Empty,
                ["entityId"] = certData.CertificateId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/certificates/{certData.CertificateId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Certificate"),
                ["expiresAt"] = (options.ExpiresAt ?? certData.ExpiryDate).ToString("o")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.certificate.expiring",
                Title: "Training Certificate Expiring",
                Message: $"Your {certData.ProgramTitle ?? certData.CertificateType} certificate expires in {certData.DaysUntilExpiry} days",
                Entity: "Certificate",
                Operation: null,
                Metadata: metadata,
                Priority: priority
            );
        }

        private NotificationEvent ComposeCertificateExpiredNotification(object data, NotificationOptions options)
        {
            var certData = ConvertToType<CertificateNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["certificateId"] = certData.CertificateId.ToString(),
                ["certificateType"] = certData.CertificateType,
                ["certificateNo"] = certData.CertificateNo,
                ["expiredDate"] = certData.ExpiryDate.ToString("o"),
                ["programTitle"] = certData.ProgramTitle ?? string.Empty,
                ["entityId"] = certData.CertificateId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/certificates/{certData.CertificateId}"),
                ["actionLabel"] = (options.ActionLabel ?? "Renew Certificate")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.certificate.expired",
                Title: "Training Certificate Expired",
                Message: $"Your {certData.ProgramTitle ?? certData.CertificateType} certificate has expired. You are no longer eligible for related work.",
                Entity: "Certificate",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.High
            );
        }

        private NotificationEvent ComposeCertificateIssuedNotification(object data, NotificationOptions options)
        {
            var certData = ConvertToType<CertificateNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["certificateId"] = certData.CertificateId.ToString(),
                ["certificateType"] = certData.CertificateType,
                ["certificateNo"] = certData.CertificateNo,
                ["expiryDate"] = certData.ExpiryDate.ToString("o"),
                ["programTitle"] = certData.ProgramTitle ?? string.Empty,
                ["entityId"] = certData.CertificateId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/certificates/{certData.CertificateId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Certificate")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.certificate.issued",
                Title: "Training Certificate Issued",
                Message: $"Congratulations! Your {certData.ProgramTitle ?? certData.CertificateType} certificate has been issued",
                Entity: "Certificate",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.Low
            );
        }

        private NotificationEvent ComposeCertificateRevokedNotification(object data, NotificationOptions options)
        {
            var certData = ConvertToType<CertificateNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["certificateId"] = certData.CertificateId.ToString(),
                ["certificateType"] = certData.CertificateType,
                ["certificateNo"] = certData.CertificateNo,
                ["programTitle"] = certData.ProgramTitle ?? string.Empty,
                ["entityId"] = certData.CertificateId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/certificates/{certData.CertificateId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Details")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.certificate.revoked",
                Title: "Training Certificate Revoked",
                Message: $"Your {certData.ProgramTitle ?? certData.CertificateType} certificate has been revoked",
                Entity: "Certificate",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.High
            );
        }

        #endregion

        #region Session Notifications

        private NotificationEvent ComposeSessionInvitationNotification(object data, NotificationOptions options)
        {
            var sessionData = ConvertToType<SessionNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["sessionId"] = sessionData.SessionId.ToString(),
                ["programTitle"] = sessionData.ProgramTitle,
                ["startDate"] = sessionData.StartDate.ToString("o"),
                ["location"] = sessionData.Location,
                ["mode"] = sessionData.Mode,
                ["conductorName"] = sessionData.ConductorName ?? string.Empty,
                ["entityId"] = sessionData.SessionId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/training/sessions/{sessionData.SessionId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Details"),
                ["expiresAt"] = (options.ExpiresAt ?? sessionData.StartDate.AddDays(-1)).ToString("o")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.session.invitation",
                Title: "Training Session Invitation",
                Message: $"You're invited to attend {sessionData.ProgramTitle} on {sessionData.StartDate:yyyy-MM-dd HH:mm} at {sessionData.Location}",
                Entity: "TrainingSession",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.Medium
            );
        }

        private NotificationEvent ComposeSessionReminderNotification(object data, NotificationOptions options)
        {
            var sessionData = ConvertToType<SessionNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["sessionId"] = sessionData.SessionId.ToString(),
                ["programTitle"] = sessionData.ProgramTitle,
                ["startDate"] = sessionData.StartDate.ToString("o"),
                ["location"] = sessionData.Location,
                ["entityId"] = sessionData.SessionId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/training/sessions/{sessionData.SessionId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Details")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.session.reminder",
                Title: "Training Session Reminder",
                Message: $"Reminder: {sessionData.ProgramTitle} training starts tomorrow at {sessionData.StartDate:HH:mm} at {sessionData.Location}",
                Entity: "TrainingSession",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.Medium
            );
        }

        private NotificationEvent ComposeSessionCompletedNotification(object data, NotificationOptions options)
        {
            var sessionData = ConvertToType<SessionNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["sessionId"] = sessionData.SessionId.ToString(),
                ["programTitle"] = sessionData.ProgramTitle,
                ["completedDate"] = DateTime.UtcNow.ToString("o"),
                ["entityId"] = sessionData.SessionId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/training/sessions/{sessionData.SessionId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Results")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.session.completed",
                Title: "Training Session Completed",
                Message: $"Your {sessionData.ProgramTitle} training session has been completed. Results will be available soon.",
                Entity: "TrainingSession",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.Medium
            );
        }

        private NotificationEvent ComposeSessionCancelledNotification(object data, NotificationOptions options)
        {
            var sessionData = ConvertToType<SessionNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["sessionId"] = sessionData.SessionId.ToString(),
                ["programTitle"] = sessionData.ProgramTitle,
                ["startDate"] = sessionData.StartDate.ToString("o"),
                ["cancelledDate"] = DateTime.UtcNow.ToString("o"),
                ["entityId"] = sessionData.SessionId.ToString(),
                ["actionUrl"] = (options.ActionUrl ?? $"/training/sessions/{sessionData.SessionId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Details")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.session.cancelled",
                Title: "Training Session Cancelled",
                Message: $"The {sessionData.ProgramTitle} training session scheduled for {sessionData.StartDate:yyyy-MM-dd} has been cancelled",
                Entity: "TrainingSession",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.High
            );
        }

        #endregion

        #region Eligibility Notifications

        private NotificationEvent ComposeEligibilityUpdatedNotification(object data, NotificationOptions options)
        {
            var eligibilityData = ConvertToType<EligibilityNotificationData>(data);

            var message = eligibilityData.EligibilityStatus switch
            {
                "ELIGIBLE" => $"You are now eligible to work in {eligibilityData.TradeName}",
                "ELIGIBLE_WITHIN_GRACE" => $"Your eligibility for {eligibilityData.TradeName} is within grace period. Please renew required certificates soon.",
                "NOT_ELIGIBLE" => $"You are not eligible to work in {eligibilityData.TradeName}. Missing required certifications.",
                _ => $"Your eligibility status for {eligibilityData.TradeName} has been updated."
            };

            var priority = eligibilityData.EligibilityStatus == "NOT_ELIGIBLE" 
                ? NotificationPriority.High 
                : NotificationPriority.Medium;

            var metadata = new Dictionary<string, string>
            {
                ["workerId"] = eligibilityData.WorkerId.ToString(),
                ["tradeId"] = eligibilityData.TradeId.ToString(),
                ["tradeName"] = eligibilityData.TradeName,
                ["eligibilityStatus"] = eligibilityData.EligibilityStatus,
                ["missingCertificates"] = JsonSerializer.Serialize(eligibilityData.MissingCertificates),
                ["expiringSoonCertificates"] = JsonSerializer.Serialize(eligibilityData.ExpiringSoonCertificates),
                ["updatedAt"] = DateTime.UtcNow.ToString("o"),
                ["entityId"] = $"{eligibilityData.WorkerId}_{eligibilityData.TradeId}",
                ["actionUrl"] = (options.ActionUrl ?? $"/training/eligibility/{eligibilityData.WorkerId}"),
                ["actionLabel"] = (options.ActionLabel ?? "View Details")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.eligibility.updated",
                Title: "Trade Eligibility Updated",
                Message: message,
                Entity: "WorkerEligibility",
                Operation: null,
                Metadata: metadata,
                Priority: priority
            );
        }

        private NotificationEvent ComposeEligibilityWarningNotification(object data, NotificationOptions options)
        {
            var eligibilityData = ConvertToType<EligibilityNotificationData>(data);

            var metadata = new Dictionary<string, string>
            {
                ["workerId"] = eligibilityData.WorkerId.ToString(),
                ["tradeId"] = eligibilityData.TradeId.ToString(),
                ["tradeName"] = eligibilityData.TradeName,
                ["expiringSoonCertificates"] = JsonSerializer.Serialize(eligibilityData.ExpiringSoonCertificates),
                ["warningDate"] = DateTime.UtcNow.ToString("o"),
                ["entityId"] = $"{eligibilityData.WorkerId}_{eligibilityData.TradeId}",
                ["actionUrl"] = (options.ActionUrl ?? $"/training/eligibility/{eligibilityData.WorkerId}"),
                ["actionLabel"] = (options.ActionLabel ?? "Renew Certificates")
            };
            if (options.AdditionalMetadata != null)
            {
                foreach (var kv in options.AdditionalMetadata)
                {
                    metadata[kv.Key] = kv.Value?.ToString() ?? string.Empty;
                }
            }

            return new NotificationEvent(
                Type: "training.eligibility.warning",
                Title: "Trade Eligibility Warning",
                Message: $"Warning: Your eligibility for {eligibilityData.TradeName} may be affected. Some certificates are expiring soon.",
                Entity: "WorkerEligibility",
                Operation: null,
                Metadata: metadata,
                Priority: NotificationPriority.Medium
            );
        }

        #endregion

        private async Task<NotificationEvent?> ComposeGenericNotificationAsync(string eventType, object data, NotificationOptions options)
        {
            // Fallback for unknown event types
            _logger.LogWarning("Unknown notification event type: {EventType}", eventType);
            return null;
        }

        private int ExtractUserIdFromData(string eventType, object data)
        {
            try
            {
                var jsonData = JsonSerializer.Serialize(data);
                var document = JsonDocument.Parse(jsonData);
                
                // Try common user ID field names
                var userIdFields = new[] { "WorkerId", "UserId", "workerId", "userId" };
                
                foreach (var field in userIdFields)
                {
                    if (document.RootElement.TryGetProperty(field, out var element) && element.TryGetInt32(out var userId))
                    {
                        return userId;
                    }
                }

                _logger.LogWarning("Could not extract user ID from notification data for event type {EventType}", eventType);
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting user ID from notification data for event type {EventType}", eventType);
                return 0;
            }
        }

        private T ConvertToType<T>(object data) where T : new()
        {
            try
            {
                var json = JsonSerializer.Serialize(data);
                return JsonSerializer.Deserialize<T>(json) ?? new T();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting notification data to type {Type}", typeof(T).Name);
                return new T();
            }
        }

        private class NotificationComposition
        {
            public NotificationEvent Event { get; set; } = null!;
            public int UserId { get; set; }
        }
    }
}





