using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace GraphQLApi.Services
{
    public class NoOpSmsSender : ISmsSender
    {
        private readonly ILogger<NoOpSmsSender> _logger;
        public NoOpSmsSender(ILogger<NoOpSmsSender> logger) { _logger = logger; }
        public Task SendAsync(string to, string message, CancellationToken ct)
        {
            _logger.LogInformation("SMS (no-op): to={To} message={Message}", to, message);
            return Task.CompletedTask;
        }
    }
}

