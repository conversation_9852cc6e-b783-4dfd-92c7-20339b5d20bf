namespace GraphQLApi.Services
{
    public interface IPhotoService
    {
        Task<string> UploadPhotoAsync(Stream photoStream, string fileName, PhotoStorageType storageType);
        Task<Stream> GetPhotoAsync(string photoUrl);
        Task DeletePhotoAsync(string photoUrl);
        Task<string> TransferPhotoToHikvisionAsync(string sourcePhotoUrl, string workerId);
    }

    public enum PhotoStorageType
    {
        Local,
        Hikvision,
        MinIO
    }
} 