using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HotChocolate.Subscriptions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public class InAppNotificationHandler : INotificationChannelHandler
    {
        private readonly ITopicEventSender _topicEventSender;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<InAppNotificationHandler> _logger;

        public InAppNotificationHandler(
            ITopicEventSender topicEventSender,
            IServiceScopeFactory serviceScopeFactory,
            ILogger<InAppNotificationHandler> logger)
        {
            _topicEventSender = topicEventSender;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        public bool CanHandle(NotificationChannel channel) => channel == NotificationChannel.InApp;

        public async Task HandleAsync(NotificationEvent evt, CancellationToken ct)
        {
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var recipientResolutionService = scope.ServiceProvider.GetRequiredService<IRecipientResolutionService>();
                var managementService = scope.ServiceProvider.GetRequiredService<NotificationManagementService>();

                List<NotificationRecipient> recipients;

                if (evt.Recipients?.Any() == true)
                {
                    // Resolve recipients from the event
                    recipients = await recipientResolutionService.ResolveRecipientsAsync(evt.Recipients, GetTenantIdFromEvent(evt));
                }
                else
                {
                    // Send to all users in tenant for backward compatibility
                    recipients = new List<NotificationRecipient>();
                    _logger.LogWarning("No recipients specified for in-app notification {Type}, sending to general topic", evt.Type);
                }

                if (recipients.Any())
                {
                    // Send targeted notifications to specific users
                    foreach (var recipient in recipients)
                    {
                        // Check if user has in-app notifications enabled
                        var preferences = await managementService.GetUserPreferencesAsync(recipient.UserId, evt.Type);
                        if (preferences?.InAppEnabled == false)
                        {
                            _logger.LogDebug("Skipping in-app notification for user {UserId} - in-app disabled in preferences", recipient.UserId);
                            continue;
                        }

                        try
                        {
                            // Send to user-specific topic
                            await _topicEventSender.SendAsync($"notifications_{recipient.UserId}", evt, ct);
                            _logger.LogDebug("In-app notification sent to user {UserId} for event {Type}", recipient.UserId, evt.Type);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to send in-app notification to user {UserId} for event {Type}", recipient.UserId, evt.Type);
                        }
                    }
                }
                else
                {
                    // Fallback to general notifications topic
                    await _topicEventSender.SendAsync("notifications", evt, ct);
                    _logger.LogDebug("In-app notification published to general topic: {Type}", evt.Type);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process in-app notification for event {Type}", evt.Type);
            }
        }

        private int GetTenantIdFromEvent(NotificationEvent evt)
        {
            // Try to extract tenant ID from metadata
            if (evt.Metadata?.TryGetValue("tenantId", out var tenantIdStr) == true &&
                int.TryParse(tenantIdStr, out var tenantId))
            {
                return tenantId;
            }

            // Default to tenant 1 if not specified
            return 1;
        }
    }
}

