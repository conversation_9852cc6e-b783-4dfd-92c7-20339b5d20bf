using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public class RiskAssessmentService : IRiskAssessmentService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public RiskAssessmentService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        // Hazard CRUD operations
        public async Task<Hazard> CreateHazardAsync(int jobId, string description)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate job exists
            var job = await context.Jobs.FindAsync(jobId);
            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            var hazard = new Hazard
            {
                Description = description,
                JobId = jobId
            };

            context.Hazards.Add(hazard);
            await context.SaveChangesAsync();

            return hazard;
        }

        public async Task<Hazard?> GetHazardByIdAsync(int hazardId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            return await context.Hazards
                .Include(h => h.ControlMeasures)
                .Include(h => h.Job)
                .FirstOrDefaultAsync(h => h.Id == hazardId);
        }

        public async Task<IEnumerable<Hazard>> GetHazardsByJobIdAsync(int jobId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            return await context.Hazards
                .Include(h => h.ControlMeasures)
                .Where(h => h.JobId == jobId)
                .ToListAsync();
        }

        public async Task<Hazard?> UpdateHazardAsync(int hazardId, string description)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var hazard = await context.Hazards.FindAsync(hazardId);
            if (hazard == null)
                return null;

            hazard.Description = description;
            await context.SaveChangesAsync();

            return hazard;
        }

        public async Task<bool> DeleteHazardAsync(int hazardId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var hazard = await context.Hazards.FindAsync(hazardId);
            if (hazard == null)
                return false;

            context.Hazards.Remove(hazard);
            await context.SaveChangesAsync();
            return true;
        }

        // Control Measure CRUD operations
        public async Task<ControlMeasure> CreateControlMeasureAsync(int hazardId, string description, bool closed = false)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate hazard exists
            var hazard = await context.Hazards.FindAsync(hazardId);
            if (hazard == null)
                throw new ArgumentException($"Hazard with ID {hazardId} not found");

            var controlMeasure = new ControlMeasure
            {
                Description = description,
                HazardId = hazardId,
                Closed = closed
            };

            context.ControlMeasures.Add(controlMeasure);
            await context.SaveChangesAsync();

            return controlMeasure;
        }

        public async Task<ControlMeasure?> GetControlMeasureByIdAsync(int controlMeasureId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            return await context.ControlMeasures
                .Include(cm => cm.Hazard)
                .FirstOrDefaultAsync(cm => cm.Id == controlMeasureId);
        }

        public async Task<IEnumerable<ControlMeasure>> GetControlMeasuresByHazardIdAsync(int hazardId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            return await context.ControlMeasures
                .Where(cm => cm.HazardId == hazardId)
                .ToListAsync();
        }

        public async Task<ControlMeasure?> UpdateControlMeasureAsync(int controlMeasureId, string description, bool? closed = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var controlMeasure = await context.ControlMeasures.FindAsync(controlMeasureId);
            if (controlMeasure == null)
                return null;

            controlMeasure.Description = description;
            if (closed.HasValue)
                controlMeasure.Closed = closed.Value;

            await context.SaveChangesAsync();
            return controlMeasure;
        }

        public async Task<bool> DeleteControlMeasureAsync(int controlMeasureId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var controlMeasure = await context.ControlMeasures.FindAsync(controlMeasureId);
            if (controlMeasure == null)
                return false;

            context.ControlMeasures.Remove(controlMeasure);
            await context.SaveChangesAsync();
            return true;
        }

        // Batch operations for toolbox creation
        public async Task ProcessJobHazardsAsync(int jobId, IEnumerable<ProcessExistingHazardInput> existingHazards, IEnumerable<ProcessNewHazardInput> newHazards)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate job exists
            var job = await context.Jobs.FindAsync(jobId);
            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Process existing hazards
            foreach (var existingHazard in existingHazards)
            {
                var hazard = await context.Hazards
                    .Include(h => h.ControlMeasures)
                    .FirstOrDefaultAsync(h => h.Id == existingHazard.Id && h.JobId == jobId);

                if (hazard == null)
                    // throw new ArgumentException($"Hazard with ID {existingHazard.Id} not found for job {jobId}");
                    continue;

                // Update hazard description
                    hazard.Description = existingHazard.Description;

                // Process existing control measures
                foreach (var existingCm in existingHazard.ExistingControlMeasures)
                {
                    var controlMeasure = hazard.ControlMeasures.FirstOrDefault(cm => cm.Id == existingCm.Id);
                    if (controlMeasure == null)
                        // throw new ArgumentException($"Control measure with ID {existingCm.Id} not found for hazard {existingHazard.Id}");
                        continue;

                    controlMeasure.Description = existingCm.Description;
                }

                // Add new control measures
                foreach (var newCm in existingHazard.NewControlMeasures)
                {
                    hazard.ControlMeasures.Add(new ControlMeasure
                    {
                        Description = newCm.Description,
                        HazardId = hazard.Id
                    });
                }
            }

            // Process new hazards
            foreach (var newHazard in newHazards)
            {
                var hazard = new Hazard
                {
                    Description = newHazard.Description,
                    JobId = jobId
                };

                // Add control measures to new hazard
                foreach (var newCm in newHazard.ControlMeasures)
                {
                    hazard.ControlMeasures.Add(new ControlMeasure
                    {
                        Description = newCm.Description
                    });
                }

                context.Hazards.Add(hazard);
            }

            await context.SaveChangesAsync();
        }
    }
}
