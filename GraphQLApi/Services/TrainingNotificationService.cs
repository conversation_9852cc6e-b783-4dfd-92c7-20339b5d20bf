using Shared.DTOs;
using System.Collections.Generic;
using Shared.GraphQL.Models.Training;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public class TrainingNotificationService
    {
        private readonly INotificationService _notificationService;
        private readonly ITrainingService _trainingService;
        private readonly ILogger<TrainingNotificationService> _logger;

        public TrainingNotificationService(
            INotificationService notificationService,
            ITrainingService trainingService,
            ILogger<TrainingNotificationService> logger)
        {
            _notificationService = notificationService;
            _trainingService = trainingService;
            _logger = logger;
        }

        // Replace training alerts with notifications
        public async Task SendCertificateExpiryNotificationsAsync()
        {
            try
            {
                var expiringCertificates = await _trainingService.GetExpiringCertificatesAsync(30);
                
                foreach (var cert in expiringCertificates)
                {
                    var daysUntilExpiry = (cert.ExpiryDate - DateTime.UtcNow).Days;
                    var priority = daysUntilExpiry <= 7 ? NotificationPriority.High : NotificationPriority.Medium;

                    var metadata = new Dictionary<string, string>
                    {
                        ["certificateId"] = cert.Id.ToString(),
                        ["workerId"] = cert.WorkerId.ToString(),
                        ["programId"] = cert.ProgramId.ToString(),
                        ["expiryDate"] = cert.ExpiryDate.ToString("o"),
                        ["daysUntilExpiry"] = daysUntilExpiry.ToString(),
                        ["entityId"] = cert.Id.ToString(),
                        ["actionUrl"] = $"/training/certificates/{cert.Id}",
                        ["actionLabel"] = "Renew Certificate",
                        ["expiresAt"] = cert.ExpiryDate.ToString("o")
                    };

                    var notificationEvent = new NotificationEvent(
                        Type: "training.certificate.expiring",
                        Title: "Training Certificate Expiring",
                        Message: $"Your {cert.Program.Title} certificate expires in {daysUntilExpiry} days ({cert.ExpiryDate:yyyy-MM-dd})",
                        Entity: "TrainingCertificate",
                        Operation: null,
                        Metadata: metadata,
                        Priority: priority
                    );

                    await _notificationService.PublishToUserAsync(notificationEvent, cert.WorkerId);
                    _logger.LogInformation("Sent certificate expiry notification for certificate {CertificateId} to worker {WorkerId}", 
                        cert.Id, cert.WorkerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending certificate expiry notifications");
                throw;
            }
        }

        public async Task SendSessionInvitationAsync(int sessionId, int workerId)
        {
            try
            {
                var session = await _trainingService.GetSessionByIdAsync(sessionId);
                if (session == null)
                {
                    _logger.LogWarning("Session {SessionId} not found for invitation", sessionId);
                    return;
                }

                var metadata = new Dictionary<string, string>
                {
                    ["sessionId"] = sessionId.ToString(),
                    ["programId"] = session.ProgramId.ToString(),
                    ["programTitle"] = session.Program.Title,
                    ["startDate"] = session.StartDate.ToString("o"),
                    ["location"] = session.Location,
                    ["mode"] = session.Mode.ToString(),
                    ["entityId"] = sessionId.ToString(),
                    ["actionUrl"] = $"/training/sessions/{sessionId}",
                    ["actionLabel"] = "View Details",
                    ["expiresAt"] = session.StartDate.AddDays(-1).ToString("o")
                };

                var notificationEvent = new NotificationEvent(
                    Type: "training.session.invitation",
                    Title: "Training Session Invitation",
                    Message: $"You're invited to attend {session.Program.Title} on {session.StartDate:yyyy-MM-dd HH:mm} at {session.Location}",
                    Entity: "TrainingSession",
                    Operation: null,
                    Metadata: metadata,
                    Priority: NotificationPriority.Medium
                );

                await _notificationService.PublishToUserAsync(notificationEvent, workerId);
                _logger.LogInformation("Sent session invitation for session {SessionId} to worker {WorkerId}", 
                    sessionId, workerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending session invitation for session {SessionId} to worker {WorkerId}", 
                    sessionId, workerId);
                throw;
            }
        }

        public async Task SendSessionReminderAsync(int sessionId)
        {
            try
            {
                var session = await _trainingService.GetSessionByIdAsync(sessionId);
                if (session == null) return;

                var enrollments = await _trainingService.GetSessionEnrollmentsAsync(sessionId);
                var registeredEnrollments = enrollments.Where(e => e.Status == Shared.Enums.EnrollmentStatus.REGISTERED);

                foreach (var enrollment in registeredEnrollments)
                {
                    var md = new Dictionary<string, string>
                    {
                        ["sessionId"] = sessionId.ToString(),
                        ["programTitle"] = session.Program.Title,
                        ["startDate"] = session.StartDate.ToString("o"),
                        ["location"] = session.Location,
                        ["entityId"] = sessionId.ToString(),
                        ["actionUrl"] = $"/training/sessions/{sessionId}",
                        ["actionLabel"] = "View Details"
                    };

                    var notificationEvent = new NotificationEvent(
                        Type: "training.session.reminder",
                        Title: "Training Session Reminder",
                        Message: $"Reminder: {session.Program.Title} training starts tomorrow at {session.StartDate:HH:mm} at {session.Location}",
                        Entity: "TrainingSession",
                        Operation: null,
                        Metadata: md,
                        Priority: NotificationPriority.Medium
                    );

                    await _notificationService.PublishToUserAsync(notificationEvent, enrollment.WorkerId);
                }

                _logger.LogInformation("Sent session reminders for session {SessionId} to {Count} workers", 
                    sessionId, registeredEnrollments.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending session reminders for session {SessionId}", sessionId);
                throw;
            }
        }

        public async Task SendCertificateIssuedNotificationAsync(int certificateId)
        {
            try
            {
                var certificates = await _trainingService.GetWorkerCertificatesAsync(certificateId);
                var certificate = certificates.FirstOrDefault(c => c.Id == certificateId);
                
                if (certificate == null) return;

                var mdIssued = new Dictionary<string, string>
                {
                    ["certificateId"] = certificateId.ToString(),
                    ["programTitle"] = certificate.Program.Title,
                    ["certificateNo"] = certificate.CertificateNo,
                    ["issueDate"] = certificate.IssueDate.ToString("o"),
                    ["expiryDate"] = certificate.ExpiryDate.ToString("o"),
                    ["entityId"] = certificateId.ToString(),
                    ["actionUrl"] = $"/training/certificates/{certificateId}",
                    ["actionLabel"] = "View Certificate"
                };

                var notificationEvent = new NotificationEvent(
                    Type: "training.certificate.issued",
                    Title: "Training Certificate Issued",
                    Message: $"Congratulations! Your {certificate.Program.Title} certificate has been issued and is valid until {certificate.ExpiryDate:yyyy-MM-dd}",
                    Entity: "TrainingCertificate",
                    Operation: null,
                    Metadata: mdIssued,
                    Priority: NotificationPriority.Low
                );

                await _notificationService.PublishToUserAsync(notificationEvent, certificate.WorkerId);
                _logger.LogInformation("Sent certificate issued notification for certificate {CertificateId} to worker {WorkerId}", 
                    certificateId, certificate.WorkerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending certificate issued notification for certificate {CertificateId}", certificateId);
                throw;
            }
        }

        public async Task SendSessionCompletedNotificationAsync(int sessionId)
        {
            try
            {
                var session = await _trainingService.GetSessionByIdAsync(sessionId);
                if (session == null) return;

                var enrollments = await _trainingService.GetSessionEnrollmentsAsync(sessionId);

                foreach (var enrollment in enrollments)
                {
                    // Determine message based on enrollment status
                    string message = enrollment.Status switch
                    {
                        EnrollmentStatus.ATTENDED => $"Thank you for attending {session.Program.Title} training. Your certificate will be issued soon if you passed.",
                        EnrollmentStatus.DID_NOT_ATTEND => $"You did not attend the {session.Program.Title} training session. Please contact your supervisor to reschedule.",
                        _ => $"Your {session.Program.Title} training session has been completed. Results will be available soon."
                    };

                    var priority = enrollment.Status == EnrollmentStatus.DID_NOT_ATTEND
                        ? NotificationPriority.High
                        : NotificationPriority.Medium;

                    var mdCompleted = new Dictionary<string, string>
                    {
                        ["sessionId"] = sessionId.ToString(),
                        ["programTitle"] = session.Program.Title,
                        ["status"] = enrollment.Status.ToString(),
                        ["completedDate"] = DateTime.UtcNow.ToString("o"),
                        ["entityId"] = sessionId.ToString(),
                        ["actionUrl"] = $"/training/sessions/{sessionId}",
                        ["actionLabel"] = "View Results"
                    };

                    var notificationEvent = new NotificationEvent(
                        Type: "training.session.completed",
                        Title: "Training Session Completed",
                        Message: message,
                        Entity: "TrainingSession",
                        Operation: null,
                        Metadata: mdCompleted,
                        Priority: priority
                    );

                    await _notificationService.PublishToUserAsync(notificationEvent, enrollment.WorkerId);
                }

                _logger.LogInformation("Sent session completed notifications for session {SessionId} to {Count} workers", 
                    sessionId, enrollments.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending session completed notifications for session {SessionId}", sessionId);
                throw;
            }
        }

        public async Task SendTradeEligibilityNotificationAsync(int workerId, int tradeId, Shared.Enums.WorkerEligibilityStatus status)
        {
            try
            {
                var eligibilityMessage = status switch
                {
                    Shared.Enums.WorkerEligibilityStatus.ELIGIBLE => "You are eligible to work in your assigned trade.",
                    Shared.Enums.WorkerEligibilityStatus.ELIGIBLE_WITHIN_GRACE => "Your trade eligibility is within grace period. Please renew required certificates soon.",
                    Shared.Enums.WorkerEligibilityStatus.NOT_ELIGIBLE => "You are not eligible to work in your assigned trade. Missing required certifications.",
                    _ => "Your trade eligibility status has been updated."
                };

                var priority = status == Shared.Enums.WorkerEligibilityStatus.NOT_ELIGIBLE 
                    ? NotificationPriority.High 
                    : NotificationPriority.Medium;

                var mdEligibility = new Dictionary<string, string>
                {
                    ["workerId"] = workerId.ToString(),
                    ["tradeId"] = tradeId.ToString(),
                    ["eligibilityStatus"] = status.ToString(),
                    ["checkedAt"] = DateTime.UtcNow.ToString("o"),
                    ["entityId"] = workerId.ToString(),
                    ["actionUrl"] = $"/training/eligibility/{workerId}",
                    ["actionLabel"] = "View Details"
                };

                var notificationEvent = new NotificationEvent(
                    Type: "training.trade.eligibility",
                    Title: "Trade Eligibility Status",
                    Message: eligibilityMessage,
                    Entity: "Worker",
                    Operation: null,
                    Metadata: mdEligibility,
                    Priority: priority
                );

                await _notificationService.PublishToUserAsync(notificationEvent, workerId);
                _logger.LogInformation("Sent trade eligibility notification to worker {WorkerId} for trade {TradeId} with status {Status}", 
                    workerId, tradeId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending trade eligibility notification to worker {WorkerId} for trade {TradeId}", 
                    workerId, tradeId);
                throw;
            }
        }

        // Batch process expired certificates (like a background job)
        public async Task ProcessExpiredCertificatesAsync()
        {
            try
            {
                var expiredCertificates = await _trainingService.GetExpiringCertificatesAsync(0); // Already expired
                
                foreach (var cert in expiredCertificates.Where(c => c.ExpiryDate < DateTime.UtcNow))
                {
                    // Update certificate status
                    await _trainingService.UpdateCertificateStatusAsync(cert.Id, Shared.Enums.CertificateStatus.EXPIRED);

                    // Send expired notification
                    var mdExpired = new Dictionary<string, string>
                    {
                        ["certificateId"] = cert.Id.ToString(),
                        ["programTitle"] = cert.Program.Title,
                        ["expiredDate"] = cert.ExpiryDate.ToString("o"),
                        ["entityId"] = cert.Id.ToString(),
                        ["actionUrl"] = $"/training/certificates/{cert.Id}",
                        ["actionLabel"] = "Renew Now"
                    };

                    var notificationEvent = new NotificationEvent(
                        Type: "training.certificate.expired",
                        Title: "Training Certificate Expired",
                        Message: $"Your {cert.Program.Title} certificate has expired. You are no longer eligible to perform related work until renewed.",
                        Entity: "TrainingCertificate",
                        Operation: null,
                        Metadata: mdExpired,
                        Priority: NotificationPriority.High
                    );

                    await _notificationService.PublishToUserAsync(notificationEvent, cert.WorkerId);
                }

                _logger.LogInformation("Processed {Count} expired certificates", expiredCertificates.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing expired certificates");
                throw;
            }
        }
    }
}

