using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Auth;
using GraphQLApi.Data;
using GraphQLApi.Auth;

namespace GraphQLApi.Services
{
    /// <summary>
    /// Enhanced authentication service with Identity integration and OWASP security measures
    /// Implements comprehensive security controls for authentication flows
    /// Addresses OWASP A01 (Broken Access Control), A07 (Identification and Authentication Failures)
    /// </summary>
    public class IdentityAuthenticationService : IAuthenticationService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IdentityJwtService _jwtService;
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly IAuditService _auditService;
        private readonly ILogger<IdentityAuthenticationService> _logger;
        private readonly JwtSettings _jwtSettings;

        public IdentityAuthenticationService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IdentityJwtService jwtService,
            IDbContextFactory<AppDbContext> dbContextFactory,
            IAuditService auditService,
            ILogger<IdentityAuthenticationService> logger,
            Microsoft.Extensions.Options.IOptions<JwtSettings> jwtSettings)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _jwtService = jwtService;
            _dbContextFactory = dbContextFactory;
            _auditService = auditService;
            _logger = logger;
            _jwtSettings = jwtSettings.Value;
        }

        public async Task<AuthenticationResult> LoginAsync(LoginRequest request)
        {
            try
            {
                // Input validation and sanitization
                if (string.IsNullOrWhiteSpace(request.Email) || string.IsNullOrWhiteSpace(request.Password))
                {
                    _logger.LogWarning("Login attempt with empty credentials from IP {IpAddress}", request.IpAddress);
                    return AuthenticationResult.Failed("Invalid credentials");
                }

                // Normalize email for consistent lookup
                var normalizedEmail = _userManager.NormalizeEmail(request.Email);
                
                // Use UserManager to find user (it handles its own context)
                var user = await _userManager.FindByEmailAsync(normalizedEmail);
                
                if (user == null)
                {
                    // Security: Don't reveal if user exists or not
                    await _auditService.LogAsync(AuditAction.FailedLogin, 
                        $"Login attempt with non-existent email: {request.Email}", null, request.IpAddress);
                    
                    _logger.LogWarning("Login attempt with non-existent email {Email} from IP {IpAddress}", 
                        request.Email, request.IpAddress);
                    
                    // Simulate processing time to prevent timing attacks
                    await Task.Delay(Random.Shared.Next(100, 500));
                    return AuthenticationResult.Failed("Invalid credentials");
                }

                // Tenant validation - critical security check
                if (user.TenantId != request.TenantId)
                {
                    await _auditService.LogAsync(AuditAction.FailedLogin, 
                        $"Tenant mismatch: User tenant {user.TenantId}, Request tenant {request.TenantId}", user.Id, request.IpAddress);
                    
                    _logger.LogWarning("Tenant mismatch for user {UserId}. User tenant: {UserTenant}, Request tenant: {RequestTenant}", 
                        user.Id, user.TenantId, request.TenantId);
                    
                    return AuthenticationResult.Failed("Invalid credentials");
                }

                // Check if account is locked
                if (await _userManager.IsLockedOutAsync(user))
                {
                    var lockoutEnd = await _userManager.GetLockoutEndDateAsync(user);
                    await _auditService.LogAsync(AuditAction.FailedLogin, 
                        $"Login attempt on locked account. Lockout ends: {lockoutEnd}", user.Id, request.IpAddress);
                    
                    _logger.LogWarning("Login attempt on locked account for user {UserId} from IP {IpAddress}", 
                        user.Id, request.IpAddress);
                    
                    return AuthenticationResult.Failed("Account is temporarily locked due to multiple failed attempts");
                }

                // Check if email is confirmed (if required)
                if (_userManager.Options.SignIn.RequireConfirmedEmail && !await _userManager.IsEmailConfirmedAsync(user))
                {
                    _logger.LogWarning("Login attempt with unconfirmed email for user {UserId}", user.Id);
                    return AuthenticationResult.Failed("Email address must be confirmed before login");
                }

                // Check if user is active
                if (!user.IsActive)
                {
                    await _auditService.LogAsync(AuditAction.FailedLogin, 
                        "Login attempt on inactive account", user.Id, request.IpAddress);
                    
                    _logger.LogWarning("Login attempt on inactive account for user {UserId}", user.Id);
                    return AuthenticationResult.Failed("Account is inactive");
                }

                // Validate password with Identity's secure mechanisms
                var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, lockoutOnFailure: true);
                
                if (result.Succeeded)
                {
                    // Reset failed login attempts on successful login
                    await _userManager.ResetAccessFailedCountAsync(user);
                    user.ResetFailedAttempts();
                    
                    // Update last login info
                    user.LastLoginAt = DateTime.UtcNow;
                    user.LastLoginIp = request.IpAddress;
                    await _userManager.UpdateAsync(user);

                    // Generate secure tokens
                    var accessToken = await _jwtService.GenerateAccessTokenAsync(user);
                    var refreshToken = await _jwtService.GenerateRefreshTokenAsync(user, request.IpAddress, request.UserAgent);

                    // Create session
                    var session = await CreateSessionAsync(user, request.IpAddress, request.UserAgent);

                    await _auditService.LogAsync(AuditAction.Login, "Successful login", user.Id, request.IpAddress);
                    
                    _logger.LogInformation("Successful login for user {UserId} from IP {IpAddress}", 
                        user.Id, request.IpAddress);

                    // Convert ApplicationUser to User for compatibility
                    var compatUser = user;
                    return AuthenticationResult.CreateSuccess(accessToken, refreshToken.Token, compatUser, session);
                }
                else if (result.IsLockedOut)
                {
                    var lockoutEnd = await _userManager.GetLockoutEndDateAsync(user);
                    await _auditService.LogAsync(AuditAction.AccountLocked, 
                        $"Account locked due to failed login attempts. Lockout ends: {lockoutEnd}", user.Id, request.IpAddress);
                    
                    _logger.LogWarning("Account locked for user {UserId} due to failed login attempts", user.Id);
                    return AuthenticationResult.Failed("Account locked due to multiple failed attempts");
                }
                else if (result.IsNotAllowed)
                {
                    _logger.LogWarning("Login not allowed for user {UserId}. Email confirmed: {EmailConfirmed}", 
                        user.Id, user.EmailConfirmed);
                    return AuthenticationResult.Failed("Login not allowed. Please confirm your email address");
                }
                else
                {
                    // Password validation failed
                    user.IncrementFailedAttempts();
                    await _userManager.UpdateAsync(user);
                    
                    await _auditService.LogAsync(AuditAction.FailedLogin, 
                        "Invalid password", user.Id, request.IpAddress);
                    
                    _logger.LogWarning("Invalid password for user {UserId} from IP {IpAddress}", 
                        user.Id, request.IpAddress);
                    
                    return AuthenticationResult.Failed("Invalid credentials");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed for email {Email} from IP {IpAddress}", 
                    request.Email, request.IpAddress);
                return AuthenticationResult.Failed("Login failed due to system error");
            }
        }

        public async Task<AuthenticationResult> RefreshTokenAsync(RefreshTokenRequest request)
        {
            try
            {
                var refreshToken = await _jwtService.GetRefreshTokenAsync(request.RefreshToken);
                if (refreshToken == null || !refreshToken.IsActive)
                {
                    _logger.LogWarning("Invalid or expired refresh token used from IP {IpAddress}", request.IpAddress);
                    return AuthenticationResult.Failed("Invalid refresh token");
                }

                // Security: Validate IP address consistency (optional strict mode)
                if (_jwtSettings.EnableStrictIpValidation && refreshToken.IpAddress != request.IpAddress)
                {
                    _logger.LogWarning("IP address mismatch for refresh token. Original: {OriginalIp}, Current: {CurrentIp}", 
                        refreshToken.IpAddress, request.IpAddress);
                    
                    // Revoke token for security
                    await _jwtService.RevokeRefreshTokenAsync(request.RefreshToken, "IP address mismatch");
                    return AuthenticationResult.Failed("Security validation failed");
                }

                // Find the ApplicationUser
                var user = await _userManager.FindByIdAsync(refreshToken.ApplicationUserId.ToString());
                if (user == null || !user.IsActive)
                {
                    _logger.LogWarning("User {UserId} not found or inactive during token refresh", refreshToken.ApplicationUserId);
                    return AuthenticationResult.Failed("User not found or inactive");
                }

                // Check if user is locked
                if (await _userManager.IsLockedOutAsync(user))
                {
                    _logger.LogWarning("Token refresh attempted for locked user {UserId}", user.Id);
                    return AuthenticationResult.Failed("Account is locked");
                }

                // Revoke old refresh token (rotation for security)
                await _jwtService.RevokeRefreshTokenAsync(request.RefreshToken, "Token rotated");

                // Generate new tokens
                var newAccessToken = await _jwtService.GenerateAccessTokenAsync(user);
                var newRefreshToken = await _jwtService.GenerateRefreshTokenAsync(user, request.IpAddress, request.UserAgent);

                await _auditService.LogAsync(AuditAction.Login, "Token refreshed", user.Id, request.IpAddress);
                
                _logger.LogDebug("Token refreshed for user {UserId} from IP {IpAddress}", user.Id, request.IpAddress);

                return AuthenticationResult.CreateSuccess(newAccessToken, newRefreshToken.Token, user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token refresh failed from IP {IpAddress}", request.IpAddress);
                return AuthenticationResult.Failed("Token refresh failed");
            }
        }

        public async Task<bool> LogoutAsync(LogoutRequest request)
        {
            try
            {
                // Revoke refresh token if provided
                if (!string.IsNullOrEmpty(request.RefreshToken))
                {
                    await _jwtService.RevokeRefreshTokenAsync(request.RefreshToken, "User logout");
                }

                // End session if provided
                if (!string.IsNullOrEmpty(request.SessionId))
                {
                    await EndSessionAsync(request.SessionId, "User logout");
                }

                await _auditService.LogAsync(AuditAction.Logout, "User logout", request.UserId, request.IpAddress);
                
                _logger.LogInformation("User {UserId} logged out from IP {IpAddress}", request.UserId, request.IpAddress);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed for user {UserId}", request.UserId);
                return false;
            }
        }

        public async Task<bool> LogoutAllSessionsAsync(int userId)
        {
            try
            {
                // Revoke all refresh tokens
                await _jwtService.RevokeAllUserTokensAsync(userId, "Logout all sessions");

                // End all sessions
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var activeSessions = await context.UserSessions
                    .Where(s => s.ApplicationUserId == userId && s.IsActive)
                    .ToListAsync();

                foreach (var session in activeSessions)
                {
                    session.IsActive = false;
                    session.EndedAt = DateTime.UtcNow;
                    session.EndReason = "Logout all sessions";
                }

                await context.SaveChangesAsync();

                // Update security stamp to invalidate all existing JWT tokens
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user != null)
                {
                    await _userManager.UpdateSecurityStampAsync(user);
                }

                await _auditService.LogAsync(AuditAction.Logout, "All sessions logged out", userId, "system");
                
                _logger.LogInformation("All sessions logged out for user {UserId}", userId);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to logout all sessions for user {UserId}", userId);
                return false;
            }
        }


        public async Task<UserSession> CreateSessionAsync(ApplicationUser user, string ipAddress, string userAgent)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            var session = new UserSession
            {
                SessionId = Guid.NewGuid().ToString(),
                ApplicationUserId = user.Id,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                CreatedAt = DateTime.UtcNow,
                LastActivityAt = DateTime.UtcNow,
                IsActive = true,
                ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.SessionTimeoutMinutes)
            };

            context.UserSessions.Add(session);
            await context.SaveChangesAsync();

            _logger.LogDebug("Session created for user {UserId}: {SessionId}", user.Id, session.SessionId);
            
            return session;
        }

        public async Task<bool> EndSessionAsync(string sessionId, string reason)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var session = await context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId && s.IsActive);

                if (session == null)
                {
                    _logger.LogWarning("Session not found or already ended: {SessionId}", sessionId);
                    return false;
                }

                session.IsActive = false;
                session.EndedAt = DateTime.UtcNow;
                session.EndReason = reason;

                await context.SaveChangesAsync();

                _logger.LogDebug("Session ended: {SessionId}, Reason: {Reason}", sessionId, reason);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to end session {SessionId}", sessionId);
                return false;
            }
        }

        public async Task<IEnumerable<UserSession>> GetActiveSessionsAsync(int userId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.UserSessions
                .Where(s => s.ApplicationUserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow)
                .OrderByDescending(s => s.LastActivityAt)
                .ToListAsync();
        }

        public async Task CleanupExpiredSessionsAsync()
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var expiredSessions = await context.UserSessions
                    .Where(s => s.IsActive && s.ExpiresAt <= DateTime.UtcNow)
                    .ToListAsync();

                foreach (var session in expiredSessions)
                {
                    session.IsActive = false;
                    session.EndedAt = DateTime.UtcNow;
                    session.EndReason = "Session expired";
                }

                await context.SaveChangesAsync();

                _logger.LogInformation("Cleaned up {Count} expired sessions", expiredSessions.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup expired sessions");
            }
        }

        public async Task<bool> ValidateSessionAsync(string sessionId)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var session = await context.UserSessions
                    .FirstOrDefaultAsync(s => s.SessionId == sessionId);

                if (session == null || !session.IsActive || session.ExpiresAt <= DateTime.UtcNow)
                {
                    return false;
                }

                // Update last activity
                session.LastActivityAt = DateTime.UtcNow;
                session.ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtSettings.SessionTimeoutMinutes);
                
                await context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Session validation failed for {SessionId}", sessionId);
                return false;
            }
        }

    }

}

