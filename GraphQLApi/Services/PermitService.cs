using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Services
{
    public class PermitService : IPermitService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly IToolboxService _toolboxService;
        private readonly ILogger<PermitService> _logger;

        // Valid state transitions as defined in permit_methods.jsonc
        private readonly Dictionary<PermitStatus, List<PermitStatus>> _validStateTransitions = new()
        {
            // { PermitStatus.DRAFTED, new List<PermitStatus> { PermitStatus.PENDING_APPROVAL } },
            // { PermitStatus.PENDING_APPROVAL, new List<PermitStatus> { PermitStatus.OPENED, PermitStatus.DISAPPROVED } },
            // { PermitStatus.DISAPPROVED, new List<PermitStatus> { PermitStatus.DRAFTED } },
            { PermitStatus.DRAFTED, new List<PermitStatus> { PermitStatus.OPENED } },
            { PermitStatus.OPENED, new List<PermitStatus> { PermitStatus.PENDING_CLOSURE, PermitStatus.CANCELLED, PermitStatus.EXPIRED } },
            { PermitStatus.PENDING_CLOSURE, new List<PermitStatus> { PermitStatus.CLOSED, PermitStatus.VOIDED } },
            { PermitStatus.CANCELLED, new List<PermitStatus>() },
            { PermitStatus.EXPIRED, new List<PermitStatus>(){PermitStatus.PENDING_CLOSURE} },
            { PermitStatus.CLOSED, new List<PermitStatus>() },
            { PermitStatus.VOIDED, new List<PermitStatus>() }
        };

        public PermitService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            IToolboxService toolboxService,
            ILogger<PermitService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _toolboxService = toolboxService;
            _logger = logger;
        }

        public async Task<IEnumerable<ToolboxRiskAssessment>> GetToolboxRiskAssessmentAsync(int jobId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            var result = new List<ToolboxRiskAssessment>();

            foreach (var hazard in job.Hazards)
            {
                result.Add(new ToolboxRiskAssessment
                {
                    Id = job.Id,
                    Title = job.Title,
                    Hazards = job.Hazards.Select(h => new ToolboxRiskAssessmentHazard
                    {
                        Id = h.Id,
                        Description = h.Description,
                        ControlMeasures = h.ControlMeasures.Select(cm => new ToolboxRiskAssessmentControlMeasure
                        {
                            Id = cm.Id,
                            Description = cm.Description
                        })
                    })
                });
            }

            return result;
        }

        public async Task<bool> VerifySignoffAsync(IEnumerable<int> workerIds)
        {
            var todaysToolbox = await _toolboxService.GetTodaysToolboxAsync();

            if (todaysToolbox == null)
                return false;

            // Check if toolbox is in correct status
            if (todaysToolbox.Status != ToolboxStatus.PENDING_CLOSURE && todaysToolbox.Status != ToolboxStatus.CLOSED)
                return false;

            // Check if all workers are in the attendance
            var attendeeWorkerIds = todaysToolbox.Attendees.Select(a => a.WorkerId).ToHashSet();

            return workerIds.All(attendeeWorkerIds.Contains);
        }

        public async Task<GeneralWorkPermit> CreateGeneralWorkPermitAsync(CreateGeneralWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var fileMetaDatas = new List<FileMetadata>();
            try
            {

                // Validate job exists and requires permits
                var job = await context.Jobs
                    .Include(j => j.Permits)
                    .FirstOrDefaultAsync(j => j.Id == input.JobId);

                if (job == null)
                    throw new ArgumentException($"Job with ID {input.JobId} not found");

                if (!job.RequiredPermits.Contains(PermitType.GENERAL_WORK_PERMIT))
                    throw new InvalidOperationException("Job does not require a General Work Permit");

                if (job.Status != JobStatus.APPROVED)
                    throw new InvalidOperationException("Job must be approved before creating a General Work Permit");
                if (job.Permits.Any(p => p.PermitType == PermitType.GENERAL_WORK_PERMIT))
                    throw new InvalidOperationException("Job already has a General Work Permit");

                // Verify signoff
                // var workerIds = input.SignOff.Workers.Select(w => w.WorkerId);
                var workers = await context.Workers
                    .Where(w => input.SignOff.WorkerIds.Contains(w.Id))
                    .ToListAsync() ?? [];

                if (workers.Count != input.SignOff.WorkerIds.Count)
                    throw new ArgumentException("One or more workers not found");

                if (!await VerifySignoffAsync(input.SignOff.WorkerIds))
                    throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

                // Copy signature files to temp bucket
                var competentPersons = await context.Workers
                    .Where(w => input.PermitIssuer.CompetentPersonIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (competentPersons.Count != input.PermitIssuer.CompetentPersonIds.Count())
                    throw new ArgumentException("One or more competent persons not found");

                if (competentPersons.Count == 0)
                    throw new ArgumentException("At least one competent person is required");
                
                var authorisedPersons = await context.Workers
                    .Where(w => input.PermitIssuer.AuthorisedPersonsIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (authorisedPersons.Count != input.PermitIssuer.AuthorisedPersonsIds.Count())
                    throw new ArgumentException("One or more authorised persons not found");
                if (authorisedPersons.Count == 0)
                    throw new ArgumentException("At least one authorised person is required");

                var processedPermitIssuer = await ProcessPermitIssuerSignatures(competentPersons, authorisedPersons, input.JobId, PermitType.GENERAL_WORK_PERMIT);
                var processedSignOff = await ProcessSignOffSignatures(workers, input.JobId, PermitType.GENERAL_WORK_PERMIT);

                var permit = new GeneralWorkPermit
                {
                    JobId = input.JobId,
                    PTWRefNumber = input.PTWRefNumber,
                    ProjectName = input.ProjectName,
                    // StartingDateTime = input.StartingDateTime,
                    // EndingDateTime = input.EndingDateTime,
                    // Description = input.Description,
                    // Location = input.Location,
                    // Hazards = input.Hazards,
                    // PrecautionsRequired = input.PrecautionsRequired,
                    // PPE = input.PPE,
                    Isolation = input.Isolation,
                    Inspections = input.Inspections,
                    PermitIssuer = processedPermitIssuer,
                    SignOff = processedSignOff,
                    Status = DetermineInitialPermitStatus(job)
                };
                // Upload attendance picture
                if (input.AttendancePictureFile != null)
                {
                    var attendancePictureMetadata = await ProcessAttendancePicture(permit, input.JobId, input.AttendancePictureFile);
                    permit.AttendancePictureFileId = attendancePictureMetadata.Id;
                    fileMetaDatas.Add(attendancePictureMetadata);
                }

                context.GeneralWorkPermits.Add(permit);
                await context.SaveChangesAsync();

                return permit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create General Work Permit for job {JobId}", input.JobId);
                foreach (var fileMetadata in fileMetaDatas)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        public async Task<HotWorkPermit> CreateHotWorkPermitAsync(CreateHotWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var fileMetaDatas = new List<FileMetadata>();
            try
            {
                var job = await context.Jobs
                    .Include(j => j.Permits)
                    .FirstOrDefaultAsync(j => j.Id == input.JobId) ?? throw new ArgumentException($"Job with ID {input.JobId} not found");

                if (!job.RequiredPermits.Contains(PermitType.HOT_WORK_PERMIT))
                    throw new InvalidOperationException("Job does not require a Hot Work Permit");
                if (job.Status != JobStatus.APPROVED)
                    throw new InvalidOperationException("Job must be approved before creating a Hot Work Permit");
                if (job.Permits.Any(p => p.PermitType == PermitType.HOT_WORK_PERMIT))
                    throw new InvalidOperationException("Job already has a Hot Work Permit");

                var fireSafetySupervisor = await context.Workers
                    .FirstOrDefaultAsync(w => w.Id == input.FireSafetySupervisorId) ?? throw new ArgumentException($"Fire safety supervisor with ID {input.FireSafetySupervisorId} not found");

                // todo: check if has the relevant training

                var workers = context.Workers
                    .Where(w => input.SignOff.WorkerIds.Contains(w.Id))
                    .ToList();
                if (workers.Count != input.SignOff.WorkerIds.Count)
                    throw new ArgumentException("One or more workers not found");

                if (!await VerifySignoffAsync(input.SignOff.WorkerIds.Concat([fireSafetySupervisor.Id])))
                    throw new InvalidOperationException("Signoff verification failed. Workers and fire safety supervisor must be in today's toolbox attendance.");

                var competentPersons = await context.Workers
                    .Where(w => input.PermitIssuer.CompetentPersonIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (competentPersons.Count != input.PermitIssuer.CompetentPersonIds.Count)
                    throw new ArgumentException("One or more competent persons not found");
                if (competentPersons.Count == 0)
                    throw new ArgumentException("At least one competent person is required");

                var athorisedPersons = await context.Workers
                    .Where(w => input.PermitIssuer.AuthorisedPersonsIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (athorisedPersons.Count != input.PermitIssuer.AuthorisedPersonsIds.Count)
                    throw new ArgumentException("One or more authorised persons not found");
                if (athorisedPersons.Count == 0)
                    throw new ArgumentException("At least one authorised person is required");

                var processedPermitIssuer = await ProcessPermitIssuerSignatures(competentPersons, athorisedPersons, input.JobId, PermitType.HOT_WORK_PERMIT);
                var processedSignOff = await ProcessSignOffSignatures(workers, input.JobId, PermitType.HOT_WORK_PERMIT);
                var processedFireSafetySupervisor = await ProcessFireSafetySupervisorSignature(fireSafetySupervisor, input.JobId);

                var permit = new HotWorkPermit
                {
                    JobId = input.JobId,
                    PTWRefNumber = input.PTWRefNumber,
                    ProjectName = input.ProjectName,
                    // StartingDateTime = input.StartingDateTime,
                    // EndingDateTime = input.EndingDateTime,
                    // Description = input.Description,
                    // Location = input.Location,
                    // Hazards = input.Hazards,
                    // PrecautionsRequired = input.PrecautionsRequired,
                    // PPE = input.PPE,
                    // NatureOfWork = input.NatureOfWork,
                    // FireExtinguishers = input.FireExtinguishers,
                    Inspections = input.Inspections,
                    Isolation = input.Isolation,
                    
                    FireSafetySupervisor = processedFireSafetySupervisor,
                    PermitIssuer = processedPermitIssuer,
                    SignOff = processedSignOff,
                    Status = DetermineInitialPermitStatus(job)
                };
                if (input.AttendancePictureFile != null)
                {
                    var attendancePictureMetadata = await ProcessAttendancePicture(permit, input.JobId, input.AttendancePictureFile);
                    permit.AttendancePictureFileId = attendancePictureMetadata.Id;
                    fileMetaDatas.Add(attendancePictureMetadata);
                }

                // if (permit.Status == PermitStatus.PENDING_APPROVAL)
                // {
                //     await LinkRelatedPermits(context, job, permit);
                // }

                context.HotWorkPermits.Add(permit);
                await context.SaveChangesAsync();

                return permit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create Hot Work Permit for job {JobId}", input.JobId);
                foreach (var fileMetadata in fileMetaDatas)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        public async Task<ExcavationWorkPermit> CreateExcavationWorkPermitAsync(CreateExcavationWorkPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var fileMetaDatas = new List<FileMetadata>();
            try
            {
                var job = await context.Jobs
                    .Include(j => j.Permits)
                    .FirstOrDefaultAsync(j => j.Id == input.JobId) ?? throw new ArgumentException($"Job with ID {input.JobId} not found");

                if (!job.RequiredPermits.Contains(PermitType.EXCAVATION_PERMIT))
                    throw new InvalidOperationException("Job does not require an Excavation Work Permit");

                if (job.Status != JobStatus.APPROVED)
                    throw new InvalidOperationException("Job must be approved before creating an Excavation Work Permit");
                if (job.Permits.Any(p => p.PermitType == PermitType.EXCAVATION_PERMIT))
                    throw new InvalidOperationException("Job already has an Excavation Work Permit");

                var workers = await context.Workers
                    .Where(w => input.SignOff.WorkerIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (workers.Count != input.SignOff.WorkerIds.Count)
                    throw new ArgumentException("One or more workers not found");


                if (!await VerifySignoffAsync(input.SignOff.WorkerIds))
                    throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

                var competentPersons = await context.Workers
                    .Where(w => input.PermitIssuer.CompetentPersonIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (competentPersons.Count != input.PermitIssuer.CompetentPersonIds.Count)
                    throw new ArgumentException("One or more competent persons not found");
                if (competentPersons.Count == 0)
                    throw new ArgumentException("At least one competent person is required");
                var athorisedPersons = await context.Workers
                    .Where(w => input.PermitIssuer.AuthorisedPersonsIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (athorisedPersons.Count != input.PermitIssuer.AuthorisedPersonsIds.Count)
                    throw new ArgumentException("One or more authorised persons not found");
                if (athorisedPersons.Count == 0)
                    throw new ArgumentException("At least one authorised person is required");

                var processedPermitIssuer = await ProcessPermitIssuerSignatures(competentPersons, athorisedPersons, input.JobId, PermitType.EXCAVATION_PERMIT);
                var processedSignOff = await ProcessSignOffSignatures(workers, input.JobId, PermitType.EXCAVATION_PERMIT);
                var processedInspectionAuth = await ProcessInspectionAuthorizationSignature(input.InspectionAuthorization);

                var permit = new ExcavationWorkPermit
                {
                    JobId = input.JobId,
                    PTWRefNumber = input.PTWRefNumber,
                    ProjectName = input.ProjectName,
                    // StartingDateTime = input.StartingDateTime,
                    // EndingDateTime = input.EndingDateTime,
                    // Description = input.Description,
                    // Location = input.Location,
                    // Hazards = input.Hazards,
                    // PrecautionsRequired = input.PrecautionsRequired,
                    // PPE = input.PPE,
                    // DepthOfExcavation = input.DepthOfExcavation,
                    // ProtectionSystems = input.ProtectionSystems,
                    // ListOfEquipmentToBeUsed = input.ListOfEquipmentToBeUsed,
                    Inspections = input.Inspections,
                    Isolation = input.Isolation,
                    InspectionAuthorization = processedInspectionAuth,
                    PermitIssuer = processedPermitIssuer,
                    SignOff = processedSignOff,
                    Status = DetermineInitialPermitStatus(job)
                };

                // Upload attendance picture
                if (input.AttendancePictureFile != null)
                {
                    var attendancePictureMetadata = await ProcessAttendancePicture(permit, input.JobId, input.AttendancePictureFile);
                    permit.AttendancePictureFileId = attendancePictureMetadata.Id;
                    fileMetaDatas.Add(attendancePictureMetadata);
                }

                // if (permit.Status == PermitStatus.PENDING_APPROVAL)
                // {
                //     await LinkRelatedPermits(context, job, permit);
                // }

                context.ExcavationWorkPermits.Add(permit);
                await context.SaveChangesAsync();

                return permit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create Excavation Work Permit for job {JobId}", input.JobId);
                foreach (var fileMetadata in fileMetaDatas)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        public async Task<WorkAtHeightPermit> CreateWorkAtHeightPermitAsync(CreateWorkAtHeightPermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var fileMetaDatas = new List<FileMetadata>();
            try
            {
                var job = await context.Jobs
                    .Include(j => j.Permits)
                    .FirstOrDefaultAsync(j => j.Id == input.JobId) ?? throw new ArgumentException($"Job with ID {input.JobId} not found");

                if (!job.RequiredPermits.Contains(PermitType.WORK_AT_HEIGHT_PERMIT))
                    throw new InvalidOperationException("Job does not require a Work at Height Permit");
                if (job.Status != JobStatus.APPROVED)
                    throw new InvalidOperationException("Job must be approved before creating a Work at Height Permit");
                if (job.Permits.Any(p => p.PermitType == PermitType.WORK_AT_HEIGHT_PERMIT))
                    throw new InvalidOperationException("Job already has a Work at Height Permit");

                var workers = await context.Workers
                    .Where(w => input.SignOff.WorkerIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (workers.Count != input.SignOff.WorkerIds.Count)
                    throw new ArgumentException("One or more workers not found");

                if (!await VerifySignoffAsync(input.SignOff.WorkerIds))
                    throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");

                var competentPersons = await context.Workers
                    .Where(w => input.PermitIssuer.CompetentPersonIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (competentPersons.Count != input.PermitIssuer.CompetentPersonIds.Count)
                    throw new ArgumentException("One or more competent persons not found");
                if (competentPersons.Count == 0)
                    throw new ArgumentException("At least one competent person is required");
                var athorisedPersons = await context.Workers
                    .Where(w => input.PermitIssuer.AuthorisedPersonsIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (athorisedPersons.Count != input.PermitIssuer.AuthorisedPersonsIds.Count)
                    throw new ArgumentException("One or more authorised persons not found");
                if (athorisedPersons.Count == 0)
                    throw new ArgumentException("At least one authorised person is required");


                var processedPermitIssuer = await ProcessPermitIssuerSignatures(competentPersons, athorisedPersons, input.JobId, PermitType.WORK_AT_HEIGHT_PERMIT);
                var processedSignOff = await ProcessSignOffSignatures(workers, input.JobId, PermitType.WORK_AT_HEIGHT_PERMIT);
                var processedInspectionAuth = await ProcessInspectionAuthorizationSignature(input.InspectionAuthorization);

                var permit = new WorkAtHeightPermit
                {
                    JobId = input.JobId,
                    PTWRefNumber = input.PTWRefNumber,
                    ProjectName = input.ProjectName,
                    // StartingDateTime = input.StartingDateTime,
                    // EndingDateTime = input.EndingDateTime,
                    // Description = input.Description,
                    // Location = input.Location,
                    // Hazards = input.Hazards,
                    // PrecautionsRequired = input.PrecautionsRequired,
                    // PPE = input.PPE,
                    // ModeOfAccessToBeUsed = input.ModeOfAccessToBeUsed,
                    Inspections = input.Inspections,
                    Isolation = input.Isolation,
                    InspectionAuthorization = processedInspectionAuth,
                    PermitIssuer = processedPermitIssuer,
                    SignOff = processedSignOff,
                    Status = DetermineInitialPermitStatus(job)
                };
                if (input.AttendancePictureFile != null)
                {
                    var attendancePictureMetadata = await ProcessAttendancePicture(permit, input.JobId, input.AttendancePictureFile);
                    permit.AttendancePictureFileId = attendancePictureMetadata.Id;
                    fileMetaDatas.Add(attendancePictureMetadata);
                }
                // if (permit.Status == PermitStatus.PENDING_APPROVAL)
                // {
                //     await LinkRelatedPermits(context, job, permit);
                // }

                context.WorkAtHeightPermits.Add(permit);
                await context.SaveChangesAsync();

                return permit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create Work at Height Permit for job {JobId}", input.JobId);
                foreach (var fileMetadata in fileMetaDatas)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        public async Task<ConfinedSpacePermit> CreateConfinedSpacePermitAsync(CreateConfinedSpacePermitInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var fileMetaDatas = new List<FileMetadata>();
            try
            {
                var job = await context.Jobs
                    .Include(j => j.Permits)
                    .FirstOrDefaultAsync(j => j.Id == input.JobId) ?? throw new ArgumentException($"Job with ID {input.JobId} not found");

                if (!job.RequiredPermits.Contains(PermitType.CONFINED_SPACE_ENTRY_PERMIT))
                    throw new InvalidOperationException("Job does not require a Confined Space Entry Permit");
                if (job.Status != JobStatus.APPROVED)
                    throw new InvalidOperationException("Job must be approved before creating a Confined Space Entry Permit");
                if (job.Permits.Any(p => p.PermitType == PermitType.CONFINED_SPACE_ENTRY_PERMIT))
                    throw new InvalidOperationException("Job already has a Confined Space Entry Permit");

                var workers = await context.Workers
                    .Where(w => input.SignOff.WorkerIds.Contains(w.Id))
                    .ToListAsync() ?? [];

                if (workers.Count != input.SignOff.WorkerIds.Count)
                    throw new ArgumentException("One or more workers not found");

                var taskObserver = await context.Workers
                    .FirstOrDefaultAsync(w => w.Id == input.TaskObserver.WorkerId) ?? throw new ArgumentException($"Task observer with ID {input.TaskObserver.WorkerId} not found");

                if (!await VerifySignoffAsync(input.SignOff.WorkerIds.Concat([taskObserver.Id])))
                    throw new InvalidOperationException("Signoff verification failed. Workers must be in today's toolbox attendance.");
                var competentPersons = await context.Workers
                    .Where(w => input.PermitIssuer.CompetentPersonIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (competentPersons.Count != input.PermitIssuer.CompetentPersonIds.Count)
                    throw new ArgumentException("One or more competent persons not found");
                if (competentPersons.Count == 0)
                    throw new ArgumentException("At least one competent person is required");
                var athorisedPersons = await context.Workers
                    .Where(w => input.PermitIssuer.AuthorisedPersonsIds.Contains(w.Id))
                    .ToListAsync() ?? [];
                if (athorisedPersons.Count != input.PermitIssuer.AuthorisedPersonsIds.Count)
                    throw new ArgumentException("One or more authorised persons not found");
                if (athorisedPersons.Count == 0)
                    throw new ArgumentException("At least one authorised person is required");

                var processedPermitIssuer = await ProcessPermitIssuerSignatures(competentPersons, athorisedPersons, input.JobId, PermitType.CONFINED_SPACE_ENTRY_PERMIT);
                var processedSignOff = await ProcessSignOffSignatures(workers, input.JobId, PermitType.CONFINED_SPACE_ENTRY_PERMIT);
                var processedTaskObserver = await ProcessTaskObserverSignature(taskObserver, input.JobId);

                var permit = new ConfinedSpacePermit
                {
                    JobId = input.JobId,
                    PTWRefNumber = input.PTWRefNumber,
                    ProjectName = input.ProjectName,
                    // StartingDateTime = input.StartingDateTime,
                    // EndingDateTime = input.EndingDateTime,
                    // Description = input.Description,
                    // Location = input.Location,
                    // Hazards = input.Hazards,
                    // PrecautionsRequired = input.PrecautionsRequired,
                    // PPE = input.PPE,
                    Inspections = input.Inspections,
                    Isolation = input.Isolation,
                    WorkersHaveBeenTrained = input.WorkersHaveBeenTrained,
                    NameOfTrainingOrganization = input.NameOfTrainingOrganization,
                    TopReading = new AtmosphericReading
                    {
                        Oxygen = input.TopReading.Oxygen,
                        Explosive = input.TopReading.Explosive,
                        Toxic = input.TopReading.Toxic,
                        Co2 = input.TopReading.Co2
                    },
                    MidReading = new AtmosphericReading
                    {
                        Oxygen = input.MidReading.Oxygen,
                        Explosive = input.MidReading.Explosive,
                        Toxic = input.MidReading.Toxic,
                        Co2 = input.MidReading.Co2
                    },
                    BottomReading = new AtmosphericReading
                    {
                        Oxygen = input.BottomReading.Oxygen,
                        Explosive = input.BottomReading.Explosive,
                        Toxic = input.BottomReading.Toxic,
                        Co2 = input.BottomReading.Co2
                    },
                    EmergencyGuidelines = input.EmergencyGuidelines,
                    TaskObserver = processedTaskObserver,
                    PermitIssuer = processedPermitIssuer,
                    SignOff = processedSignOff,
                    Status = DetermineInitialPermitStatus(job)
                };
                if (input.AttendancePictureFile != null)
                {
                    var attendancePictureMetadata = await ProcessAttendancePicture(permit, input.JobId, input.AttendancePictureFile);
                    permit.AttendancePictureFileId = attendancePictureMetadata.Id;
                    fileMetaDatas.Add(attendancePictureMetadata);
                }

                // if (permit.Status == PermitStatus.PENDING_APPROVAL)
                // {
                //     await LinkRelatedPermits(context, job, permit);
                // }

                context.ConfinedSpacePermits.Add(permit);
                await context.SaveChangesAsync();

                return permit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create Confined Space Entry Permit for job {JobId}", input.JobId);
                foreach (var fileMetadata in fileMetaDatas)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        private static PermitStatus DetermineInitialPermitStatus(Job job)
        {
            var requiredPermitTypes = job.RequiredPermits.ToList();
            var existingPermitTypes = job.Permits.Select(p => p.PermitType).ToList();

            // If only one permit is needed for the job
            if (requiredPermitTypes.Count == 1)
            {
                return PermitStatus.OPENED; // Open all permits
            }

            // If more than one permit is needed
            // Check if all needed permits have been drafted
            var missingPermitTypes = requiredPermitTypes.Except(existingPermitTypes).ToList();

            // If this is the last permit needed
            if (missingPermitTypes.Count == 1)
            {
                // Open all permits required
                foreach (var permit in job.Permits)
                {
                    permit.Status = PermitStatus.OPENED;
                    Console.WriteLine($"Opening permit {permit.Id}");
                    // Link all permits
                    foreach (var externalPermit in job.Permits)
                    {
                        if (externalPermit.Id != permit.Id)
                        {
                            permit.OtherPermitsInUse.Add(externalPermit);
                            externalPermit.OtherPermitsInUse.Add(permit);
                        }
                    }
                }
                return PermitStatus.OPENED;
            }

            // Otherwise, status is drafted
            return PermitStatus.DRAFTED;
        }

        private async Task<PermitIssuer> ProcessPermitIssuerSignatures(List<Worker> competentPersons, List<Worker> authorisedPersons, int jobId, PermitType permitType)
        {
            var processedCompetentPersons = new List<CompetentPerson>();
            var processedAuthorisedPersons = new List<AuthorisedPerson>();

            foreach (var cp in competentPersons)
            {
                if (!cp.SignatureFileId.HasValue)
                    throw new ArgumentException($"Competent person with ID {cp.Id} has no signature file");
                var signatureFileId = await CopySignatureToTempAsync(cp.SignatureFileId.Value, jobId, permitType);
                processedCompetentPersons.Add(new CompetentPerson
                {
                    WorkerId = cp.Id,
                    Name = cp.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = DateTime.UtcNow
                });
            }

            foreach (var ap in authorisedPersons)
            {
                if (!ap.SignatureFileId.HasValue)
                    throw new ArgumentException($"Authorised person with ID {ap.Id} has no signature file");
                var signatureFileId = await CopySignatureToTempAsync(ap.SignatureFileId.Value, jobId, permitType);
                processedAuthorisedPersons.Add(new AuthorisedPerson
                {
                    WorkerId = ap.Id,
                    Name = ap.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = DateTime.UtcNow
                });
            }

            return new PermitIssuer
            {
                CompetentPersons = processedCompetentPersons,
                AuthorisedPersons = processedAuthorisedPersons
            };
        }

        private async Task<SignOff> ProcessSignOffSignatures(List<Worker> workers, int jobId, PermitType permitType)
        {
            var processedWorkers = new List<PermitWorker>();

            foreach (var worker in workers)
            {
                if (!worker.SignatureFileId.HasValue)
                    throw new ArgumentException($"Worker with ID {worker.Id} has no signature file");
                var signatureFileId = await CopySignatureToTempAsync(worker.SignatureFileId.Value, jobId, permitType);
                processedWorkers.Add(new PermitWorker
                {
                    WorkerId = worker.Id,
                    Designation = worker.Trades.FirstOrDefault()?.Name ?? "CASUAL",
                    Name = worker.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = DateTime.UtcNow
                });
            }

            return new SignOff
            {
                DateTime = DateTime.UtcNow,
                Workers = processedWorkers
            };
        }

        private async Task<FireSafetySupervisor> ProcessFireSafetySupervisorSignature(Worker fireSafetySupervisor, int jobId)
        {
            if (!fireSafetySupervisor.SignatureFileId.HasValue)
                throw new ArgumentException("Fire safety supervisor has no signature file");
            var signatureFileId = await CopySignatureToTempAsync(fireSafetySupervisor.SignatureFileId.Value, jobId, PermitType.HOT_WORK_PERMIT);

            return new FireSafetySupervisor
            {
                WorkerId = fireSafetySupervisor.Id,
                Name = fireSafetySupervisor.Name,
                SignatureFileId = signatureFileId,
                SignedAt = DateTime.UtcNow
            };
        }

        private async Task<string> CopySignatureToTempAsync(int sourceFileId, int jobId, PermitType permitType)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId) ?? throw new ArgumentException($"Source file with ID {sourceFileId} not found");

                // ! Keep this naming convention since it's used in ClearPermitTempFolderAsync
                var tempFolderPath = $"{nameof(permitType)}_{jobId}/signatures";
                var tempFileName = $"{System.IO.Path.GetFileNameWithoutExtension(sourceFile.FileName)}_{Guid.NewGuid()}.{System.IO.Path.GetExtension(sourceFile.FileName)}";

                using var sourceStream = await _minioService.DownloadFileAsync(sourceFile);

                var copiedFileMetadata = await _minioService.UploadFileAsync(
                    sourceStream,
                    tempFileName,
                    Shared.Constants.FileStorageConstants.BucketNames.TEMP,
                    sourceFile.ContentType,
                    $"Temporary copy of signature for {nameof(permitType)} {jobId}",
                    tempFolderPath,
                    false, // Not public
                           // DateTime.UtcNow.AddDays(30) // Expire in 30 days
                    null
                );
                _logger.LogInformation("Successfully copied signature file {SourceFileId} to temp bucket as {CopiedFileId}",
                    sourceFileId, copiedFileMetadata.Id);

                return copiedFileMetadata.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to temp bucket", sourceFileId);
                throw new InvalidOperationException("Failed to copy signature file", ex);
            }
        }

        private static Task<InspectionAuthorization> ProcessInspectionAuthorizationSignature(InspectionAuthorizationInput input)
        {
            return System.Threading.Tasks.Task.FromResult(new InspectionAuthorization
            {
                NameOfInspector = input.NameOfInspector,
                Designation = input.Designation,
                DateOfInspection = input.DateOfInspection,
                Comments = input.Comments
            });
        }

        private async Task<TaskObserver> ProcessTaskObserverSignature(Worker input, int jobId)
        {
            if (!input.SignatureFileId.HasValue)
                throw new ArgumentException("Task observer has no signature file");
            var signatureFileId = await CopySignatureToTempAsync(input.SignatureFileId.Value, jobId, PermitType.CONFINED_SPACE_ENTRY_PERMIT);

            return new TaskObserver
            {
                WorkerId = input.Id,
                Name = input.Name,
                SignatureFileId = signatureFileId,
                SignedAt = DateTime.UtcNow
            };
        }

        public async Task<IEnumerable<Permit>> GetPermitsByJobAsync(int jobId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.JobId == jobId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Permit>> GetPermitsByStatusAsync(PermitStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Permit>> GetPermitsByDateAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Where(p => p.CreatedAt.Date == date.Date)
                .ToListAsync();
        }

        public async Task<Permit> CancelPermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.CANCELLED);

            permit.Status = PermitStatus.CANCELLED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task<Permit> ReturnPermitAsync(int permitId, PermitReturnInput permitReturn)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.PENDING_CLOSURE);

            // Process permit return signatures
            var processedPermitReturn = await ProcessPermitReturnSignatures(permitReturn, permit);

            // Copy permit issuer to permit return and add the argument
            permit.PermitReturn = new PermitReturn
            {
                CompetentPersons = permit.PermitIssuer.CompetentPersons.Concat(processedPermitReturn.CompetentPersons).ToList(),
                AuthorisedPersons = permit.PermitIssuer.AuthorisedPersons.Concat(processedPermitReturn.AuthorisedPersons).ToList()
            };

            permit.Status = PermitStatus.PENDING_CLOSURE;

            await context.SaveChangesAsync();

            return permit;
        }

        public async Task<Permit> VoidPermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.VOIDED);

            permit.Status = PermitStatus.VOIDED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task<Permit> ClosePermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.CLOSED);

            permit.Status = PermitStatus.CLOSED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }
        public async Task<Permit> ExpirePermitAsync(int permitId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var permit = await context.Permits
                .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

            ValidateStateTransition(permit.Status, PermitStatus.EXPIRED);

            permit.Status = PermitStatus.EXPIRED;

            await context.SaveChangesAsync();

            // TODO: notification to @admin HSE, @site HSE, and @client added here

            return permit;
        }

        public async Task ClearPermitTempFolderAsync(int permitId)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                var permit = await context.Permits
                    .FirstOrDefaultAsync(p => p.Id == permitId) ?? throw new ArgumentException($"Permit with ID {permitId} not found");

                // Only clear temp folder for permits with no other state change allowed

                var finalStates = new[] { PermitStatus.CANCELLED, PermitStatus.VOIDED, PermitStatus.CLOSED }; // may add EXPIRIED later

                if (!finalStates.Contains(permit.Status))
                    throw new InvalidOperationException("Can only clear temp folder for permits in final states");

                await _minioService.DeleteFolderSafeAsync(Shared.Constants.FileStorageConstants.BucketNames.TEMP, $"{nameof(permit.PermitType)}_{permit.JobId}/");
                _logger.LogInformation("Cleared temp folder for permit {PermitId}", permitId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear temp folder for permit {PermitId}", permitId);
                throw;
            }
        }

        public async Task<Permit?> GetPermitByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Include(p => p.Job)
                // .Include(p => p.Documents)
                .Include(p => p.OtherPermitsInUse)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Permit>> GetAllPermitsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Permits
                .Include(p => p.Job)
                // .Include(p => p.Documents)
                // .Include(p => p.OtherPermitsInUse)
                .ToListAsync();
        }

        private async Task<PermitReturn> ProcessPermitReturnSignatures(PermitReturnInput input, Permit permit)
        {
            var processedCompetentPersons = new List<CompetentPerson>();
            var processedAuthorisedPersons = new List<AuthorisedPerson>();

            foreach (var cp in input.CompetentPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(cp.SignatureFileId), permit.JobId, permit.PermitType);
                processedCompetentPersons.Add(new CompetentPerson
                {
                    WorkerId = cp.WorkerId,
                    Name = cp.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = cp.SignedAt
                });
            }

            foreach (var ap in input.AuthorisedPersons)
            {
                var signatureFileId = await CopySignatureToTempAsync(int.Parse(ap.SignatureFileId), permit.JobId, permit.PermitType);
                processedAuthorisedPersons.Add(new AuthorisedPerson
                {
                    WorkerId = ap.WorkerId,
                    Name = ap.Name,
                    SignatureFileId = signatureFileId,
                    SignedAt = ap.SignedAt
                });
            }

            return new PermitReturn
            {
                CompetentPersons = processedCompetentPersons,
                AuthorisedPersons = processedAuthorisedPersons
            };
        }

        private void ValidateStateTransition(PermitStatus currentStatus, PermitStatus newStatus)
        {
            if (!_validStateTransitions.TryGetValue(currentStatus, out List<PermitStatus>? value) ||
                !value.Contains(newStatus))
            {
                throw new InvalidOperationException($"Invalid state transition from {currentStatus} to {newStatus}");
            }
        }
        private async Task<FileMetadata> ProcessAttendancePicture(Permit permit, int jobId, IFile attendancePictureFile)
        {
            using var attendancePictureStream = attendancePictureFile.OpenReadStream();
            var attendancePictureMetadata = await _minioService.UploadFileAsync(
                attendancePictureStream,
                attendancePictureFile.Name,
                Shared.Constants.FileStorageConstants.BucketNames.DOCS,
                attendancePictureFile.ContentType ?? "image/jpeg",
                "Attendance picture for General Work Permit",
                $"{nameof(permit.PermitType)}_job_{jobId}/attendance-picture",
                false,
                null);
            return attendancePictureMetadata;
        }
    }
}
