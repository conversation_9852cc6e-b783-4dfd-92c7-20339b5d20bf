using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Shared.DTOs;
using Shared.GraphQL.Models.Notifications;

namespace GraphQLApi.Services
{
    public interface INotificationService
    {
        Task PublishAsync(NotificationEvent evt, CancellationToken ct = default);
        Task PublishToRecipientsAsync(NotificationEvent evt, List<NotificationRecipient> recipients, CancellationToken ct = default);
        Task PublishToUserAsync(NotificationEvent evt, int userId, CancellationToken ct = default);
        Task PublishToRoleAsync(NotificationEvent evt, string roleName, int tenantId, CancellationToken ct = default);
        Task<List<Notification>> GetUserNotificationsAsync(int userId, int skip = 0, int take = 50, bool unreadOnly = false);
        Task<int> GetUnreadCountAsync(int userId);
        Task MarkAsReadAsync(int notificationId, int userId);
        Task MarkAllAsReadAsync(int userId);
        Task<NotificationPreference?> GetUserPreferencesAsync(int userId, string notificationType);
        Task UpdateUserPreferencesAsync(int userId, string notificationType, NotificationPreference preferences);
    }
}

