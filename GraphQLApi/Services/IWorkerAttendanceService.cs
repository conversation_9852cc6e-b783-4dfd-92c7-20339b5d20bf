using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IWorkerAttendanceService
    {
        Task<WorkerAttendance> RecordAttendanceAsync(int workerId, DateTime timestamp, string status, string? notes = null);
        Task<WorkerAttendance?> UpdateAttendanceAsync(int attendanceId, DateTime? checkOutTime, string? notes);
        Task<IEnumerable<WorkerAttendance>> GetWorkerAttendanceAsync(int workerId, DateTime from, DateTime to);
        Task<IEnumerable<WorkerAttendance>> GetDailyAttendanceAsync(DateTime date);
        Task<ToolboxSession> CreateToolboxSessionAsync(string topic, string conductor, Stream groupPhoto, DateTime sessionTime, int[] workerIds, string? notes = null);
        Task<IEnumerable<ToolboxSession>> GetToolboxSessionsAsync(DateTime from, DateTime to);
        Task<ToolboxSession?> GetToolboxSessionAsync(int sessionId);
        Task<bool> VerifyAttendanceWithHikvisionAsync(int attendanceId);
    }
} 