using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Configuration;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public class NotificationEventBus : BackgroundService
    {
        private readonly Channel<NotificationEvent> _channel;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<NotificationEventBus> _logger;
        private readonly NotificationOptions _options;

        public NotificationEventBus(
            IServiceScopeFactory serviceScopeFactory,
            IOptions<NotificationOptions> options,
            ILogger<NotificationEventBus> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
            _options = options.Value;
            // bounded channel to protect request path
            _channel = Channel.CreateBounded<NotificationEvent>(new BoundedChannelOptions(1000)
            {
                FullMode = BoundedChannelFullMode.DropOldest
            });
        }

        public bool TryEnqueue(NotificationEvent evt)
        {
            if (!_options.InAppEnabled && !_options.EmailEnabled && !_options.SmsEnabled)
            {
                return false;
            }
            return _channel.Writer.TryWrite(evt);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await foreach (var evt in _channel.Reader.ReadAllAsync(stoppingToken))
            {
                try
                {
                    // Create a scope to resolve scoped services
                    using var scope = _serviceScopeFactory.CreateScope();
                    var handlers = scope.ServiceProvider.GetServices<INotificationChannelHandler>();
                    var recipientResolutionService = scope.ServiceProvider.GetRequiredService<IRecipientResolutionService>();
                    var managementService = scope.ServiceProvider.GetRequiredService<NotificationManagementService>();

                    // Resolve recipients if specified
                    List<NotificationRecipient> recipients = new();
                    if (evt.Recipients?.Any() == true)
                    {
                        recipients = await recipientResolutionService.ResolveRecipientsAsync(evt.Recipients, GetTenantIdFromEvent(evt));
                    }

                    // Create persistent notifications if we have recipients
                    if (recipients.Any())
                    {
                        await managementService.CreateNotificationsAsync(evt, recipients, GetTenantIdFromEvent(evt), stoppingToken);
                    }

                    // Determine channels via options rules (simple: always InApp unless disabled)
                    var channels = ResolveChannels(evt);
                    foreach (var channel in channels)
                    {
                        foreach (var handler in handlers)
                        {
                            if (handler.CanHandle(channel))
                            {
                                try { await handler.HandleAsync(evt, stoppingToken); }
                                catch (Exception ex) { _logger.LogError(ex, "Notification handler error for {Type}", evt.Type); }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Notification dispatch failed for {Type}", evt.Type);
                }
            }
        }

        private IEnumerable<NotificationChannel> ResolveChannels(NotificationEvent evt)
        {
            // Very simple rules for now: map by Type via configured rules, else default InApp
            if (_options.NotificationRules is { Count: > 0 })
            {
                foreach (var rule in _options.NotificationRules)
                {
                    if (!string.IsNullOrWhiteSpace(rule.Type) && string.Equals(rule.Type, evt.Type, StringComparison.OrdinalIgnoreCase))
                    {
                        if (rule.Channels is { Count: > 0 })
                        {
                            foreach (var c in rule.Channels)
                            {
                                if (Enum.TryParse<NotificationChannel>(c, true, out var ch))
                                    yield return ch;
                            }
                            yield break;
                        }
                    }
                }
            }
            yield return NotificationChannel.InApp;
        }

        private int GetTenantIdFromEvent(NotificationEvent evt)
        {
            // Try to extract tenant ID from metadata
            if (evt.Metadata?.TryGetValue("tenantId", out var tenantIdStr) == true &&
                int.TryParse(tenantIdStr, out var tenantId))
            {
                return tenantId;
            }

            // Default to tenant 1 if not specified
            return 1;
        }
    }
}

