using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public class ToolboxService : IToolboxService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly IRiskAssessmentService _riskAssessmentService;
        private readonly ILogger<ToolboxService> _logger;

        public ToolboxService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            IRiskAssessmentService riskAssessmentService,
            ILogger<ToolboxService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _riskAssessmentService = riskAssessmentService;
            _logger = logger;
        }

        public async Task<IEnumerable<TodaysJobRiskAssessment>> GetTodaysJobRiskAssessmentAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            // var today = DateTime.Today;
            var approvedJobs = await context.Jobs
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Where(j => j.Status == JobStatus.APPROVED)
                .ToListAsync();

            var result = new List<TodaysJobRiskAssessment>();
            
            foreach (var job in approvedJobs)
            {
                
                result.Add(new TodaysJobRiskAssessment
                {
                    Id = job.Id,
                    Title = job.Title,
                    Hazards = job.Hazards.Select(h => new TodaysJobHazard
                    {
                        Id = h.Id,
                        Description = h.Description,
                        ControlMeasures = h.ControlMeasures.Select(cm => new TodaysJobControlMeasure
                        {
                            Id = cm.Id,
                            Description = cm.Description
                        })
                    })
                });
            }

            return result;
        }

        public async Task<Toolbox> CreateToolboxAsync(CreateToolboxInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate only one toolbox per day
            var today = DateTime.Today;
            // var existingToolbox = await context.Toolboxes
            //     .FirstOrDefaultAsync(t => t.Date.Date == today);

            // if (existingToolbox != null)
            //     throw new InvalidOperationException("Only one toolbox is allowed per day");

            // Get conductor information
            var conductor = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == input.ConductorId) ?? throw new ArgumentException($"Worker with ID {input.ConductorId} not found");

            // Get jobs

            var jobIds = input.Jobs.Select(j => j.JobId).ToList();
            var jobs = await context.Jobs
                .Where(j => jobIds.Contains(j.Id))
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .ToListAsync();

            if (jobs.Count != jobIds.Count)
                throw new ArgumentException("One or more jobs not found");

            // Process hazards and control measures for each job using RiskAssessmentService
            foreach (var jobInput in input.Jobs)
            {
                var existingHazards = jobInput.ExistingHazards.Select(eh => new ProcessExistingHazardInput
                {
                    Id = eh.Id,
                    Description = eh.Description,
                    ExistingControlMeasures = eh.ExistingControlMeasures.Select(ecm => new ProcessExistingControlMeasureInput
                    {
                        Id = ecm.Id,
                        Description = ecm.Description
                    }),
                    NewControlMeasures = eh.NewControlMeasures.Select(ncm => new ProcessNewControlMeasureInput
                    {
                        Description = ncm.Description
                    })
                });

                var newHazards = jobInput.NewHazards.Select(nh => new ProcessNewHazardInput
                {
                    Description = nh.Description,
                    ControlMeasures = nh.ControlMeasures.Select(cm => new ProcessNewControlMeasureInput
                    {
                        Description = cm.Description
                    })
                });

                await _riskAssessmentService.ProcessJobHazardsAsync(jobInput.JobId, existingHazards, newHazards);
            }

            // Refresh jobs to get updated hazards and control measures
            jobs = await context.Jobs
                .Where(j => jobIds.Contains(j.Id))
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .ToListAsync();

            var toolbox = new Toolbox
            {
                Date = today,
                Status = ToolboxStatus.FILLED,
                EmergencyProcedures = input.EmergencyProcedures,
                ToolboxTrainingTopics = input.ToolboxTrainingTopics,
                Jobs = jobs,
                Conductor = new ToolboxConductor
                {
                    WorkerId = conductor.Id,
                    Name = conductor.Name,
                    SignatureFileId = string.Empty
                }
            };

            context.Toolboxes.Add(toolbox);

            if (input.AttendeePictureFile != null)
            {
                using var attendeePictureStream = input.AttendeePictureFile.OpenReadStream();
                var attendeePictureMetadata = await _minioService.UploadFileAsync(
                    attendeePictureStream,
                    input.AttendeePictureFile.Name,
                    Shared.Constants.FileStorageConstants.BucketNames.DOCS,
                    input.AttendeePictureFile.ContentType ?? "image/jpeg",
                    "Attendee picture for Toolbox",
                    $"toolbox_{toolbox.Id}/attendee-picture",
                    false,
                    null);

                toolbox.AttendeePictureFile = attendeePictureMetadata;
            }
            
            await context.SaveChangesAsync();

            // Copy signature file to temp bucket
            string conductorSignatureFileId = string.Empty;
            if (conductor.SignatureFileId.HasValue)
            {
                conductorSignatureFileId = await CopySignatureToTempAsync(conductor.SignatureFileId.Value, today, toolbox.Id);
            }
            toolbox.Conductor = new ToolboxConductor
            {
                WorkerId = conductor.Id,
                Name = conductor.Name,
                SignatureFileId = conductorSignatureFileId
            };

            
            await context.SaveChangesAsync();
            return toolbox;
        }

        public async Task AddAttendeesAsync(int toolboxId, IEnumerable<int> workerIds)//IEnumerable<ToolboxAttendeeInput> attendees)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var toolbox = await context.Toolboxes
                .FirstOrDefaultAsync(t => t.Id == toolboxId) ?? throw new ArgumentException($"Toolbox with ID {toolboxId} not found");

            // Validate state transition

            if (toolbox.Status != ToolboxStatus.FILLED)
                throw new InvalidOperationException($"Cannot add attendees to toolbox with status {toolbox.Status}");

            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();
            
            if (workers.Count != workerIds.Count())
                throw new ArgumentException("One or more workers not found");
            
            var attendees = workers.Select(w => new ToolboxAttendeeInput
            {
                WorkerId = w.Id,
                Name = w.Name,
                Designation = w.Trades.FirstOrDefault()?.Name ?? "CASUAL",
                SignatureFileId = $"{w.SignatureFileId}"
            });

            var attendeeList = new List<ToolboxAttendee>();
            
            // Process attendees and copy signature files
            foreach (var attendeeInput in attendees)
            {
                var worker = await context.Workers
                    .FirstOrDefaultAsync(w => w.Id == attendeeInput.WorkerId);
                
                if (worker == null)
                    throw new ArgumentException($"Worker with ID {attendeeInput.WorkerId} not found");

                string signatureFileId = string.Empty;
                if (worker.SignatureFileId.HasValue)
                {
                    signatureFileId = await CopySignatureToTempAsync(worker.SignatureFileId.Value, toolbox.Date, toolbox.Id);
                }

                attendeeList.Add(new ToolboxAttendee
                {
                    WorkerId = attendeeInput.WorkerId,
                    Name = attendeeInput.Name,
                    Designation = attendeeInput.Designation,
                    SignatureFileId = signatureFileId
                });
            }

            toolbox.Attendees = attendeeList;
            toolbox.Status = ToolboxStatus.PENDING_CLOSURE;

            await context.SaveChangesAsync();
        }

        private async Task<string> CopySignatureToTempAsync(int sourceFileId, DateTime toolboxDate, int toolboxId)
        {
            try
            {
                // Get source file metadata
                await using var context = await _contextFactory.CreateDbContextAsync();
                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId);

                if (sourceFile == null)
                    return string.Empty;

                // Create temp folder path for the toolbox
                var tempFolderPath = $"toolbox_{toolboxId}_{toolboxDate:yyyyMMdd}/signatures";

                // Download the source file from MinIO
                using var sourceStream = await _minioService.DownloadFileAsync(sourceFile);

                // Generate unique filename for temp copy
                var tempFileName = $"{System.IO.Path.GetFileNameWithoutExtension(sourceFile.FileName)}_{Guid.NewGuid()}{System.IO.Path.GetExtension(sourceFile.FileName)}";

                // Upload to temp bucket with folder path
                var tempFileMetadata = await _minioService.UploadFileAsync(
                    sourceStream,
                    tempFileName,
                    Shared.Constants.FileStorageConstants.BucketNames.TEMP,
                    sourceFile.ContentType,
                    $"Temporary copy of signature for toolbox {toolboxDate:yyyyMMdd}",
                    tempFolderPath,
                    false, // Not public
                    DateTime.UtcNow.AddDays(30) // Expire in 30 days
                );

                _logger.LogInformation("Successfully copied signature file {SourceFileId} to temp bucket as {TempFileId}",
                    sourceFileId, tempFileMetadata.Id);

                return tempFileMetadata.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to temp bucket", sourceFileId);
                throw new InvalidOperationException("Failed to copy signature file", ex);
            }
        }

        public async Task SummarizeToolboxAsync(int toolboxId, IEnumerable<CreateToolboxJobInput> inputJobs)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var toolbox = await context.Toolboxes
                .Include(t => t.Jobs)
                    .ThenInclude(j => j.Hazards)
                        .ThenInclude(h => h.ControlMeasures)
                .FirstOrDefaultAsync(t => t.Id == toolboxId) ?? throw new ArgumentException($"Toolbox with ID {toolboxId} not found");

            // Validate state transition

            if (toolbox.Status != ToolboxStatus.PENDING_CLOSURE)
                throw new InvalidOperationException($"Cannot summarize toolbox with status {toolbox.Status}");

            // Get jobs

            var jobIds = inputJobs.Select(j => j.JobId).ToList();

            var jobs = toolbox.Jobs
                .Where(j => jobIds.Contains(j.Id))
                .ToList();

            // Process hazards and control measures for each job using RiskAssessmentService
            foreach (var jobInput in inputJobs)
            {
                var existingHazards = jobInput.ExistingHazards.Select(eh => new ProcessExistingHazardInput
                {
                    Id = eh.Id,
                    Description = eh.Description,
                    ExistingControlMeasures = eh.ExistingControlMeasures.Select(ecm => new ProcessExistingControlMeasureInput
                    {
                        Id = ecm.Id,
                        Description = ecm.Description
                    }),
                    NewControlMeasures = eh.NewControlMeasures.Select(ncm => new ProcessNewControlMeasureInput
                    {
                        Description = ncm.Description
                    })
                });

                var newHazards = jobInput.NewHazards.Select(nh => new ProcessNewHazardInput
                {
                    Description = nh.Description,
                    ControlMeasures = nh.ControlMeasures.Select(cm => new ProcessNewControlMeasureInput
                    {
                        Description = cm.Description
                    })
                });

                await _riskAssessmentService.ProcessJobHazardsAsync(jobInput.JobId, existingHazards, newHazards);
            }

            // foreach (var jobInput in jobs)
            // {
            //     var job = toolbox.Jobs.FirstOrDefault(j => j.Id == jobInput.Id);
            //     if (job != null)
            //     {
            //         // Add new hazards to job
            //         var newHazard = new Hazard
            //         {
            //             Description = jobInput.Hazards.Description,
            //             JobId = job.Id
            //         };

            //         // Add control measures to the hazard
            //         foreach (var cmInput in jobInput.Hazards.ControlMeasures)
            //         {
            //             newHazard.ControlMeasures.Add(new ControlMeasure
            //             {
            //                 Description = cmInput.Description,
            //                 Closed = true // Mark as closed as per requirements
            //             });
            //         }

            //         context.Hazards.Add(newHazard);
            //     }
            // }

            await context.SaveChangesAsync();
        }

        public async Task AddHazardAsync(AddHazardInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var job = await context.Jobs
                .FirstOrDefaultAsync(j => j.Id == input.JobId);
            
            if (job == null)
                throw new ArgumentException($"Job with ID {input.JobId} not found");

            var hazard = new Hazard
            {
                Description = input.Description,
                JobId = input.JobId
            };

            // Create associated control measures
            foreach (var cmDescription in input.ControlMeasures)
            {
                hazard.ControlMeasures.Add(new ControlMeasure
                {
                    Description = cmDescription
                });
            }

            context.Hazards.Add(hazard);
            await context.SaveChangesAsync();
        }

        public async Task AddControlMeasureAsync(AddControlMeasureInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var hazard = await context.Hazards
                .FirstOrDefaultAsync(h => h.Id == input.HazardId);
            
            if (hazard == null)
                throw new ArgumentException($"Hazard with ID {input.HazardId} not found");

            var controlMeasure = new ControlMeasure
            {
                Description = input.Description,
                HazardId = input.HazardId
            };

            context.ControlMeasures.Add(controlMeasure);
            await context.SaveChangesAsync();
        }

        public async Task ClearPermitTempFolderAsync(int permitId)
        {
            try
            {
                // This would implement the logic to delete permit temp folder
                // Only for permits with no other state change allowed (e.g., closed)
                await using var context = await _contextFactory.CreateDbContextAsync();
                
                // Check if permit exists and is in a final state
                // Implementation would depend on permit structure
                
                _logger.LogInformation("Cleared temp folder for permit {PermitId}", permitId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear temp folder for permit {PermitId}", permitId);
                throw;
            }
        }

        public async Task<Toolbox?> GetToolboxByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<IEnumerable<Toolbox>> GetAllToolboxesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .ToListAsync();
        }

        public async Task<IEnumerable<Toolbox>> GetToolboxesByDateAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Toolboxes
                .Include(t => t.Jobs)
                .Where(t => t.Date.Date == date.Date)
                .ToListAsync();
        }

        public async Task<Toolbox?> GetTodaysToolboxAsync()
        {
            var today = DateTime.Today;
            var toolboxes = await GetToolboxesByDateAsync(today);
            return toolboxes.FirstOrDefault();
        }
    }
}
