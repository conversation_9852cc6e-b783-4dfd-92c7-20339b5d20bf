using GraphQLApi.Data;
using GraphQLApi.GraphQL.Types;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IWorkerService
    {
        Task<IEnumerable<Worker>> GetAllWorkersAsync();
        Task<Worker?> GetWorkerByIdAsync(int id);
        Task<Worker> CreateWorkerAsync(Worker worker);
        Task<Worker> CreateCompleteWorkerAsync(CreateWorkerWithTrainingInput input);
        Task<Worker?> UpdateWorkerAsync(int id, Worker worker);
        Task<bool> DeleteWorkerAsync(int id);
        //Task<string> UploadWorkerPhotoAsync(int workerId, Stream photoStream);
    }
    public class CompleteWorkerInput
    {
        public required string Name { get; set; }
        public required string Company { get; set; }
        public required string NationalId { get; set; }
        public required string Gender { get; set; }
        public required string PhoneNumber { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<WorkerTrainingInput>? Trainings { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public required IFile ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<DocumentFileInput>? Documents { get; set; }
    }
} 