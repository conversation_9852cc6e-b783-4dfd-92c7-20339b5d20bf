using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IRiskAssessmentService
    {
        // Hazard CRUD operations for a specific job
        Task<Hazard> CreateHazardAsync(int jobId, string description);
        Task<Hazard?> GetHazardByIdAsync(int hazardId);
        Task<IEnumerable<Hazard>> GetHazardsByJobIdAsync(int jobId);
        Task<Hazard?> UpdateHazardAsync(int hazardId, string description);
        Task<bool> DeleteHazardAsync(int hazardId);

        // Control Measure CRUD operations for a specific hazard
        Task<ControlMeasure> CreateControlMeasureAsync(int hazardId, string description, bool closed = false);
        Task<ControlMeasure?> GetControlMeasureByIdAsync(int controlMeasureId);
        Task<IEnumerable<ControlMeasure>> GetControlMeasuresByHazardIdAsync(int hazardId);
        Task<ControlMeasure?> UpdateControlMeasureAsync(int controlMeasureId, string description, bool? closed = null);
        Task<bool> DeleteControlMeasureAsync(int controlMeasureId);

        // Batch operations for toolbox creation
        Task ProcessJobHazardsAsync(int jobId, IEnumerable<ProcessExistingHazardInput> existingHazards, IEnumerable<ProcessNewHazardInput> newHazards);
    }

    // Input classes for batch operations
    public class ProcessExistingHazardInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public IEnumerable<ProcessExistingControlMeasureInput> ExistingControlMeasures { get; set; } = [];
        public IEnumerable<ProcessNewControlMeasureInput> NewControlMeasures { get; set; } = [];
    }

    public class ProcessNewHazardInput
    {
        public string Description { get; set; } = string.Empty;
        public IEnumerable<ProcessNewControlMeasureInput> ControlMeasures { get; set; } = [];
    }

    public class ProcessExistingControlMeasureInput
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class ProcessNewControlMeasureInput
    {
        public string Description { get; set; } = string.Empty;
    }
}
