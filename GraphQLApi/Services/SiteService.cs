using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.DTOs;
using System.Text.Json;

namespace GraphQLApi.Services
{
    public class SiteService : ISiteService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ILogger<SiteService> _logger;

        public SiteService(IDbContextFactory<AppDbContext> contextFactory, ILogger<SiteService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        public async Task<IEnumerable<Site>> GetAllSitesAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites.OrderBy(s => s.Name).ToListAsync();
        }

        public async Task<Site?> GetSiteByIdAsync(Guid id)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites.FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Site> CreateSiteAsync(string name, SiteDataDto? siteData = null)
        {
            using var context = _contextFactory.CreateDbContext();
            var site = new Site
            {
                Id = Guid.NewGuid(),
                Name = name,
                SchemaVersion = "1.0",
                CreatedBy = "System", // TODO: Get from current user context
                Status = "planning",
                HealthStatus = "green"
            };

            if (siteData != null)
            {
                site.SetSiteData(siteData);
            }

            context.Sites.Add(site);
            await context.SaveChangesAsync();

            _logger.LogInformation("Created new site with ID: {SiteId}", site.Id);
            return site;
        }

        public async Task<Site?> UpdateSiteAsync(Guid id, string? name = null, SiteDataDto? siteData = null)
        {
            using var context = _contextFactory.CreateDbContext();
            var site = await context.Sites.FirstOrDefaultAsync(s => s.Id == id);
            if (site == null) return null;

            if (!string.IsNullOrEmpty(name))
                site.Name = name;

            if (siteData != null)
            {
                site.SetSiteData(siteData);
            }

            site.UpdatedBy = "System"; // TODO: Get from current user context
            await context.SaveChangesAsync();

            _logger.LogInformation("Updated site with ID: {SiteId}", site.Id);
            return site;
        }

        public async Task<bool> DeleteSiteAsync(Guid id)
        {
            using var context = _contextFactory.CreateDbContext();
            var site = await context.Sites.FirstOrDefaultAsync(s => s.Id == id);
            if (site == null) return false;

            context.Sites.Remove(site);
            await context.SaveChangesAsync();

            _logger.LogInformation("Deleted site with ID: {SiteId}", site.Id);
            return true;
        }

        public async Task<SiteDataDto?> GetSiteDataAsync(Guid id)
        {
            var site = await GetSiteByIdAsync(id);
            return site?.GetSiteData<SiteDataDto>();
        }

        public async Task<Site?> UpdateSiteDataAsync(Guid id, SiteDataDto siteData)
        {
            using var context = _contextFactory.CreateDbContext();
            var site = await context.Sites.FirstOrDefaultAsync(s => s.Id == id);
            if (site == null) return null;

            site.SetSiteData(siteData);
            site.UpdatedBy = "System"; // TODO: Get from current user context
            await context.SaveChangesAsync();

            _logger.LogInformation("Updated site data for site ID: {SiteId}", site.Id);
            return site;
        }

        public async Task<Site?> PatchSiteDataAsync(Guid id, string jsonPatch)
        {
            using var context = _contextFactory.CreateDbContext();
            var site = await context.Sites.FirstOrDefaultAsync(s => s.Id == id);
            if (site == null) return null;

            try
            {
                // Simple JSON merge - for more complex patching, consider using JSON Patch library
                var existingData = site.GetSiteDataAsDocument();
                var patchData = JsonDocument.Parse(jsonPatch);

                // Merge logic would go here - for now, just replace
                site.SiteDataJson = jsonPatch;
                site.UpdateCachedFields();

                site.UpdatedBy = "System"; // TODO: Get from current user context
                await context.SaveChangesAsync();

                _logger.LogInformation("Patched site data for site ID: {SiteId}", site.Id);
                return site;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to patch site data for site ID: {SiteId}", site.Id);
                return null;
            }
        }

        public async Task<IEnumerable<Site>> GetSitesByStatusAsync(string status)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites
                .Where(s => s.Status == status)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Site>> GetSitesByProjectManagerAsync(string projectManager)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites
                .Where(s => s.ProjectManager == projectManager)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Site>> GetSitesByProjectTypeAsync(string projectType)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites
                .Where(s => s.ProjectType == projectType)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Site>> SearchSitesAsync(string searchTerm)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites
                .Where(s =>
                    s.Name.Contains(searchTerm) ||
                    (s.Location != null && s.Location.Contains(searchTerm)) ||
                    (s.ProjectManager != null && s.ProjectManager.Contains(searchTerm)))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<bool> SiteExistsAsync(Guid id)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Sites.AnyAsync(s => s.Id == id);
        }

        public async Task<bool> ValidateSiteDataAsync(SiteDataDto siteData)
        {
            // Add validation logic here
            // For now, just check if it's not null
            return await System.Threading.Tasks.Task.FromResult(siteData != null);
        }

        public async Task<Site?> CloneSiteAsync(Guid sourceId, string newName)
        {
            var sourceSite = await GetSiteByIdAsync(sourceId);
            if (sourceSite == null) return null;

            var siteData = sourceSite.GetSiteData<SiteDataDto>();
            var clonedSite = await CreateSiteAsync(newName, siteData);

            _logger.LogInformation("Cloned site {SourceId} to new site {ClonedId}", sourceId, clonedSite.Id);
            return clonedSite;
        }
    }
}
