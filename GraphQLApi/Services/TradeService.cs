using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services;

public class TradeService : ITradeService
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory;
    private readonly ILogger<TradeService> _logger;

    public TradeService(
        IDbContextFactory<AppDbContext> contextFactory,
        ILogger<TradeService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<Trade>> GetAllTradesAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trades
            .Include(t => t.Workers)
            .ToListAsync();
    }

    public async Task<Trade?> GetTradeByIdAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trades
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<Trade> CreateTradeAsync(Trade trade)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        // Check if trade with same name already exists (case-insensitive, culture-invariant)
        var existingTrade = await context.Trades
            .FirstOrDefaultAsync(t => EF.Functions.Like(t.Name, trade.Name));
        
        if (existingTrade != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A trade with name '{trade.Name}' already exists.")
            );
        }

        context.Trades.Add(trade);
        await context.SaveChangesAsync();
        return trade;
    }

    public async Task<Trade?> UpdateTradeAsync(int id, Trade trade, string updatedBy)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingTrade = await context.Trades
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (existingTrade == null)
        {
            return null;
        }

        // Check if another trade with the same name exists (case-insensitive, culture-invariant)
        var duplicateTrade = await context.Trades
            .FirstOrDefaultAsync(t => t.Id != id && EF.Functions.Like(t.Name, trade.Name));

        if (duplicateTrade != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A trade with name '{trade.Name}' already exists.")
            );
        }

        // Update properties
        existingTrade.Name = trade.Name;
        existingTrade.Description = trade.Description;

        // Set audit fields
        existingTrade.UpdatedAt = DateTime.UtcNow;
        existingTrade.UpdatedBy = updatedBy;

        await context.SaveChangesAsync();
        return existingTrade;
    }

    public async Task<bool> DeleteTradeAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var trade = await context.Trades.FindAsync(id);
        
        if (trade == null)
        {
            return false;
        }

        try
        {
            context.Trades.Remove(trade);
            await context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting trade with ID {TradeId}", id);
            return false;
        }
    }
}
