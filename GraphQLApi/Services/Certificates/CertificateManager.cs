using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Certificates;
using Shared.Enums;
using System.Text.Json;

namespace GraphQLApi.Services.Certificates
{
    /// <summary>
    /// Generic Certificate Manager implementation
    /// Handles all certificate types via composition pattern
    /// </summary>
    public class CertificateManager : ICertificateManager
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly ILogger<CertificateManager> _logger;

        public CertificateManager(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            ILogger<CertificateManager> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _logger = logger;
        }

        public async Task<Certificate> IssueAsync(CertificateRequest request)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate worker exists
            var worker = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == request.WorkerId && !w.IsDeleted);
            if (worker == null)
                throw new ArgumentException($"Worker with ID {request.WorkerId} not found");

            // Check for duplicate certificate number
            var existingCert = await context.Certificates
                .FirstOrDefaultAsync(c => c.CertificateNo == request.CertificateNo && !c.IsDeleted);
            if (existingCert != null)
                throw new InvalidOperationException($"Certificate number {request.CertificateNo} already exists");

            var certificate = new Certificate
            {
                WorkerId = request.WorkerId,
                CertificateNo = request.CertificateNo,
                CertificateType = request.CertificateType,
                Source = request.Source,
                SourceId = request.SourceId,
                IssueDate = request.IssueDate,
                ExpiryDate = request.ExpiryDate,
                Status = CertificateStatus.ISSUED,
                IssuedBy = request.IssuedBy,
                Metadata = request.Metadata != null ? JsonSerializer.Serialize(request.Metadata) : null,
                Notes = request.Notes
            };

            // Handle file upload if provided
            if (request.CertificateFile != null)
            {
                var fileMetadata = await UploadCertificateFileAsync(request.CertificateFile, certificate);
                certificate.CertificateFile = fileMetadata;
            }

            context.Certificates.Add(certificate);
            await context.SaveChangesAsync();

            _logger.LogInformation("Certificate {CertificateNo} issued for worker {WorkerId} of type {CertificateType}", 
                certificate.CertificateNo, certificate.WorkerId, certificate.CertificateType);

            return certificate;
        }

        public async Task<Certificate> RenewAsync(int certificateId, RenewCertificateRequest request)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificate = await context.Certificates
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
            if (certificate == null)
                throw new ArgumentException($"Certificate with ID {certificateId} not found");

            // Update certificate details
            certificate.ExpiryDate = request.NewExpiryDate;
            certificate.Status = CertificateStatus.VALID;
            certificate.UpdatedAt = DateTime.UtcNow;
            certificate.UpdatedBy = request.RenewedBy;

            if (!string.IsNullOrEmpty(request.NewCertificateNo))
            {
                // Check for duplicate new certificate number
                var existingCert = await context.Certificates
                    .FirstOrDefaultAsync(c => c.CertificateNo == request.NewCertificateNo && c.Id != certificateId && !c.IsDeleted);
                if (existingCert != null)
                    throw new InvalidOperationException($"Certificate number {request.NewCertificateNo} already exists");

                certificate.CertificateNo = request.NewCertificateNo;
            }

            // Update metadata
            if (request.AdditionalMetadata != null)
            {
                var existingMetadata = !string.IsNullOrEmpty(certificate.Metadata) 
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(certificate.Metadata) ?? new()
                    : new Dictionary<string, object>();

                foreach (var kvp in request.AdditionalMetadata)
                {
                    existingMetadata[kvp.Key] = kvp.Value;
                }
                
                // Add renewal information
                existingMetadata["RenewedAt"] = DateTime.UtcNow;
                existingMetadata["RenewedBy"] = request.RenewedBy;
                if (!string.IsNullOrEmpty(request.RenewalSource))
                    existingMetadata["RenewalSource"] = request.RenewalSource;
                if (!string.IsNullOrEmpty(request.RenewalSourceId))
                    existingMetadata["RenewalSourceId"] = request.RenewalSourceId;

                certificate.Metadata = JsonSerializer.Serialize(existingMetadata);
            }

            if (!string.IsNullOrEmpty(request.Notes))
            {
                certificate.Notes = string.IsNullOrEmpty(certificate.Notes) 
                    ? request.Notes 
                    : $"{certificate.Notes}\n\nRenewal: {request.Notes}";
            }

            // Handle new certificate file
            if (request.NewCertificateFile != null)
            {
                var fileMetadata = await UploadCertificateFileAsync(request.NewCertificateFile, certificate);
                certificate.CertificateFile = fileMetadata;
            }

            await context.SaveChangesAsync();

            _logger.LogInformation("Certificate {CertificateNo} renewed for worker {WorkerId}", 
                certificate.CertificateNo, certificate.WorkerId);

            return certificate;
        }

        public async Task<Certificate> RevokeAsync(int certificateId, string reason, string revokedBy)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificate = await context.Certificates
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
            if (certificate == null)
                throw new ArgumentException($"Certificate with ID {certificateId} not found");

            certificate.Status = CertificateStatus.REVOKED;
            certificate.UpdatedAt = DateTime.UtcNow;
            certificate.UpdatedBy = revokedBy;

            // Update metadata with revocation info
            var metadata = !string.IsNullOrEmpty(certificate.Metadata) 
                ? JsonSerializer.Deserialize<Dictionary<string, object>>(certificate.Metadata) ?? new()
                : new Dictionary<string, object>();

            metadata["RevokedAt"] = DateTime.UtcNow;
            metadata["RevokedBy"] = revokedBy;
            metadata["RevocationReason"] = reason;

            certificate.Metadata = JsonSerializer.Serialize(metadata);
            certificate.Notes = string.IsNullOrEmpty(certificate.Notes) 
                ? $"Revoked: {reason}" 
                : $"{certificate.Notes}\n\nRevoked: {reason}";

            await context.SaveChangesAsync();

            _logger.LogInformation("Certificate {CertificateNo} revoked for worker {WorkerId}. Reason: {Reason}", 
                certificate.CertificateNo, certificate.WorkerId, reason);

            return certificate;
        }

        public async Task<Certificate> UpdateStatusAsync(int certificateId, CertificateStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificate = await context.Certificates
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
            if (certificate == null)
                throw new ArgumentException($"Certificate with ID {certificateId} not found");

            certificate.Status = status;
            certificate.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            return certificate;
        }

        public async Task<Certificate?> GetByIdAsync(int certificateId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Certificates
                .Include(c => c.Worker)
                .Include(c => c.CertificateFile)
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
        }

        public async Task<Certificate?> GetByCertificateNoAsync(string certificateNo)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Certificates
                .Include(c => c.Worker)
                .Include(c => c.CertificateFile)
                .FirstOrDefaultAsync(c => c.CertificateNo == certificateNo && !c.IsDeleted);
        }

        public async Task<IEnumerable<Certificate>> GetWorkerCertificatesAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Certificates
                .Include(c => c.CertificateFile)
                .Where(c => c.WorkerId == workerId && !c.IsDeleted)
                .OrderByDescending(c => c.IssueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Certificate>> GetCertificatesByTypeAsync(string certificateType)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Certificates
                .Include(c => c.Worker)
                .Include(c => c.CertificateFile)
                .Where(c => c.CertificateType == certificateType && !c.IsDeleted)
                .OrderByDescending(c => c.IssueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Certificate>> GetExpiringCertificatesAsync(int daysAhead = 30)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var expiryThreshold = DateTime.UtcNow.AddDays(daysAhead);

            return await context.Certificates
                .Include(c => c.Worker)
                .Include(c => c.CertificateFile)
                .Where(c => c.ExpiryDate <= expiryThreshold && 
                           (c.Status == CertificateStatus.VALID || c.Status == CertificateStatus.ISSUED) && 
                           !c.IsDeleted)
                .OrderBy(c => c.ExpiryDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Certificate>> GetCertificatesBySourceAsync(string source, string sourceId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Certificates
                .Include(c => c.Worker)
                .Include(c => c.CertificateFile)
                .Where(c => c.Source == source && c.SourceId == sourceId && !c.IsDeleted)
                .OrderByDescending(c => c.IssueDate)
                .ToListAsync();
        }

        public async Task<CertificateValidationResult> ValidateAsync(int certificateId)
        {
            var certificate = await GetByIdAsync(certificateId);
            if (certificate == null)
            {
                return new CertificateValidationResult
                {
                    IsValid = false,
                    ValidationMessages = new List<string> { "Certificate not found" }
                };
            }

            return ValidateCertificate(certificate);
        }

        public async Task<CertificateValidationResult> ValidateByNumberAsync(string certificateNo)
        {
            var certificate = await GetByCertificateNoAsync(certificateNo);
            if (certificate == null)
            {
                return new CertificateValidationResult
                {
                    IsValid = false,
                    ValidationMessages = new List<string> { "Certificate not found" }
                };
            }

            return ValidateCertificate(certificate);
        }

        public async Task<Certificate> AttachFileAsync(int certificateId, IFile certificateFile)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificate = await context.Certificates
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
            if (certificate == null)
                throw new ArgumentException($"Certificate with ID {certificateId} not found");

            var fileMetadata = await UploadCertificateFileAsync(certificateFile, certificate);
            certificate.CertificateFile = fileMetadata;
            certificate.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            return certificate;
        }

        public async Task<Certificate> RemoveFileAsync(int certificateId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificate = await context.Certificates
                .Include(c => c.CertificateFile)
                .FirstOrDefaultAsync(c => c.Id == certificateId && !c.IsDeleted);
            if (certificate == null)
                throw new ArgumentException($"Certificate with ID {certificateId} not found");

            certificate.CertificateFileId = null;
            certificate.CertificateFile = null;
            certificate.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            return certificate;
        }

        public async Task<int> ProcessExpiredCertificatesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var expiredCertificates = await context.Certificates
                .Where(c => c.ExpiryDate < DateTime.UtcNow && 
                           c.Status != CertificateStatus.EXPIRED && 
                           c.Status != CertificateStatus.REVOKED && 
                           !c.IsDeleted)
                .ToListAsync();

            foreach (var certificate in expiredCertificates)
            {
                certificate.Status = CertificateStatus.EXPIRED;
                certificate.UpdatedAt = DateTime.UtcNow;
                certificate.UpdatedBy = "SYSTEM";
            }

            await context.SaveChangesAsync();

            _logger.LogInformation("Processed {Count} expired certificates", expiredCertificates.Count);

            return expiredCertificates.Count;
        }

        public async Task<IEnumerable<Certificate>> BulkIssueAsync(IEnumerable<CertificateRequest> requests)
        {
            var certificates = new List<Certificate>();

            foreach (var request in requests)
            {
                try
                {
                    var certificate = await IssueAsync(request);
                    certificates.Add(certificate);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to issue certificate {CertificateNo} for worker {WorkerId}", 
                        request.CertificateNo, request.WorkerId);
                    // Continue with other certificates
                }
            }

            return certificates;
        }

        private CertificateValidationResult ValidateCertificate(Certificate certificate)
        {
            var result = new CertificateValidationResult
            {
                Certificate = certificate,
                Status = certificate.Status
            };

            var messages = new List<string>();

            // Check status
            if (certificate.Status == CertificateStatus.REVOKED)
            {
                result.IsValid = false;
                messages.Add("Certificate has been revoked");
            }
            else if (certificate.Status == CertificateStatus.EXPIRED)
            {
                result.IsValid = false;
                messages.Add("Certificate has expired");
            }
            else if (certificate.ExpiryDate < DateTime.UtcNow)
            {
                result.IsValid = false;
                messages.Add("Certificate has expired");
                result.Status = CertificateStatus.EXPIRED;
            }
            else
            {
                result.IsValid = true;
                result.ExpiryDate = certificate.ExpiryDate;
                result.DaysUntilExpiry = (certificate.ExpiryDate - DateTime.UtcNow).Days;

                if (result.DaysUntilExpiry <= 30)
                {
                    messages.Add($"Certificate expires in {result.DaysUntilExpiry} days");
                }
            }

            result.ValidationMessages = messages;
            return result;
        }

        private async Task<Shared.GraphQL.Models.FileMetadata> UploadCertificateFileAsync(IFile file, Certificate certificate)
        {
            using var fileStream = file.OpenReadStream();
            var folderPath = $"certificates/{certificate.WorkerId}/{certificate.CertificateType}";
            var fileName = $"{certificate.CertificateNo}_{Guid.NewGuid()}{System.IO.Path.GetExtension(file.Name)}";

            return await _minioService.UploadFileAsync(
                fileStream,
                fileName,
                Shared.Constants.FileStorageConstants.BucketNames.DOCS,
                file.ContentType ?? "application/pdf",
                $"Certificate file for {certificate.CertificateNo}",
                folderPath,
                false,
                null);
        }
    }
}

