using Shared.GraphQL.Models.Certificates;
using Shared.Enums;

namespace GraphQLApi.Services.Certificates
{
    /// <summary>
    /// Generic Certificate Manager - handles all types of certificates via composition
    /// Not tied to training system specifically
    /// </summary>
    public interface ICertificateManager
    {
        // Certificate lifecycle management
        Task<Certificate> IssueAsync(CertificateRequest request);
        Task<Certificate> RenewAsync(int certificateId, RenewCertificateRequest request);
        Task<Certificate> RevokeAsync(int certificateId, string reason, string revokedBy);
        Task<Certificate> UpdateStatusAsync(int certificateId, CertificateStatus status);

        // Certificate queries
        Task<Certificate?> GetByIdAsync(int certificateId);
        Task<Certificate?> GetByCertificateNoAsync(string certificateNo);
        Task<IEnumerable<Certificate>> GetWorkerCertificatesAsync(int workerId);
        Task<IEnumerable<Certificate>> GetCertificatesByTypeAsync(string certificateType);
        Task<IEnumerable<Certificate>> GetExpiringCertificatesAsync(int daysAhead = 30);
        Task<IEnumerable<Certificate>> GetCertificatesBySourceAsync(string source, string sourceId);

        // Certificate validation
        Task<CertificateValidationResult> ValidateAsync(int certificateId);
        Task<CertificateValidationResult> ValidateByNumberAsync(string certificateNo);

        // File management
        Task<Certificate> AttachFileAsync(int certificateId, IFile certificateFile);
        Task<Certificate> RemoveFileAsync(int certificateId);

        // Bulk operations
        Task<int> ProcessExpiredCertificatesAsync();
        Task<IEnumerable<Certificate>> BulkIssueAsync(IEnumerable<CertificateRequest> requests);
    }

    /// <summary>
    /// Request to issue a new certificate
    /// </summary>
    public class CertificateRequest
    {
        public int WorkerId { get; set; }
        public string CertificateType { get; set; } = string.Empty; // "TrainingCertificate", "MedicalCertificate", etc.
        public string Source { get; set; } = string.Empty; // "TrainingSession", "ExternalProvider", "Manual"
        public string SourceId { get; set; } = string.Empty; // ID of the source
        public string CertificateNo { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string IssuedBy { get; set; } = string.Empty;
        public Dictionary<string, object>? Metadata { get; set; } // Flexible metadata
        public string? Notes { get; set; }
        public IFile? CertificateFile { get; set; }
    }

    /// <summary>
    /// Request to renew an existing certificate
    /// </summary>
    public class RenewCertificateRequest
    {
        public DateTime NewExpiryDate { get; set; }
        public string? NewCertificateNo { get; set; } // Optional new certificate number
        public string RenewedBy { get; set; } = string.Empty;
        public string? RenewalSource { get; set; } // Source of renewal
        public string? RenewalSourceId { get; set; }
        public Dictionary<string, object>? AdditionalMetadata { get; set; }
        public string? Notes { get; set; }
        public IFile? NewCertificateFile { get; set; }
    }

    /// <summary>
    /// Result of certificate validation
    /// </summary>
    public class CertificateValidationResult
    {
        public bool IsValid { get; set; }
        public CertificateStatus Status { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public int? DaysUntilExpiry { get; set; }
        public List<string> ValidationMessages { get; set; } = new();
        public Certificate? Certificate { get; set; }
    }
}





