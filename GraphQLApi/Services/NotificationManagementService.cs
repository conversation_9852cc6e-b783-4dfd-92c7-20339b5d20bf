using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using GraphQLApi.Data;
using Shared.DTOs;
using Shared.GraphQL.Models.Notifications;

namespace GraphQLApi.Services
{
    public class NotificationManagementService
    {
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly IRecipientResolutionService _recipientResolutionService;
        private readonly ILogger<NotificationManagementService> _logger;

        public NotificationManagementService(
            IDbContextFactory<AppDbContext> dbContextFactory,
            IRecipientResolutionService recipientResolutionService,
            ILogger<NotificationManagementService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _recipientResolutionService = recipientResolutionService;
            _logger = logger;
        }

        public async Task<List<Notification>> CreateNotificationsAsync(
            NotificationEvent evt, 
            List<NotificationRecipient> recipients, 
            int tenantId,
            CancellationToken ct = default)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync(ct);
            var notifications = new List<Notification>();

            foreach (var recipient in recipients)
            {
                // Check user preferences
                var preferences = await GetUserPreferencesAsync(recipient.UserId, evt.Type);
                if (preferences != null && !ShouldSendNotification(evt, preferences))
                {
                    _logger.LogDebug("Skipping notification for user {UserId} due to preferences", recipient.UserId);
                    continue;
                }

                var notification = new Notification
                {
                    Type = evt.Type,
                    Title = evt.Title,
                    Message = evt.Message,
                    Priority = evt.Priority,
                    Status = NotificationStatus.Pending,
                    Entity = evt.Entity,
                    Operation = evt.Operation,
                    Metadata = evt.Metadata != null ? JsonSerializer.Serialize(evt.Metadata) : null,
                    UserId = recipient.UserId,
                    TenantId = tenantId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                };

                context.Notifications.Add(notification);
                notifications.Add(notification);

                // Create delivery records for each channel
                var channels = evt.Channels ?? GetDefaultChannelsForUser(recipient, preferences);
                foreach (var channel in channels)
                {
                    var delivery = new NotificationDelivery
                    {
                        Channel = channel,
                        Status = NotificationStatus.Pending,
                        Recipient = GetRecipientAddress(recipient, channel),
                        Notification = notification,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System"
                    };

                    context.NotificationDeliveries.Add(delivery);
                }
            }

            await context.SaveChangesAsync(ct);
            _logger.LogInformation("Created {Count} notifications for event type {Type}", notifications.Count, evt.Type);

            return notifications;
        }

        public async Task<List<Notification>> GetUserNotificationsAsync(
            int userId, 
            int skip = 0, 
            int take = 50, 
            bool unreadOnly = false)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var query = context.Notifications
                .Include(n => n.Deliveries)
                .Where(n => n.UserId == userId);

            if (unreadOnly)
            {
                query = query.Where(n => n.ReadAt == null);
            }

            return await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        public async Task<int> GetUnreadCountAsync(int userId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.Notifications
                .Where(n => n.UserId == userId && n.ReadAt == null)
                .CountAsync();
        }

        public async Task<bool> MarkAsReadAsync(int notificationId, int userId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var notification = await context.Notifications
                .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId);

            if (notification == null)
                return false;

            notification.ReadAt = DateTime.UtcNow;
            notification.Status = NotificationStatus.Read;
            notification.UpdatedAt = DateTime.UtcNow;
            notification.UpdatedBy = userId.ToString();

            await context.SaveChangesAsync();
            return true;
        }

        public async Task<int> MarkAllAsReadAsync(int userId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var unreadNotifications = await context.Notifications
                .Where(n => n.UserId == userId && n.ReadAt == null)
                .ToListAsync();

            var readTime = DateTime.UtcNow;
            foreach (var notification in unreadNotifications)
            {
                notification.ReadAt = readTime;
                notification.Status = NotificationStatus.Read;
                notification.UpdatedAt = readTime;
                notification.UpdatedBy = userId.ToString();
            }

            await context.SaveChangesAsync();
            return unreadNotifications.Count;
        }

        public async Task<NotificationPreference?> GetUserPreferencesAsync(int userId, string notificationType)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            return await context.NotificationPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.NotificationType == notificationType);
        }

        public async Task UpdateUserPreferencesAsync(int userId, string notificationType, NotificationPreference preferences)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var existing = await context.NotificationPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.NotificationType == notificationType);

            if (existing != null)
            {
                existing.InAppEnabled = preferences.InAppEnabled;
                existing.EmailEnabled = preferences.EmailEnabled;
                existing.SmsEnabled = preferences.SmsEnabled;
                existing.MinimumPriority = preferences.MinimumPriority;
                existing.DoNotDisturbEnabled = preferences.DoNotDisturbEnabled;
                existing.DoNotDisturbStart = preferences.DoNotDisturbStart;
                existing.DoNotDisturbEnd = preferences.DoNotDisturbEnd;
                existing.UpdatedAt = DateTime.UtcNow;
                existing.UpdatedBy = userId.ToString();
            }
            else
            {
                preferences.UserId = userId;
                preferences.NotificationType = notificationType;
                preferences.CreatedAt = DateTime.UtcNow;
                preferences.CreatedBy = userId.ToString();
                context.NotificationPreferences.Add(preferences);
            }

            await context.SaveChangesAsync();
        }

        private bool ShouldSendNotification(NotificationEvent evt, NotificationPreference preferences)
        {
            // Check priority threshold
            if (evt.Priority < preferences.MinimumPriority)
                return false;

            // Check do not disturb
            if (preferences.DoNotDisturbEnabled && IsInDoNotDisturbPeriod(preferences))
                return false;

            return true;
        }

        private bool IsInDoNotDisturbPeriod(NotificationPreference preferences)
        {
            if (!preferences.DoNotDisturbStart.HasValue || !preferences.DoNotDisturbEnd.HasValue)
                return false;

            var now = DateTime.Now.TimeOfDay;
            var start = preferences.DoNotDisturbStart.Value;
            var end = preferences.DoNotDisturbEnd.Value;

            if (start <= end)
            {
                return now >= start && now <= end;
            }
            else
            {
                // Crosses midnight
                return now >= start || now <= end;
            }
        }

        private List<NotificationChannel> GetDefaultChannelsForUser(NotificationRecipient recipient, NotificationPreference? preferences)
        {
            var channels = new List<NotificationChannel>();

            if (preferences?.InAppEnabled != false)
                channels.Add(NotificationChannel.InApp);

            if (preferences?.EmailEnabled != false)
                channels.Add(NotificationChannel.Email);

            if (preferences?.SmsEnabled == true)
                channels.Add(NotificationChannel.Sms);

            return channels.Any() ? channels : new List<NotificationChannel> { NotificationChannel.InApp };
        }

        private string? GetRecipientAddress(NotificationRecipient recipient, NotificationChannel channel)
        {
            return channel switch
            {
                NotificationChannel.Email => recipient.Email,
                NotificationChannel.Sms => null, // Would need phone number from user profile
                NotificationChannel.InApp => recipient.UserId.ToString(),
                _ => null
            };
        }
    }
}
