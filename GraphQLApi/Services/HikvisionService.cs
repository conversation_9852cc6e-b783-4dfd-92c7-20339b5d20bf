using System.Net.Http.Headers;

namespace GraphQLApi.Services
{
    public class HikvisionService : IHikvisionService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public HikvisionService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            
            // Configure authentication
            var apiKey = _configuration["HikvisionApi:ApiKey"];
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
        }

        public async Task<string> UploadWorkerPhotoAsync(string workerId, Stream photoStream)
        {
            var content = new MultipartFormDataContent();
            var streamContent = new StreamContent(photoStream);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue("image/jpeg");
            content.Add(streamContent, "photo", $"worker_{workerId}.jpg");

            var response = await _httpClient.PostAsync($"/api/workers/{workerId}/photo", content);
            response.EnsureSuccessStatusCode();

            var photoUrl = await response.Content.ReadAsStringAsync();
            return photoUrl;
        }

        public async Task<Stream> GetWorkerPhotoAsync(string workerId)
        {
            var response = await _httpClient.GetAsync($"/api/workers/{workerId}/photo");
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStreamAsync();
        }

        public async Task DeleteWorkerPhotoAsync(string workerId)
        {
            var response = await _httpClient.DeleteAsync($"/api/workers/{workerId}/photo");
            response.EnsureSuccessStatusCode();
        }
    }
} 