using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Services
{
    public interface IUserService
    {
        Task<ApplicationUser?> GetUserByIdAsync(int userId);
        Task<ApplicationUser?> GetUserByEmailAsync(string email, int tenantId);
        Task<ApplicationUser> CreateUserAsync(CreateUserRequest request);
        Task<ApplicationUser> UpdateUserAsync(int userId, UpdateUserRequest request);
        Task<bool> DeleteUserAsync(int userId, string deletedBy);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(int userId, string newPassword, string resetBy);
        Task<bool> LockUserAsync(int userId, TimeSpan lockDuration, string reason, string lockedBy);
        Task<bool> UnlockUserAsync(int userId, string unlockedBy);
        Task<IEnumerable<ApplicationUser>> GetUsersByTenantAsync(int tenantId, int skip = 0, int take = 50);
        Task<IEnumerable<ApplicationUser>> GetUsersByRoleAsync(int roleId, int skip = 0, int take = 50);
        Task<bool> ValidateUserCredentialsAsync(string email, string password, int tenantId);
        Task LogUserAuditAsync(int userId, AuditAction action, string? description = null, string? ipAddress = null, string? userAgent = null, string? additionalData = null);
    }

    public class CreateUserRequest
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string Password { get; set; } = string.Empty;
        public int RoleId { get; set; }
        public int TenantId { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class UpdateUserRequest
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public int? RoleId { get; set; }
        public UserStatus? Status { get; set; }
        public bool? EmailConfirmed { get; set; }
        public bool? PhoneConfirmed { get; set; }
        public bool? TwoFactorEnabled { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
