using Shared.GraphQL.Models.Training;

namespace GraphQLApi.Services.Training
{
    /// <summary>
    /// Manages training providers at company/tenant level using composition pattern
    /// </summary>
    public interface ITrainingProviderManager
    {
        // Provider CRUD operations (tenant-scoped)
        Task<TrainingProvider> CreateProviderAsync(int tenantId, CreateProviderRequest request);
        Task<TrainingProvider> UpdateProviderAsync(int providerId, UpdateProviderRequest request);
        Task<TrainingProvider> DeactivateProviderAsync(int providerId, string reason);
        Task<TrainingProvider> ActivateProviderAsync(int providerId);
        Task DeleteProviderAsync(int providerId); // Soft delete

        // Provider queries (tenant-scoped)
        Task<TrainingProvider?> GetProviderByIdAsync(int providerId);
        Task<IEnumerable<TrainingProvider>> GetTenantProvidersAsync(int tenantId, bool activeOnly = true);
        Task<IEnumerable<TrainingProvider>> GetProvidersByTypeAsync(int tenantId, ProviderType type);
        Task<TrainingProvider?> GetProviderByNameAsync(int tenantId, string name);

        // Provider authorization (which programs they can deliver)
        Task AuthorizeProviderForProgramAsync(int providerId, int programId);
        Task RevokeProviderAuthorizationAsync(int providerId, int programId);
        Task<IEnumerable<TrainingProgram>> GetProviderAuthorizedProgramsAsync(int providerId);
        Task<IEnumerable<TrainingProvider>> GetAuthorizedProvidersForProgramAsync(int programId);

        // Provider capabilities and reporting
        Task<ProviderCapabilityReport> GetProviderCapabilitiesAsync(int providerId);
        Task<ProviderPerformanceReport> GetProviderPerformanceAsync(int providerId, DateTime fromDate, DateTime toDate);
        Task<TenantProviderDashboard> GetTenantProviderDashboardAsync(int tenantId, int? providerId = null);

        // Validation and compliance
        Task<ProviderValidationResult> ValidateProviderAsync(int providerId);
        Task<bool> IsProviderAuthorizedForProgramAsync(int providerId, int programId);
        Task<bool> IsProviderActiveAsync(int providerId);
        Task<bool> DoesProviderBelongToTenantAsync(int providerId, int tenantId);
    }

    /// <summary>
    /// Request to create a new training provider
    /// </summary>
    public class CreateProviderRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public ProviderType Type { get; set; } = ProviderType.External;
        public List<string>? Certifications { get; set; }
        public Dictionary<string, object>? AccreditationInfo { get; set; }
        public List<int>? AuthorizedProgramIds { get; set; }
        public bool Active { get; set; } = true;
    }

    /// <summary>
    /// Request to update an existing training provider
    /// </summary>
    public class UpdateProviderRequest
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Contact { get; set; }
        public ProviderType? Type { get; set; }
        public List<string>? Certifications { get; set; }
        public Dictionary<string, object>? AccreditationInfo { get; set; }
        public bool? Active { get; set; }
    }

    /// <summary>
    /// Provider capability report
    /// </summary>
    public class ProviderCapabilityReport
    {
        public int ProviderId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public ProviderType Type { get; set; }
        public int AuthorizedProgramsCount { get; set; }
        public List<string> ProgramTitles { get; set; } = new();
        public List<string> Certifications { get; set; } = new();
        public bool IsActive { get; set; }
        public DateTime LastSessionDate { get; set; }
        public int TotalSessionsDelivered { get; set; }
        public int CertificatesIssued { get; set; }
    }

    /// <summary>
    /// Provider performance report
    /// </summary>
    public class ProviderPerformanceReport
    {
        public int ProviderId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int SessionsDelivered { get; set; }
        public int TotalParticipants { get; set; }
        public int CertificatesIssued { get; set; }
        public double PassRate { get; set; }
        public double AverageRating { get; set; }
        public decimal TotalRevenue { get; set; }
        public List<ProgramPerformance> ProgramPerformances { get; set; } = new();
    }

    public class ProgramPerformance
    {
        public int ProgramId { get; set; }
        public string ProgramTitle { get; set; } = string.Empty;
        public int SessionsDelivered { get; set; }
        public int Participants { get; set; }
        public double PassRate { get; set; }
    }

    /// <summary>
    /// Tenant-level provider dashboard
    /// </summary>
    public class TenantProviderDashboard
    {
        public int TenantId { get; set; }
        public string TenantName { get; set; } = string.Empty;
        public int TotalProviders { get; set; }
        public int ActiveProviders { get; set; }
        public int InternalProviders { get; set; }
        public int ExternalProviders { get; set; }
        public int PartnerProviders { get; set; }
        public int CertifiedProviders { get; set; }
        public int TotalSessionsThisMonth { get; set; }
        public int TotalCertificatesIssuedThisMonth { get; set; }
        public decimal TotalRevenueThisMonth { get; set; }
        public List<ProviderCapabilityReport> TopProviders { get; set; } = new();
        public List<ProviderPerformanceIssue> PerformanceIssues { get; set; } = new();
    }

    public class ProviderPerformanceIssue
    {
        public int ProviderId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string IssueType { get; set; } = string.Empty; // "LowPassRate", "InactiveProvider", "ExpiredCertification"
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty; // "Low", "Medium", "High"
    }

    /// <summary>
    /// Provider validation result
    /// </summary>
    public class ProviderValidationResult
    {
        public bool IsValid { get; set; }
        public bool IsActive { get; set; }
        public bool HasValidCertifications { get; set; }
        public bool HasAuthorizedPrograms { get; set; }
        public List<string> ValidationMessages { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}





