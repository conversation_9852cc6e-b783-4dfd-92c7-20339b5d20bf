using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Services
{
    public class UserService : IUserService
    {
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IPasswordService _passwordService;
        private readonly ILogger<UserService> _logger;

        public UserService(
            IDbContextFactory<AppDbContext> dbContextFactory,
            UserManager<ApplicationUser> userManager,
            IPasswordService passwordService,
            ILogger<UserService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _userManager = userManager;
            _passwordService = passwordService;
            _logger = logger;
        }

        public async Task<ApplicationUser?> GetUserByIdAsync(int userId)
        {
            return await _userManager.FindByIdAsync(userId.ToString());
        }

        public async Task<ApplicationUser?> GetUserByEmailAsync(string email, int tenantId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.Users
                .Include(u => u.Role)
                .Include(u => u.Tenant)
                .FirstOrDefaultAsync(u => u.Email == email && u.TenantId == tenantId && !u.IsDeleted);
        }

        public async Task<ApplicationUser> CreateUserAsync(CreateUserRequest request)
        {
            var user = new ApplicationUser
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                UserName = request.Email, // Use email as username for Identity
                PhoneNumber = request.Phone,
                RoleId = request.RoleId,
                TenantId = request.TenantId,
                EmailConfirmed = false,
                PhoneNumberConfirmed = false,
                TwoFactorEnabled = false,
                Status = UserStatus.Active,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            var result = await _userManager.CreateAsync(user, request.Password);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"Failed to create user: {errors}");
            }

            _logger.LogInformation("User created successfully: {UserId} by {CreatedBy}", user.Id, request.CreatedBy);
            return user;
        }

        public async Task<ApplicationUser> UpdateUserAsync(int userId, UpdateUserRequest request)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                throw new InvalidOperationException($"User not found: {userId}");
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(request.FirstName))
                user.FirstName = request.FirstName;
            
            if (!string.IsNullOrEmpty(request.LastName))
                user.LastName = request.LastName;
            
            if (!string.IsNullOrEmpty(request.Email))
            {
                user.Email = request.Email;
                user.UserName = request.Email;
                user.EmailConfirmed = false; // Re-verify email
            }
            
            if (!string.IsNullOrEmpty(request.Phone))
            {
                user.PhoneNumber = request.Phone;
                user.PhoneNumberConfirmed = false; // Re-verify phone
            }
            
            if (request.RoleId.HasValue)
                user.RoleId = request.RoleId;
            
            if (request.Status.HasValue)
                user.Status = request.Status.Value;
            
            if (request.EmailConfirmed.HasValue)
                user.EmailConfirmed = request.EmailConfirmed.Value;
            
            if (request.PhoneConfirmed.HasValue)
                user.PhoneNumberConfirmed = request.PhoneConfirmed.Value;
            
            if (request.TwoFactorEnabled.HasValue)
                user.TwoFactorEnabled = request.TwoFactorEnabled.Value;

            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = request.UpdatedBy;

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"Failed to update user: {errors}");
            }

            _logger.LogInformation("User updated successfully: {UserId} by {UpdatedBy}", userId, request.UpdatedBy);
            return user;
        }

        public async Task<bool> DeleteUserAsync(int userId, string deletedBy)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                return false;
            }

            // Soft delete
            user.IsDeleted = true;
            user.DeletedAt = DateTime.UtcNow;
            user.DeletedBy = deletedBy;
            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = deletedBy;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("User soft deleted: {UserId} by {DeletedBy}", userId, deletedBy);
            }
            
            return result.Succeeded;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                return false;
            }

            var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
            if (result.Succeeded)
            {
                user.UpdatedAt = DateTime.UtcNow;
                user.UpdatedBy = user.Email ?? string.Empty;
                await _userManager.UpdateAsync(user);
                
                _logger.LogInformation("Password changed successfully for user: {UserId}", userId);
            }

            return result.Succeeded;
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword, string resetBy)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                return false;
            }

            // Remove password and set new one (admin reset)
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            var result = await _userManager.ResetPasswordAsync(user, token, newPassword);
            
            if (result.Succeeded)
            {
                user.UpdatedAt = DateTime.UtcNow;
                user.UpdatedBy = resetBy;
                await _userManager.UpdateAsync(user);
                
                _logger.LogInformation("Password reset successfully for user: {UserId} by {ResetBy}", userId, resetBy);
            }

            return result.Succeeded;
        }

        public async Task<bool> LockUserAsync(int userId, TimeSpan lockDuration, string reason, string lockedBy)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                return false;
            }

            user.LockoutEnd = DateTimeOffset.UtcNow.Add(lockDuration);
            user.LockoutEnabled = true;
            user.LockedOutUntil = DateTime.UtcNow.Add(lockDuration);
            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = lockedBy;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                await LogUserAuditAsync(userId, AuditAction.AccountLocked, $"Account locked: {reason}", additionalData: $"Duration: {lockDuration}, Locked by: {lockedBy}");
                _logger.LogInformation("User locked: {UserId} for {Duration} by {LockedBy}. Reason: {Reason}", userId, lockDuration, lockedBy, reason);
            }

            return result.Succeeded;
        }

        public async Task<bool> UnlockUserAsync(int userId, string unlockedBy)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || user.IsDeleted)
            {
                return false;
            }

            user.LockoutEnd = null;
            user.LockedOutUntil = null;
            user.FailedLoginAttempts = 0;
            user.AccessFailedCount = 0;
            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = unlockedBy;

            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                await LogUserAuditAsync(userId, AuditAction.AccountUnlocked, "Account unlocked", additionalData: $"Unlocked by: {unlockedBy}");
                _logger.LogInformation("User unlocked: {UserId} by {UnlockedBy}", userId, unlockedBy);
            }

            return result.Succeeded;
        }

        public async Task<IEnumerable<ApplicationUser>> GetUsersByTenantAsync(int tenantId, int skip = 0, int take = 50)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.Users
                .Include(u => u.Role)
                .Include(u => u.Tenant)
                .Where(u => u.TenantId == tenantId && !u.IsDeleted)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        public async Task<IEnumerable<ApplicationUser>> GetUsersByRoleAsync(int roleId, int skip = 0, int take = 50)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.Users
                .Include(u => u.Role)
                .Include(u => u.Tenant)
                .Where(u => u.RoleId == roleId && !u.IsDeleted)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        public async Task<bool> ValidateUserCredentialsAsync(string email, string password, int tenantId)
        {
            var user = await GetUserByEmailAsync(email, tenantId);
            if (user == null || !user.IsActive)
            {
                return false;
            }

            return await _userManager.CheckPasswordAsync(user, password);
        }

        public async Task LogUserAuditAsync(int userId, AuditAction action, string? description = null, string? ipAddress = null, string? userAgent = null, string? additionalData = null)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var auditLog = new UserAuditLog
                {
                    ApplicationUserId = userId,
                    Action = action,
                    Description = description,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    AdditionalData = additionalData,
                    CreatedAt = DateTime.UtcNow
                };

                context.UserAuditLogs.Add(auditLog);
                await context.SaveChangesAsync();
                
                _logger.LogDebug("User audit logged: {UserId} - {Action}", userId, action);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log user audit: {UserId} - {Action}", userId, action);
            }
        }
    }
}

