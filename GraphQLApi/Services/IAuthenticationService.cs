using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Services
{
    public interface IAuthenticationService
    {
        Task<AuthenticationResult> LoginAsync(LoginRequest request);
        Task<AuthenticationResult> RefreshTokenAsync(RefreshTokenRequest request);
        Task<bool> LogoutAsync(LogoutRequest request);
        Task<bool> LogoutAllSessionsAsync(int userId);
        Task<UserSession> CreateSessionAsync(ApplicationUser user, string ipAddress, string userAgent);
        Task<bool> EndSessionAsync(string sessionId, string reason);
        Task<IEnumerable<UserSession>> GetActiveSessionsAsync(int userId);
        Task CleanupExpiredSessionsAsync();
        Task<bool> ValidateSessionAsync(string sessionId);
    }

    public class LoginRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public int TenantId { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public bool RememberMe { get; set; } = false;
    }

    public class RefreshTokenRequest
    {
        public string RefreshToken { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
    }

    public class LogoutRequest
    {
        public int UserId { get; set; }
        public string? RefreshToken { get; set; }
        public string? SessionId { get; set; }
        public string IpAddress { get; set; } = string.Empty;
    }

    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public ApplicationUser? User { get; set; }
        public UserSession? Session { get; set; }

        public static AuthenticationResult Failed(string errorMessage)
        {
            return new AuthenticationResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }

        public static AuthenticationResult CreateSuccess(string accessToken, string refreshToken, ApplicationUser user, UserSession? session = null)
        {
            return new AuthenticationResult
            {
                Success = true,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                User = user,
                Session = session,
                ExpiresAt = DateTime.UtcNow.AddMinutes(15) // Default access token expiration
            };
        }
    }
}
