using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models.Training;
using Shared.Enums;
using Shared.GraphQL.Models;
using GraphQLApi.GraphQL.Queries;

namespace GraphQLApi.Services
{
    public class TrainingService : ITrainingService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly ILogger<TrainingService> _logger;

        public TrainingService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            ILogger<TrainingService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _logger = logger;
        }

        public async Task<TrainingSession> CreateSessionAsync(CreateTrainingSessionInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Get conductor information
            var conductor = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == input.ConductorId);

            // Get program
            var program = await context.TrainingPrograms
                .FirstOrDefaultAsync(p => p.Id == input.ProgramId) ?? throw new ArgumentException($"Program with ID {input.ProgramId} not found");

            // Get provider
            var provider = await context.TrainingProviders
                .FirstOrDefaultAsync(p => p.Id == input.ProviderId) ?? throw new ArgumentException($"Provider with ID {input.ProviderId} not found");

            var session = new TrainingSession
            {
                ProgramId = input.ProgramId,
                ProviderId = input.ProviderId,
                SiteId = input.SiteId,
                Mode = input.Mode,
                Location = input.Location,
                StartDate = input.StartDate,
                EndDate = input.EndDate,
                Status = TrainingSessionStatus.DRAFT,
                Capacity = input.Capacity,
                Notes = input.Notes,
                Conductor = new TrainingConductor
                {
                    WorkerId = conductor.Id,
                    Name = conductor.Name,
                    SignatureFileId = string.Empty
                }
            };

            context.TrainingSessions.Add(session);

            // Handle session picture upload (like toolbox)
            if (input.SessionPictureFile != null)
            {
                using var sessionPictureStream = input.SessionPictureFile.OpenReadStream();
                var sessionPictureMetadata = await _minioService.UploadFileAsync(
                    sessionPictureStream,
                    input.SessionPictureFile.Name,
                    Shared.Constants.FileStorageConstants.BucketNames.DOCS,
                    input.SessionPictureFile.ContentType ?? "image/jpeg",
                    "Session picture for Training",
                    $"training_session_{session.Id}/session-picture",
                    false,
                    null);

                session.SessionPictureFile = sessionPictureMetadata;
            }

            await context.SaveChangesAsync();

            // Copy signature file to temp bucket (like toolbox)
            string conductorSignatureFileId = string.Empty;
            if (conductor.SignatureFileId.HasValue)
            {
                conductorSignatureFileId = await CopySignatureToTempAsync(conductor.SignatureFileId.Value, session.StartDate, session.Id);
            }
            
            session.Conductor = new TrainingConductor
            {
                WorkerId = conductor.Id,
                Name = conductor.Name,
                SignatureFileId = conductorSignatureFileId
            };

            await context.SaveChangesAsync();
            return session;
        }

        public async Task AddAttendeesAsync(int sessionId, IEnumerable<int> workerIds)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var session = await context.TrainingSessions
                .FirstOrDefaultAsync(s => s.Id == sessionId) ?? throw new ArgumentException($"Training session with ID {sessionId} not found");

            // Validate state transition (like toolbox)
            if (session.Status != TrainingSessionStatus.DRAFT)
                throw new InvalidOperationException($"Cannot add attendees to session with status {session.Status}");

            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();
            
            if (workers.Count != workerIds.Count())
                throw new ArgumentException("One or more workers not found");

            // Create enrollment records for each worker
            foreach (var worker in workers)
            {
                // Create enrollment record
                var enrollment = new TrainingEnrollment
                {
                    SessionId = sessionId,
                    WorkerId = worker.Id,
                    Status = EnrollmentStatus.REGISTERED
                };
                context.TrainingEnrollments.Add(enrollment);
            }

            session.Status = TrainingSessionStatus.SCHEDULED;

            await context.SaveChangesAsync();
        }

        public async Task UpdateSessionStatusAsync(int sessionId, TrainingSessionStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var session = await context.TrainingSessions
                .FirstOrDefaultAsync(s => s.Id == sessionId) ?? throw new ArgumentException($"Training session with ID {sessionId} not found");

            session.Status = status;
            await context.SaveChangesAsync();
        }

        public async Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var session = await context.TrainingSessions
                .Include(s => s.Program)
                .Include(s => s.Enrollments)
                .FirstOrDefaultAsync(s => s.Id == sessionId) ?? throw new ArgumentException($"Training session with ID {sessionId} not found");

            if (session.Status != TrainingSessionStatus.COMPLETED)
                throw new InvalidOperationException($"Cannot finalize session with status {session.Status}");

            // Update enrollment status based on attendance
            foreach (var attendanceInput in attendance)
            {
                var enrollment = session.Enrollments.FirstOrDefault(e => e.WorkerId == attendanceInput.WorkerId);
                if (enrollment != null)
                {
                    // Update enrollment status to ATTENDED or DID_NOT_ATTEND
                    enrollment.Status = attendanceInput.Outcome == TrainingOutcome.PASS || attendanceInput.Outcome == TrainingOutcome.FAIL
                        ? EnrollmentStatus.ATTENDED
                        : EnrollmentStatus.DID_NOT_ATTEND;

                    enrollment.CompletedAt = DateTime.UtcNow;
                    enrollment.Outcome = attendanceInput.Outcome;
                    enrollment.Notes = attendanceInput.Notes;
                    enrollment.UpdatedAt = DateTime.UtcNow;

                    // Issue certificate if passed
                    if (attendanceInput.Outcome == TrainingOutcome.PASS)
                    {
                        await IssueCertificateAsync(new IssueCertificateInput
                        {
                            SessionId = sessionId,
                            WorkerId = attendanceInput.WorkerId,
                            Outcome = attendanceInput.Outcome,
                            CertificateNo = $"{session.Program.Code}-{DateTime.UtcNow:yyyyMMdd}-{attendanceInput.WorkerId:D6}",
                            Notes = attendanceInput.Notes
                        });
                    }
                }
            }

            session.Status = TrainingSessionStatus.FINALIZED;
            await context.SaveChangesAsync();
        }

        private async Task<string> CopySignatureToTempAsync(int sourceFileId, DateTime sessionDate, int sessionId)
        {
            try
            {
                // Get source file metadata (like toolbox implementation)
                await using var context = await _contextFactory.CreateDbContextAsync();
                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId);

                if (sourceFile == null)
                    return string.Empty;

                // Create temp folder path for the training session
                var tempFolderPath = $"training_session_{sessionId}_{sessionDate:yyyyMMdd}/signatures";

                // Download the source file from MinIO
                using var sourceStream = await _minioService.DownloadFileAsync(sourceFile);

                // Generate unique filename for temp copy
                var tempFileName = $"{System.IO.Path.GetFileNameWithoutExtension(sourceFile.FileName)}_{Guid.NewGuid()}{System.IO.Path.GetExtension(sourceFile.FileName)}";

                // Upload to temp bucket with folder path
                var tempFileMetadata = await _minioService.UploadFileAsync(
                    sourceStream,
                    tempFileName,
                    Shared.Constants.FileStorageConstants.BucketNames.TEMP,
                    sourceFile.ContentType,
                    $"Temporary copy of signature for training session {sessionDate:yyyyMMdd}",
                    tempFolderPath,
                    false, // Not public
                    DateTime.UtcNow.AddDays(30) // Expire in 30 days
                );

                _logger.LogInformation("Successfully copied signature file {SourceFileId} to temp bucket as {TempFileId}",
                    sourceFileId, tempFileMetadata.Id);

                return tempFileMetadata.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to temp bucket", sourceFileId);
                throw new InvalidOperationException("Failed to copy signature file", ex);
            }
        }

        public async Task<TrainingSession?> GetSessionByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingSessions
                .Include(s => s.Program)
                .Include(s => s.Provider)
                .Include(s => s.Site)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<TrainingSession>> GetAllSessionsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingSessions
                .Include(s => s.Program)
                .Include(s => s.Provider)
                .Include(s => s.Site)
                .ToListAsync();
        }

        public async Task<IEnumerable<TrainingSession>> GetSessionsByDateAsync(DateTime date)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingSessions
                .Include(s => s.Program)
                .Include(s => s.Provider)
                .Include(s => s.Site)
                .Where(s => s.StartDate.Date == date.Date)
                .ToListAsync();
        }

        public async Task<TrainingSession?> GetTodaysSessionAsync(int programId)
        {
            var today = DateTime.Today;
            var sessions = await GetSessionsByDateAsync(today);
            return sessions.FirstOrDefault(s => s.ProgramId == programId);
        }

        public async Task<IEnumerable<TrainingProgram>> GetActiveProgramsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingPrograms
                .Where(p => p.Active && !p.IsDeleted)
                .ToListAsync();
        }

        public async Task<TrainingProgram?> GetProgramByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingPrograms
                .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
        }

        public async Task<IEnumerable<TrainingProvider>> GetActiveProvidersAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingProviders
                .Where(p => p.Active && !p.IsDeleted)
                .ToListAsync();
        }

        public async Task<TrainingProvider?> GetProviderByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingProviders
                .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
        }

        public async Task<TrainingEnrollment> EnrollWorkerAsync(int sessionId, int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var existingEnrollment = await context.TrainingEnrollments
                .FirstOrDefaultAsync(e => e.SessionId == sessionId && e.WorkerId == workerId);

            if (existingEnrollment != null)
                return existingEnrollment;

            var enrollment = new TrainingEnrollment
            {
                SessionId = sessionId,
                WorkerId = workerId,
                Status = EnrollmentStatus.REGISTERED
            };

            context.TrainingEnrollments.Add(enrollment);
            await context.SaveChangesAsync();

            return enrollment;
        }

        public async Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsAsync(int sessionId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingEnrollments
                .Include(e => e.Worker)
                .Where(e => e.SessionId == sessionId)
                .ToListAsync();
        }

        public async Task UpdateEnrollmentStatusAsync(int enrollmentId, EnrollmentStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var enrollment = await context.TrainingEnrollments
                .FirstOrDefaultAsync(e => e.Id == enrollmentId) ?? throw new ArgumentException($"Enrollment with ID {enrollmentId} not found");

            enrollment.Status = status;
            enrollment.UpdatedAt = DateTime.UtcNow;
            
            if (status == EnrollmentStatus.ATTENDED || status == EnrollmentStatus.DID_NOT_ATTEND)
                enrollment.CompletedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();
        }

        public async Task<TrainingCertificate> IssueCertificateAsync(IssueCertificateInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var session = await context.TrainingSessions
                .Include(s => s.Program)
                .Include(s => s.Provider)
                .FirstOrDefaultAsync(s => s.Id == input.SessionId) ?? throw new ArgumentException($"Session with ID {input.SessionId} not found");

            var worker = await context.Workers
                .FirstOrDefaultAsync(w => w.Id == input.WorkerId) ?? throw new ArgumentException($"Worker with ID {input.WorkerId} not found");

            var expiryDate = input.CustomExpiryDate ?? (session.Program.ValidityDays > 0 
                ? DateTime.UtcNow.AddDays(session.Program.ValidityDays) 
                : DateTime.UtcNow.AddYears(1)); // Default 1 year if no validity set

            var certificate = new TrainingCertificate
            {
                WorkerId = input.WorkerId,
                ProgramId = session.ProgramId,
                ProviderName = session.Provider?.Name, // Store provider name instead of ID
                SessionId = input.SessionId,
                CertificateNo = input.CertificateNo,
                IssueDate = DateTime.UtcNow,
                ExpiryDate = expiryDate,
                Status = CertificateStatus.ISSUED,
                Notes = input.Notes
            };

            // Handle certificate file upload
            if (input.CertificateFile != null)
            {
                using var certificateStream = input.CertificateFile.OpenReadStream();
                var certificateMetadata = await _minioService.UploadFileAsync(
                    certificateStream,
                    input.CertificateFile.Name,
                    Shared.Constants.FileStorageConstants.BucketNames.DOCS,
                    input.CertificateFile.ContentType ?? "application/pdf",
                    "Certificate file",
                    $"training_certificates/{worker.Id}/{session.Program.Code}",
                    false,
                    null);

                certificate.CertificateFile = certificateMetadata;
            }

            context.TrainingCertificates.Add(certificate);
            await context.SaveChangesAsync();

            return certificate;
        }

        public async Task<IEnumerable<TrainingCertificate>> GetWorkerCertificatesAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingCertificates
                .Include(c => c.Program)
                .Where(c => c.WorkerId == workerId && !c.IsDeleted)
                .OrderByDescending(c => c.IssueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<TrainingCertificate>> GetExpiringCertificatesAsync(int daysAhead = 30)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var expiryThreshold = DateTime.UtcNow.AddDays(daysAhead);

            return await context.TrainingCertificates
                .Include(c => c.Worker)
                .Include(c => c.Program)
                .Where(c => c.ExpiryDate <= expiryThreshold &&
                           c.Status == CertificateStatus.VALID &&
                           !c.IsDeleted)
                .ToListAsync();
        }

        public async Task EvaluateAndUpdateCertificateStatusesAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var now = DateTime.UtcNow;

            // Load certificates that might need status transitions
            var certificates = await context.TrainingCertificates
                .Include(c => c.Program)
                .Where(c => !c.IsDeleted)
                .ToListAsync();

            int updates = 0;

            foreach (var cert in certificates)
            {
                var originalStatus = cert.Status;

                // Transition ISSUED -> VALID on first evaluation
                if (cert.Status == CertificateStatus.ISSUED)
                {
                    cert.Status = CertificateStatus.VALID;
                }

                // Determine expiring soon threshold (default 30 days; allow per-program validity to infer)
                var daysUntilExpiry = (cert.ExpiryDate - now).TotalDays;
                var expiringSoonThresholdDays = 30;

                if (cert.ExpiryDate <= now)
                {
                    cert.Status = CertificateStatus.EXPIRED;
                }
                else if (daysUntilExpiry <= expiringSoonThresholdDays && cert.Status == CertificateStatus.VALID)
                {
                    cert.Status = CertificateStatus.EXPIRING_SOON;
                }
                else if (daysUntilExpiry > expiringSoonThresholdDays && cert.Status == CertificateStatus.EXPIRING_SOON)
                {
                    // If it moved back out of the window (date changed), revert to VALID
                    cert.Status = CertificateStatus.VALID;
                }

                if (cert.Status != originalStatus)
                {
                    updates++;
                }
            }

            if (updates > 0)
            {
                await context.SaveChangesAsync();
                _logger.LogInformation("Updated {Count} training certificate statuses", updates);
            }
        }

        public async Task UpdateCertificateStatusAsync(int certificateId, CertificateStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var certificate = await context.TrainingCertificates
                .FirstOrDefaultAsync(c => c.Id == certificateId) ?? throw new ArgumentException($"Certificate with ID {certificateId} not found");

            certificate.Status = status;
            await context.SaveChangesAsync();
        }

        public async Task<WorkerEligibilityStatus> CheckWorkerEligibilityAsync(int workerId, int tradeId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Get required programs for the trade
            var requiredPrograms = await context.TradeRequirements
                .Include(tr => tr.Program)
                .Where(tr => tr.TradeId == tradeId && tr.Mandatory)
                .ToListAsync();

            if (!requiredPrograms.Any())
                return WorkerEligibilityStatus.ELIGIBLE;

            // Get worker's current certificates
            var workerCertificates = await context.TrainingCertificates
                .Where(c => c.WorkerId == workerId && 
                           c.Status == CertificateStatus.VALID && 
                           !c.IsDeleted)
                .ToListAsync();

            var missingRequirements = requiredPrograms
                .Where(rp => !workerCertificates.Any(wc => wc.ProgramId == rp.ProgramId))
                .ToList();

            if (!missingRequirements.Any())
                return WorkerEligibilityStatus.ELIGIBLE;

            // Check if any certificates are expiring soon (grace period)
            var expiringCertificates = workerCertificates
                .Where(c => c.ExpiryDate <= DateTime.UtcNow.AddDays(7)) // 7 day grace period
                .ToList();

            if (expiringCertificates.Any())
                return WorkerEligibilityStatus.ELIGIBLE_WITHIN_GRACE;

            return WorkerEligibilityStatus.NOT_ELIGIBLE;
        }

        public async Task<IEnumerable<TrainingCertificate>> GetRequiredCertificatesForTradeAsync(int workerId, int tradeId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var requiredProgramIds = await context.TradeRequirements
                .Where(tr => tr.TradeId == tradeId && tr.Mandatory)
                .Select(tr => tr.ProgramId)
                .ToListAsync();

            return await context.TrainingCertificates
                .Include(c => c.Program)
                .Where(c => c.WorkerId == workerId && 
                           requiredProgramIds.Contains(c.ProgramId) && 
                           !c.IsDeleted)
                .ToListAsync();
        }

        public async Task<WorkerCertificateStatusSummary> GetWorkerCertificateStatusAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var certificates = await context.TrainingCertificates
                .Where(c => c.WorkerId == workerId && !c.IsDeleted)
                .ToListAsync();

            var now = DateTime.UtcNow;
            var expiringSoonThresholdDays = 30;

            int valid = 0, expiring = 0, expired = 0;
            DateTime? nearestExpiry = null;

            foreach (var cert in certificates)
            {
                if (cert.ExpiryDate <= now || cert.Status == CertificateStatus.EXPIRED)
                {
                    expired++;
                }
                else if ((cert.ExpiryDate - now).TotalDays <= expiringSoonThresholdDays || cert.Status == CertificateStatus.EXPIRING_SOON)
                {
                    expiring++;
                }
                else if (cert.Status == CertificateStatus.VALID || cert.Status == CertificateStatus.ISSUED)
                {
                    valid++;
                }

                if (!nearestExpiry.HasValue || cert.ExpiryDate < nearestExpiry.Value)
                {
                    nearestExpiry = cert.ExpiryDate;
                }
            }

            var overall = expired > 0
                ? CertificateStatus.EXPIRED
                : (expiring > 0 ? CertificateStatus.EXPIRING_SOON : (valid > 0 ? CertificateStatus.VALID : CertificateStatus.REVOKED));

            return new WorkerCertificateStatusSummary
            {
                WorkerId = workerId,
                TotalCertificates = certificates.Count,
                ValidCount = valid,
                ExpiringSoonCount = expiring,
                ExpiredCount = expired,
                OverallStatus = overall,
                NearestExpiryDate = nearestExpiry
            };
        }

        public async Task<Dictionary<int, WorkerCertificateStatusSummary>> GetWorkersCertificateStatusesAsync(IEnumerable<int> workerIds)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var idSet = workerIds.ToHashSet();

            var certificates = await context.TrainingCertificates
                .Where(c => idSet.Contains(c.WorkerId) && !c.IsDeleted)
                .ToListAsync();

            var now = DateTime.UtcNow;
            var expiringSoonThresholdDays = 30;

            var grouped = certificates.GroupBy(c => c.WorkerId);
            var result = new Dictionary<int, WorkerCertificateStatusSummary>();

            foreach (var group in grouped)
            {
                int valid = 0, expiring = 0, expired = 0;
                DateTime? nearestExpiry = null;

                foreach (var cert in group)
                {
                    if (cert.ExpiryDate <= now || cert.Status == CertificateStatus.EXPIRED)
                    {
                        expired++;
                    }
                    else if ((cert.ExpiryDate - now).TotalDays <= expiringSoonThresholdDays || cert.Status == CertificateStatus.EXPIRING_SOON)
                    {
                        expiring++;
                    }
                    else if (cert.Status == CertificateStatus.VALID || cert.Status == CertificateStatus.ISSUED)
                    {
                        valid++;
                    }

                    if (!nearestExpiry.HasValue || cert.ExpiryDate < nearestExpiry.Value)
                    {
                        nearestExpiry = cert.ExpiryDate;
                    }
                }

                var overall = expired > 0
                    ? CertificateStatus.EXPIRED
                    : (expiring > 0 ? CertificateStatus.EXPIRING_SOON : (valid > 0 ? CertificateStatus.VALID : CertificateStatus.REVOKED));

                result[group.Key] = new WorkerCertificateStatusSummary
                {
                    WorkerId = group.Key,
                    TotalCertificates = group.Count(),
                    ValidCount = valid,
                    ExpiringSoonCount = expiring,
                    ExpiredCount = expired,
                    OverallStatus = overall,
                    NearestExpiryDate = nearestExpiry
                };
            }

            // Ensure workers with no certificates are included
            foreach (var wid in idSet)
            {
                if (!result.ContainsKey(wid))
                {
                    result[wid] = new WorkerCertificateStatusSummary
                    {
                        WorkerId = wid,
                        TotalCertificates = 0,
                        ValidCount = 0,
                        ExpiringSoonCount = 0,
                        ExpiredCount = 0,
                        OverallStatus = CertificateStatus.REVOKED,
                        NearestExpiryDate = null
                    };
                }
            }

            return result;
        }

        public async Task MarkAttendanceAsync(int enrollmentId, bool attended, string? notes = null)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var enrollment = await context.TrainingEnrollments
                .FirstOrDefaultAsync(e => e.Id == enrollmentId) ?? throw new ArgumentException($"Enrollment with ID {enrollmentId} not found");

            enrollment.Status = attended ? EnrollmentStatus.ATTENDED : EnrollmentStatus.DID_NOT_ATTEND;
            enrollment.CompletedAt = DateTime.UtcNow;
            enrollment.UpdatedAt = DateTime.UtcNow;
            
            if (!string.IsNullOrEmpty(notes))
                enrollment.Notes = notes;

            await context.SaveChangesAsync();
        }

        public async Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsWithAttendanceAsync(int sessionId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.TrainingEnrollments
                .Include(e => e.Worker)
                .Include(e => e.Session)
                .Where(e => e.SessionId == sessionId)
                .ToListAsync();
        }

        public async Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersByTradeAsync(int sessionId, int tradeId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Get workers with the specified trade
            var workers = await context.Workers
                .Include(w => w.Trades)
                .Where(w => w.Trades.Any(t => t.Id == tradeId) && !w.IsDeleted)
                .ToListAsync();

            var enrollments = new List<TrainingEnrollment>();

            foreach (var worker in workers)
            {
                // Check if already enrolled
                var existingEnrollment = await context.TrainingEnrollments
                    .FirstOrDefaultAsync(e => e.SessionId == sessionId && e.WorkerId == worker.Id);

                if (existingEnrollment == null)
                {
                    var enrollment = new TrainingEnrollment
                    {
                        SessionId = sessionId,
                        WorkerId = worker.Id,
                        Status = EnrollmentStatus.REGISTERED
                    };

                    context.TrainingEnrollments.Add(enrollment);
                    enrollments.Add(enrollment);
                }
                else
                {
                    enrollments.Add(existingEnrollment);
                }
            }

            await context.SaveChangesAsync();
            return enrollments;
        }

        public async Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersBySiteAsync(int sessionId, Guid siteId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            // TODO: Implement site-worker relationship
            // For now, return empty list until site-worker relationship is established
            var enrollments = new List<TrainingEnrollment>();
            return enrollments;
        }

        public async Task ClearSessionTempFolderAsync(int sessionId)
        {
            try
            {
                // Implementation to delete session temp folder (like toolbox)
                //TODO: actually implement it 
                _logger.LogInformation("Cleared temp folder for training session {SessionId}", sessionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear temp folder for training session {SessionId}", sessionId);
                throw;
            }
        }

        // Legacy Training Management (backward compatibility with existing mutations)
        public async Task<LegacyTraining> CreateTrainingAsync(LegacyTraining training)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            context.Trainings.Add(training);
            await context.SaveChangesAsync();
            return training;
        }

        public async Task<LegacyTraining?> GetTrainingByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Trainings.FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<LegacyTraining?> UpdateTrainingAsync(int id, LegacyTraining updatedTraining)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var existing = await context.Trainings.FirstOrDefaultAsync(t => t.Id == id);
            if (existing == null)
            {
                return null;
            }

            existing.Name = updatedTraining.Name;
            existing.Description = updatedTraining.Description;
            existing.StartDate = updatedTraining.StartDate;
            existing.EndDate = updatedTraining.EndDate;
            existing.Duration = updatedTraining.Duration;
            existing.ValidityPeriodMonths = updatedTraining.ValidityPeriodMonths;
            existing.TrainingType = updatedTraining.TrainingType;
            existing.Trainer = updatedTraining.Trainer;
            existing.Frequency = updatedTraining.Frequency;
            existing.Status = updatedTraining.Status;
            existing.UpdatedAt = DateTime.UtcNow;
            existing.UpdatedBy = "System";

            await context.SaveChangesAsync();
            return existing;
        }

        public async Task<bool> DeleteTrainingAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var existing = await context.Trainings.FirstOrDefaultAsync(t => t.Id == id);
            if (existing == null)
            {
                return false;
            }

            // Soft delete
            existing.IsDeleted = true;
            existing.DeletedAt = DateTime.UtcNow;
            await context.SaveChangesAsync();
            return true;
        }
    }
}