using Shared.GraphQL.Models;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public interface ISiteService
    {
        // Basic CRUD operations
        Task<IEnumerable<Site>> GetAllSitesAsync();
        Task<Site?> GetSiteByIdAsync(Guid id);
        Task<Site> CreateSiteAsync(string name, SiteDataDto? siteData = null);
        Task<Site?> UpdateSiteAsync(Guid id, string? name = null, SiteDataDto? siteData = null);
        Task<bool> DeleteSiteAsync(Guid id);

        // Site data specific operations
        Task<SiteDataDto?> GetSiteDataAsync(Guid id);
        Task<Site?> UpdateSiteDataAsync(Guid id, SiteDataDto siteData);
        Task<Site?> PatchSiteDataAsync(Guid id, string jsonPatch);

        // Query operations
        Task<IEnumerable<Site>> GetSitesByStatusAsync(string status);
        Task<IEnumerable<Site>> GetSitesByProjectManagerAsync(string projectManager);
        Task<IEnumerable<Site>> GetSitesByProjectTypeAsync(string projectType);
        Task<IEnumerable<Site>> SearchSitesAsync(string searchTerm);

        // Utility operations
        Task<bool> SiteExistsAsync(Guid id);
        Task<bool> ValidateSiteDataAsync(SiteDataDto siteData);
        Task<Site?> CloneSiteAsync(Guid sourceId, string newName);
    }
}
