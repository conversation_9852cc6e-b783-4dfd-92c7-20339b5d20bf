using System.Collections.Generic;
using System.Threading.Tasks;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public interface IRecipientResolutionService
    {
        Task<List<NotificationRecipient>> ResolveRecipientsAsync(List<string> recipientRules, int tenantId);
        Task<List<NotificationRecipient>> ResolveRecipientsByRoleAsync(string roleName, int tenantId);
        Task<List<NotificationRecipient>> ResolveRecipientsByUserEmailAsync(string email, int tenantId);
        Task<List<NotificationRecipient>> ResolveRecipientsByUserIdAsync(int userId);
        Task<List<NotificationRecipient>> ResolveRecipientsByPermissionAsync(string resource, string operation, string level, int tenantId);
    }
}
