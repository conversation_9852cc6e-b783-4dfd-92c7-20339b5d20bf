using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.Enums;
using Shared.Errors;

namespace GraphQLApi.Services
{
    public class JobService : IJobService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;

        public JobService(IDbContextFactory<AppDbContext> contextFactory, IMinioService minioService)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
        }

        public async Task<Job> CreateJobAsync(
            string title,
            int chiefEngineerId,// todo: replace with system later
            string location, // todo: engineer
            IEnumerable<int>? tradeIds,
            TimeSpan? timeForCompletion, // todo: default to 1 day
            DateTime? startDate = null,// todo: always tommorow
            string? description = null // todo: engineer
            )
        {
            tradeIds ??= [];
            await using var context = await _contextFactory.CreateDbContextAsync();

            // Validate chief engineer exists
            var chiefEngineer = await context.Workers.FindAsync(chiefEngineerId)
                ?? throw new ArgumentException($"Chief engineer with ID {chiefEngineerId} not found");

            // Validate trades exist
            var trades = await context.Trades
                .Where(t => tradeIds.Contains(t.Id))
                .ToListAsync();

            if (trades.Count != tradeIds.Count())
                throw new ArgumentException("One or more trades not found");


            var job = new Job
            {
                Title = title,
                Description = description ?? string.Empty,
                RequiredTrades = [.. trades],
                Status = JobStatus.REQUESTED,
                TimeForCompletion = timeForCompletion ?? TimeSpan.FromDays(1),
                StartDate = startDate ?? DateTime.Today.AddDays(1),
                ChiefEngineerId = chiefEngineerId,
                RequestedDate = DateTime.UtcNow,
                Location = location
            };

            // Calculate due date
            job.DueDate = job.StartDate.Add(timeForCompletion ?? TimeSpan.FromDays(1));


            context.Jobs.Add(job);
            Job.ValidateStartDate(job);
            Job.ValidateDueDate(job);
            await context.SaveChangesAsync();

            // TODO: notification added here

            return await GetJobByIdAsync(job.Id) ?? job;
        }

        public async Task<IEnumerable<Job>> GetRequestedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.REQUESTED);
        }

        public async Task<Job> BlockJobAsync(int jobId, int blockedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId) ?? throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.REQUESTED)
                throw new InvalidOperationException($"Cannot block job with status {job.Status}. Only REQUESTED jobs can be blocked.");

            // Validate blocker exists
            var blocker = await context.Workers.FindAsync(blockedById) ?? throw new ArgumentException($"Worker with ID {blockedById} not found");
            job.Status = JobStatus.BLOCKED;
            job.BlockedById = blockedById;
            job.BlockedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<IEnumerable<Job>> GetBlockedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.BLOCKED);
        }

        public async Task<Job> ReviewJobAsync(
            int jobId,
            int reviewedById, // todo: replace with system later
            IEnumerable<HazardInput> hazards,
            IEnumerable<PermitType> requiredPermits,
            List<string> PPEs,
            HashSet<string> precautionsRequired,
            List<string>? modesOfAccessToBeUsed = null,
            IEnumerable<int>? trainingIds = null,
            List<string>? fireExtinguishers = null,
            List<string>? excavationProtectionSystems = null,
            string? depthOfExcavation = null,      // todo: site HSE     
            List<string>? excavationEquipmentsToBeUsed = null, // todo: site HSE
            List<string>? natureOfHotWorks = null,// todo: site HSE
            IEnumerable<GraphQLApi.GraphQL.Types.DocumentFileInput>? additionalDocuments = null
            )
        {
            additionalDocuments ??= [];
            trainingIds ??= [];
            await using var context = await _contextFactory.CreateDbContextAsync();
            List<FileMetadata> fileMetadataList = [];
            try
            {
                var job = await context.Jobs
                    .Include(j => j.ChiefEngineer)
                    .Include(j => j.Hazards)
                        .ThenInclude(h => h.ControlMeasures)
                    .Include(j => j.Documents)
                        .ThenInclude(d => d.FileMetadata)
                    .FirstOrDefaultAsync(j => j.Id == jobId)
                    ?? throw new ArgumentException($"Job with ID {jobId} not found");

                // Validate state transition
                if (job.Status != JobStatus.REQUESTED && job.Status != JobStatus.DISAPPROVED)
                    throw new InvalidOperationException($"Cannot review job with status {job.Status}. Only REQUESTED or DISAPPROVED jobs can be reviewed.");

                // Validate reviewer exists
                var reviewer = await context.Workers.FindAsync(reviewedById)
                    ?? throw new ArgumentException($"Worker with ID {reviewedById} not found");
                var trainings = await context.Trainings
                    .Where(t => trainingIds.Contains(t.Id))
                    .ToListAsync();

                if (trainings.Count != trainingIds.Count())
                    throw new ArgumentException("One or more trainings not found");

                // Update job status and reviewer info
                job.Status = JobStatus.PENDING_APPROVAL;
                job.ReviewedById = reviewedById;
                job.ReviewedDate = DateTime.UtcNow;
                job.RequiredPermits = [.. requiredPermits];
                job.PrecautionsRequired = [.. precautionsRequired];
                job.PPEs = PPEs;
                job.RequiredTrainings = [.. trainings];


                job.ExcavationFields = generateExcavationFields(depthOfExcavation, excavationProtectionSystems, excavationEquipmentsToBeUsed);
                job.HotWorkFields = generateHotWorkFields(fireExtinguishers, natureOfHotWorks);
                job.WorkAtHeightFields = generateWorkAtHeightFields(modesOfAccessToBeUsed);

                foreach (var hazardInput in hazards)
                {
                    var hazard = new Hazard
                    {
                        Description = hazardInput.Description,
                        JobId = jobId
                    };

                    foreach (var controlMeasureDesc in hazardInput.ControlMeasures)
                    {
                        hazard.ControlMeasures.Add(new ControlMeasure
                        {
                            Description = controlMeasureDesc
                        });
                    }

                    job.Hazards.Add(hazard);
                }

                foreach (var doc in additionalDocuments)
                {
                    using var docStream = doc.File.OpenReadStream();
                    var docMetadata = await _minioService.UploadFileAsync(
                        docStream,
                        doc.File.Name,
                        "docs",
                        doc.File.ContentType ?? "application/pdf",
                        doc.Description,
                        $"job-{jobId}",
                        doc.IsPublic,
                        doc.ExpiresAt);

                    if (docMetadata != null)
                    {
                        var documentFile = new DocumentFile
                        {
                            Name = doc.Name,
                            FileMetadataId = docMetadata.Id,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "System"
                        };
                        fileMetadataList.Add(docMetadata);
                        context.DocumentFiles.Add(documentFile);
                        job.Documents.Add(documentFile);
                    }
                }
                Job.ValidatePermits(job);
                await context.SaveChangesAsync();

                return job;
            }
            catch
            {
                // Console.WriteLine(ex);
                foreach (var fileMetadata in fileMetadataList)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                throw;
            }
        }

        public async Task<IEnumerable<Job>> GetPendingApprovalJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.PENDING_APPROVAL);
        }

        public async Task<Job> ApproveJobAsync(int jobId, int approvedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.PENDING_APPROVAL)
                throw new InvalidOperationException($"Cannot approve job with status {job.Status}. Only PENDING_APPROVAL jobs can be approved.");

            // Validate approver exists
            var approver = await context.Workers.FindAsync(approvedById) ?? throw new ArgumentException($"Worker with ID {approvedById} not found");
            job.Status = JobStatus.APPROVED;
            job.ApprovedById = approvedById;
            job.ApprovedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<Job> DisapproveJobAsync(int jobId, int disapprovedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.PENDING_APPROVAL)
                throw new InvalidOperationException($"Cannot disapprove job with status {job.Status}. Only PENDING_APPROVAL jobs can be disapproved.");

            // Validate disapprover exists
            var disapprover = await context.Workers.FindAsync(disapprovedById) ?? throw new ArgumentException($"Worker with ID {disapprovedById} not found");
            job.Status = JobStatus.DISAPPROVED;
            job.ApprovedById = disapprovedById; // Using ApprovedBy field for disapprover as specified
            job.ApprovedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // TODO: notification added here

            return job;
        }

        public async Task<IEnumerable<Job>> GetDisapprovedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.DISAPPROVED);
        }

        public async Task<IEnumerable<Job>> GetApprovedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.APPROVED);
        }

        public async Task<Job> FinishJobAsync(int jobId, int finishedById)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var job = await context.Jobs
                .Include(j => j.ChiefEngineer)
                .FirstOrDefaultAsync(j => j.Id == jobId);

            if (job == null)
                throw new ArgumentException($"Job with ID {jobId} not found");

            // Validate state transition
            if (job.Status != JobStatus.APPROVED)
                throw new InvalidOperationException($"Cannot finish job with status {job.Status}. Only APPROVED jobs can be finished.");

            // Validate finisher exists
            var finisher = await context.Workers.FindAsync(finishedById) ?? throw new ArgumentException($"Worker with ID {finishedById} not found");
            job.Status = JobStatus.FINISHED;
            job.FinishedById = finishedById;
            job.FinishedDate = DateTime.UtcNow;

            await context.SaveChangesAsync();

            return job;
        }

        public async Task<IEnumerable<Job>> GetFinishedJobsAsync()
        {
            return await GetJobsByStatusAsync(JobStatus.FINISHED);
        }

        public async Task<Job?> GetJobByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs.FirstOrDefaultAsync(j => j.Id == id);
        }

        public async Task<IEnumerable<Job>> GetAllJobsAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs.ToListAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByStatusAsync(JobStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Include(j => j.Hazards)
                    .ThenInclude(h => h.ControlMeasures)
                .Include(j => j.Documents)
                    .ThenInclude(d => d.FileMetadata)
                .Where(j => j.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Job>> GetJobsByChiefEngineerIdAsync(int chiefEngineerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Jobs
                .Include(j => j.Category)
                .Include(j => j.ChiefEngineer)
                .Where(j => j.ChiefEngineerId == chiefEngineerId)
                .ToListAsync();
        }

        public IEnumerable<MultipleValidationException> ValidateJob(Job job)
        {
            try
            {
                Job.SelfValidate(job);
                return [];
            }
            catch (MultipleValidationException ex)
            {
                return [ex];
            }
        }
        private ExcavationFields? generateExcavationFields(string? depthOfExcavation, List<string>? excavationProtectionSystems, List<string>? excavationEquipmentsToBeUsed)
        {
            if (depthOfExcavation == null && excavationProtectionSystems == null && excavationEquipmentsToBeUsed == null)
                return null;

            return new ExcavationFields
            {
                DepthOfExcavation = depthOfExcavation ?? string.Empty,
                ExcavationProtectionSystems = excavationProtectionSystems ?? [],
                ExcavationEquipmentsToBeUsed = excavationEquipmentsToBeUsed ?? []
            };
        }
        private HotWorkFields? generateHotWorkFields(List<string>? fireExtinguishers, List<string>? natureOfHotWorks)
        {
            if (fireExtinguishers == null && natureOfHotWorks == null)
                return null;

            return new HotWorkFields
            {
                FireExtinguishers = fireExtinguishers ?? [],
                NatureOfHotWorks = natureOfHotWorks ?? []
            };
        }
        private WorkAtHeightFields? generateWorkAtHeightFields(List<string>? modesOfAccessToBeUsed)
        {
            if (modesOfAccessToBeUsed == null)
                return null;

            return new WorkAtHeightFields
            {
                ModesOfAccessToBeUsed = modesOfAccessToBeUsed
            };
        }
    }
}
