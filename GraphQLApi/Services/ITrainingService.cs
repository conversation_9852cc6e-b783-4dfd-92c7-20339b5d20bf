using Shared.GraphQL.Models.Training;
using Shared.Enums;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Services
{
    public interface ITrainingService
    {
        // Session Management (following ToolboxService patterns)
        Task<TrainingSession> CreateSessionAsync(CreateTrainingSessionInput input);
        Task AddAttendeesAsync(int sessionId, IEnumerable<int> workerIds);
        Task UpdateSessionStatusAsync(int sessionId, TrainingSessionStatus status);
        Task FinalizeSessionAsync(int sessionId, IEnumerable<TrainingAttendanceInput> attendance);
        Task<TrainingSession?> GetSessionByIdAsync(int id);
        Task<IEnumerable<TrainingSession>> GetAllSessionsAsync();
        Task<IEnumerable<TrainingSession>> GetSessionsByDateAsync(DateTime date);
        Task<TrainingSession?> GetTodaysSessionAsync(int programId);
        
        // Program Management
        Task<IEnumerable<TrainingProgram>> GetActiveProgramsAsync();
        Task<TrainingProgram?> GetProgramByIdAsync(int id);
        
        // Provider Management
        Task<IEnumerable<TrainingProvider>> GetActiveProvidersAsync();
        Task<TrainingProvider?> GetProviderByIdAsync(int id);
        
        // Enrollment Management
        Task<TrainingEnrollment> EnrollWorkerAsync(int sessionId, int workerId);
        Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsAsync(int sessionId);
        Task UpdateEnrollmentStatusAsync(int enrollmentId, EnrollmentStatus status);
        
        // Batch Enrollment
        Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersByTradeAsync(int sessionId, int tradeId);
        Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersBySiteAsync(int sessionId, Guid siteId);
        
        // Certificate Management
        Task<TrainingCertificate> IssueCertificateAsync(IssueCertificateInput input);
        Task<IEnumerable<TrainingCertificate>> GetWorkerCertificatesAsync(int workerId);
        Task<IEnumerable<TrainingCertificate>> GetExpiringCertificatesAsync(int daysAhead = 30);
        Task UpdateCertificateStatusAsync(int certificateId, CertificateStatus status);
        Task EvaluateAndUpdateCertificateStatusesAsync();
        
        // Worker Eligibility
        Task<WorkerEligibilityStatus> CheckWorkerEligibilityAsync(int workerId, int tradeId);
        Task<IEnumerable<TrainingCertificate>> GetRequiredCertificatesForTradeAsync(int workerId, int tradeId);
        
        // Worker Certificate Status
        Task<GraphQLApi.GraphQL.Queries.WorkerCertificateStatusSummary> GetWorkerCertificateStatusAsync(int workerId);
        Task<Dictionary<int, GraphQLApi.GraphQL.Queries.WorkerCertificateStatusSummary>> GetWorkersCertificateStatusesAsync(IEnumerable<int> workerIds);
        
        // Attendance Management (simplified)
        Task MarkAttendanceAsync(int enrollmentId, bool attended, string? notes = null);
        Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsWithAttendanceAsync(int sessionId);
        
        // File Management (like toolbox file handling)
        Task ClearSessionTempFolderAsync(int sessionId);

        // Legacy Training Management (backward compatibility with existing mutations)
        Task<Shared.GraphQL.Models.LegacyTraining> CreateTrainingAsync(Shared.GraphQL.Models.LegacyTraining training);
        Task<Shared.GraphQL.Models.LegacyTraining?> GetTrainingByIdAsync(int id);
        Task<Shared.GraphQL.Models.LegacyTraining?> UpdateTrainingAsync(int id, Shared.GraphQL.Models.LegacyTraining updatedTraining);
        Task<bool> DeleteTrainingAsync(int id);
    }

    // Input classes for service methods (following ToolboxService patterns)
    public class CreateTrainingSessionInput
    {
        public int ProgramId { get; set; }
        public int ProviderId { get; set; }
        public int ConductorId { get; set; }
        public Guid? SiteId { get; set; }
        public TrainingMode Mode { get; set; }
        public string Location { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int Capacity { get; set; }
        public string? Notes { get; set; }
        public IFile? SessionPictureFile { get; set; }
    }

    public class TrainingAttendanceInput
    {
        public int WorkerId { get; set; }
        public TrainingOutcome Outcome { get; set; }
        public string? Notes { get; set; }
    }

    public class IssueCertificateInput
    {
        public int SessionId { get; set; }
        public int WorkerId { get; set; }
        public TrainingOutcome Outcome { get; set; }
        public string CertificateNo { get; set; } = string.Empty;
        public DateTime? CustomExpiryDate { get; set; } // Override program validity if needed
        public IFile? CertificateFile { get; set; }
        public string? Notes { get; set; }
    }

    public class TrainingAttendeeInput
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
    }
}