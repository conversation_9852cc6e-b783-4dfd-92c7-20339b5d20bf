using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IRelationshipService
    {
        System.Threading.Tasks.Task AssignTrainingsToWorkerAsync(int workerId, List<int> trainingIds);
        System.Threading.Tasks.Task AssignTradesToWorkerAsync(int workerId, List<int> tradeIds);
        System.Threading.Tasks.Task AssignSkillsToWorkerAsync(int workerId, List<int> skillIds);
        System.Threading.Tasks.Task AssignWorkersToTrainingAsync(int trainingId, List<int> workerIds);
        System.Threading.Tasks.Task AssignWorkersToTradeAsync(int tradeId, List<int> workerIds);
        System.Threading.Tasks.Task AssignWorkersToSkillAsync(int skillId, List<int> workerIds);
        // System.Threading.Tasks.Task AssignWorkersToTaskAsync(int taskId, List<int> workerIds);
        // System.Threading.Tasks.Task AssignEquipmentToTaskAsync(int taskId, List<int> equipmentIds);

        System.Threading.Tasks.Task UpdateWorkerRelationshipsAsync(int workerId, List<int>? trainingIds, List<int>? tradeIds, List<int>? skillIds);
        System.Threading.Tasks.Task UpdateTrainingRelationshipsAsync(int trainingId, List<int>? workerIds);
        System.Threading.Tasks.Task UpdateTradeRelationshipsAsync(int tradeId, List<int>? workerIds);
        System.Threading.Tasks.Task UpdateSkillRelationshipsAsync(int skillId, List<int>? workerIds);
        // System.Threading.Tasks.Task UpdateTaskRelationshipsAsync(int taskId, List<int>? workerIds, List<int>? equipmentIds);
    }
}
