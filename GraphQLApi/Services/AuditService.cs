using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using Shared.GraphQL.Models.Auth;
using Shared.Enums;

namespace GraphQLApi.Services
{
    public interface IAuditService
    {
        Task LogAsync(Shared.GraphQL.Models.Auth.AuditAction action, string details, int? userId = null, string? ipAddress = null, string? userAgent = null);
        Task LogLoginAttemptAsync(string email, bool success, string? ipAddress = null, string? userAgent = null, string? failureReason = null);
        Task LogPasswordChangeAsync(int userId, string? ipAddress = null, string? userAgent = null);
        Task LogAccountLockoutAsync(int userId, string reason, string? ipAddress = null);
        Task LogSessionEventAsync(int userId, string action, string? ipAddress = null, string? userAgent = null);
    }

    public class AuditService : IAuditService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ILogger<AuditService> _logger;

        public AuditService(IDbContextFactory<AppDbContext> contextFactory, ILogger<AuditService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        public async Task LogAsync(Shared.GraphQL.Models.Auth.AuditAction action, string details, int? userId = null, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                
                var auditLog = new UserAuditLog
                {
                    Action = action,
                    Description = details,
                    ApplicationUserId = userId, // Allow null for system actions that don't have a user
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    CreatedAt = DateTime.UtcNow
                };

                context.UserAuditLogs.Add(auditLog);
                await context.SaveChangesAsync();

                _logger.LogInformation("Audit log created: {Action} - {Details} for User {UserId}", 
                    action, details, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create audit log for action {Action}", action);
            }
        }

        public async Task LogLoginAttemptAsync(string email, bool success, string? ipAddress = null, string? userAgent = null, string? failureReason = null)
        {
            var action = success ? Shared.GraphQL.Models.Auth.AuditAction.Login : Shared.GraphQL.Models.Auth.AuditAction.FailedLogin;
            var details = success 
                ? $"Successful login for {email}"
                : $"Failed login attempt for {email}. Reason: {failureReason ?? "Invalid credentials"}";

            await LogAsync(action, details, null, ipAddress, userAgent);
        }

        public async Task LogPasswordChangeAsync(int userId, string? ipAddress = null, string? userAgent = null)
        {
            await LogAsync(Shared.GraphQL.Models.Auth.AuditAction.PasswordChanged,
                "User password changed", userId, ipAddress, userAgent);
        }

        public async Task LogAccountLockoutAsync(int userId, string reason, string? ipAddress = null)
        {
            await LogAsync(Shared.GraphQL.Models.Auth.AuditAction.AccountLocked,
                $"Account locked: {reason}", userId, ipAddress);
        }

        public async Task LogSessionEventAsync(int userId, string action, string? ipAddress = null, string? userAgent = null)
        {
            var auditAction = action.ToLowerInvariant() switch
            {
                "login" => Shared.GraphQL.Models.Auth.AuditAction.Login,
                "logout" => Shared.GraphQL.Models.Auth.AuditAction.Logout,
                "session_expired" => Shared.GraphQL.Models.Auth.AuditAction.SessionTerminated,
                "session_terminated" => Shared.GraphQL.Models.Auth.AuditAction.SessionTerminated,
                _ => Shared.GraphQL.Models.Auth.AuditAction.SecurityEvent
            };

            await LogAsync(auditAction, $"Session {action}", userId, ipAddress, userAgent);
        }
    }
}
