using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using GraphQLApi.Data;
using Shared.DTOs;
using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.Services
{
    public class RecipientResolutionService : IRecipientResolutionService
    {
        private readonly IDbContextFactory<AppDbContext> _dbContextFactory;
        private readonly ILogger<RecipientResolutionService> _logger;

        public RecipientResolutionService(
            IDbContextFactory<AppDbContext> dbContextFactory,
            ILogger<RecipientResolutionService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _logger = logger;
        }

        public async Task<List<NotificationRecipient>> ResolveRecipientsAsync(List<string> recipientRules, int tenantId)
        {
            var recipients = new List<NotificationRecipient>();

            foreach (var rule in recipientRules)
            {
                try
                {
                    var resolvedRecipients = await ResolveRecipientRuleAsync(rule, tenantId);
                    recipients.AddRange(resolvedRecipients);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to resolve recipient rule: {Rule}", rule);
                }
            }

            // Remove duplicates based on UserId
            return recipients
                .GroupBy(r => r.UserId)
                .Select(g => g.First())
                .ToList();
        }

        private async Task<List<NotificationRecipient>> ResolveRecipientRuleAsync(string rule, int tenantId)
        {
            if (string.IsNullOrWhiteSpace(rule))
                return new List<NotificationRecipient>();

            var parts = rule.Split(':', 2);
            if (parts.Length != 2)
            {
                _logger.LogWarning("Invalid recipient rule format: {Rule}. Expected format: 'type:value'", rule);
                return new List<NotificationRecipient>();
            }

            var type = parts[0].ToLowerInvariant();
            var value = parts[1];

            return type switch
            {
                "role" => await ResolveRecipientsByRoleAsync(value, tenantId),
                "user" => await ResolveRecipientsByUserEmailAsync(value, tenantId),
                "userid" when int.TryParse(value, out var userId) => await ResolveRecipientsByUserIdAsync(userId),
                "permission" => await ResolveRecipientsByPermissionRuleAsync(value, tenantId),
                _ => new List<NotificationRecipient>()
            };
        }

        public async Task<List<NotificationRecipient>> ResolveRecipientsByRoleAsync(string roleName, int tenantId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var users = await context.Users
                .Include(u => u.Role)
                .Where(u => u.TenantId == tenantId && 
                           u.Role.Name.ToLower() == roleName.ToLower() &&
                           u.Status == UserStatus.Active)
                .Select(u => new NotificationRecipient(
                    u.Id,
                    u.Email,
                    u.FirstName,
                    u.LastName,
                    null // Will be resolved from preferences later
                ))
                .ToListAsync();

            _logger.LogDebug("Resolved {Count} recipients for role '{Role}' in tenant {TenantId}", 
                users.Count, roleName, tenantId);

            return users;
        }

        public async Task<List<NotificationRecipient>> ResolveRecipientsByUserEmailAsync(string email, int tenantId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var user = await context.Users
                .Where(u => u.TenantId == tenantId && 
                           u.Email.ToLower() == email.ToLower() &&
                           u.Status == UserStatus.Active)
                .Select(u => new NotificationRecipient(
                    u.Id,
                    u.Email,
                    u.FirstName,
                    u.LastName,
                    null
                ))
                .FirstOrDefaultAsync();

            return user != null ? new List<NotificationRecipient> { user } : new List<NotificationRecipient>();
        }

        public async Task<List<NotificationRecipient>> ResolveRecipientsByUserIdAsync(int userId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var user = await context.Users
                .Where(u => u.Id == userId && u.Status == UserStatus.Active)
                .Select(u => new NotificationRecipient(
                    u.Id,
                    u.Email,
                    u.FirstName,
                    u.LastName,
                    null
                ))
                .FirstOrDefaultAsync();

            return user != null ? new List<NotificationRecipient> { user } : new List<NotificationRecipient>();
        }

        public async Task<List<NotificationRecipient>> ResolveRecipientsByPermissionAsync(string resource, string operation, string level, int tenantId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            // This is a simplified implementation - you might want to enhance this based on your permission system
            var users = await context.Users
                .Include(u => u.Role)
                .Where(u => u.TenantId == tenantId && u.Status == UserStatus.Active)
                .ToListAsync();

            var recipients = new List<NotificationRecipient>();

            foreach (var user in users)
            {
                // Check if user has the required permission
                // This is a placeholder - implement based on your permission checking logic
                if (HasPermission(user.Role, resource, operation, level))
                {
                    recipients.Add(new NotificationRecipient(
                        user.Id,
                        user.Email,
                        user.FirstName,
                        user.LastName,
                        null
                    ));
                }
            }

            return recipients;
        }

        private async Task<List<NotificationRecipient>> ResolveRecipientsByPermissionRuleAsync(string permissionRule, int tenantId)
        {
            // Expected format: "resource.operation.level" e.g., "Workers.Read.Site"
            var parts = permissionRule.Split('.');
            if (parts.Length != 3)
            {
                _logger.LogWarning("Invalid permission rule format: {Rule}. Expected format: 'resource.operation.level'", permissionRule);
                return new List<NotificationRecipient>();
            }

            return await ResolveRecipientsByPermissionAsync(parts[0], parts[1], parts[2], tenantId);
        }

        private bool HasPermission(Role role, string resource, string operation, string level)
        {
            // This is a simplified implementation
            // You should implement this based on your actual permission system
            // For now, we'll just check if the role is Admin or has specific permissions
            
            if (role.Name.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                return true;

            // Add more sophisticated permission checking here based on your permission flags
            // This is just a placeholder implementation
            return false;
        }
    }
}
