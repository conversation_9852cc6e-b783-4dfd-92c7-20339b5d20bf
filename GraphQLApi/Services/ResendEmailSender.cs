using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Configuration;

namespace GraphQLApi.Services
{
    public class ResendEmailSender : IEmailSender
    {
        private readonly HttpClient _httpClient;
        private readonly ResendOptions _options;
        private readonly ILogger<ResendEmailSender> _logger;

        public ResendEmailSender(IOptions<ResendOptions> options, ILogger<ResendEmailSender> logger)
        {
            _options = options.Value;
            _logger = logger;
            _httpClient = new HttpClient
            {
                BaseAddress = new System.Uri(_options.BaseUrl)
            };
            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _options.ApiKey);
        }

        public async Task SendAsync(string to, string subject, string htmlBody, CancellationToken ct)
        {
            var payload = new
            {
                from = _options.FromEmail,
                to = new[] { to },
                subject = subject,
                html = htmlBody
            };

            var response = await _httpClient.PostAsJsonAsync("/emails", payload, cancellationToken: ct);
            if (!response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(ct);
                _logger.LogError("Resend email failed: {Status} {Content}", (int)response.StatusCode, content);
            }
        }
    }
}

