using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace GraphQLApi.Services
{
    public class LogEmailSender : IEmailSender
    {
        private readonly ILogger<LogEmailSender> _logger;
        public LogEmailSender(ILogger<LogEmailSender> logger) { _logger = logger; }

        public Task SendAsync(string to, string subject, string htmlBody, CancellationToken ct)
        {
            _logger.LogInformation("[Email-LOG] To={To} Subject={Subject} Body={Body}", to, subject, htmlBody);
            return Task.CompletedTask;
        }
    }
}

