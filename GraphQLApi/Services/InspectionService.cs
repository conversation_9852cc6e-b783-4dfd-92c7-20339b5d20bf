using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.GraphQL.InputTypes;
using Shared.GraphQL.Models;
using GraphQLApi.Data;

namespace GraphQLApi.Services
{
    /// <summary>
    /// Service for managing inspections
    /// </summary>
    public class InspectionService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly ILogger<InspectionService> _logger;

        public InspectionService(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            ILogger<InspectionService> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new inspection with the specified items and inspector signature
        /// </summary>
        /// <param name="input">The inspection input data</param>
        /// <returns>The created inspection</returns>
        public async Task<Inspection> CreateInspectionAsync(CreateInspectionInput input)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                // Validate that the inspector exists
                var inspector = await context.Workers
                    .Include(w => w.SignatureFile)
                    .FirstOrDefaultAsync(w => w.Id == input.InspectedById) ?? throw new ArgumentException($"Worker with ID {input.InspectedById} not found");

                // Create the inspection

                var inspection = new Inspection
                {
                    Approved = input.Approved,
                    Comments = input.Comments ?? "",
                    InspectionItems = input.InspectionItems,
                    InspectionType = input.InspectionType,
                    InspectedById = input.InspectedById,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                };

                // Copy inspector's signature to inspections bucket if available
                if (inspector.SignatureFileId.HasValue)
                {
                    var signatureFileId = await CopySignatureToInspectionsBucketAsync(
                        inspector.SignatureFileId.Value,
                        inspection.Id);

                    if (!string.IsNullOrEmpty(signatureFileId))
                    {
                        inspection.SignatureFileId = int.Parse(signatureFileId);
                    }
                }

                // Add inspection to context first to get the ID
                context.Inspections.Add(inspection);
                await context.SaveChangesAsync();

                _logger.LogInformation("Successfully created inspection {InspectionId} by worker {InspectorId}",
                    inspection.Id, input.InspectedById);

                return inspection;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create inspection for worker {InspectorId}", input.InspectedById);
                throw;
            }
        }

        /// <summary>
        /// Copies the inspector's signature to the inspections bucket
        /// </summary>
        private async Task<string> CopySignatureToInspectionsBucketAsync(int sourceFileId, int inspectionId)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var sourceFile = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == sourceFileId);

                if (sourceFile == null)
                    throw new ArgumentException($"Source file with ID {sourceFileId} not found");

                // Create folder path for this inspection
                var folderPath = $"inspection_{inspectionId}/signatures";

                // Download the source file from MinIO
                using var sourceStream = await _minioService.DownloadFileAsync(sourceFile);

                // Generate unique filename for the copy
                var fileName = $"{System.IO.Path.GetFileNameWithoutExtension(sourceFile.FileName)}_{Guid.NewGuid()}.{System.IO.Path.GetExtension(sourceFile.FileName)}";

                // Upload to inspections bucket with folder path
                var copiedFileMetadata = await _minioService.UploadFileAsync(
                    sourceStream,
                    fileName,
                    Shared.Constants.FileStorageConstants.BucketNames.INSPECTIONS,
                    sourceFile.ContentType,
                    $"Inspector signature for inspection {inspectionId}",
                    folderPath,
                    false, // Not public
                    null // No expiration for inspection signatures
                );

                _logger.LogInformation("Successfully copied signature file {SourceFileId} to inspections bucket as {CopiedFileId}",
                    sourceFileId, copiedFileMetadata.Id);

                return copiedFileMetadata.Id.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy signature file {FileId} to inspections bucket", sourceFileId);
                return string.Empty; // Return empty string instead of throwing to allow inspection creation to continue
            }
        }

        /// <summary>
        /// Processes and copies image files to the inspections bucket for an inspection item
        /// </summary>
        private async Task ProcessInspectionItemImagesAsync(AppDbContext context, int inspectionItemId, int[] imageFileIds, int inspectionId)
        {
            try
            {
                var inspectionItem = await context.InspectionItems
                    .Include(ii => ii.ImageFiles)
                    .FirstOrDefaultAsync(ii => ii.Id == inspectionItemId);

                if (inspectionItem == null)
                    return;

                foreach (var imageFileId in imageFileIds)
                {
                    var sourceFile = await context.FileMetadata
                        .FirstOrDefaultAsync(f => f.Id == imageFileId);

                    if (sourceFile == null)
                        continue;

                    // Create folder path for this inspection's images
                    var folderPath = $"inspection_{inspectionId}/images";

                    // Download the source file from MinIO
                    using var sourceStream = await _minioService.DownloadFileAsync(sourceFile);

                    // Generate unique filename for the copy
                    var fileName = $"{System.IO.Path.GetFileNameWithoutExtension(sourceFile.FileName)}_{Guid.NewGuid()}{System.IO.Path.GetExtension(sourceFile.FileName)}";

                    // Upload to inspections bucket with folder path
                    var copiedFileMetadata = await _minioService.UploadFileAsync(
                        sourceStream,
                        fileName,
                        Shared.Constants.FileStorageConstants.BucketNames.INSPECTIONS,
                        sourceFile.ContentType,
                        $"Image for inspection item {inspectionItemId}",
                        folderPath,
                        false, // Not public
                        null // No expiration for inspection images
                    );

                    // Add the copied file to the inspection item's image files
                    inspectionItem.ImageFiles.Add(copiedFileMetadata);
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process images for inspection item {InspectionItemId}", inspectionItemId);
                throw;
            }
        }

        /// <summary>
        /// Gets an inspection by ID with all related data
        /// </summary>
        public async Task<Inspection?> GetInspectionByIdAsync(int inspectionId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            return await context.Inspections
                .Include(i => i.InspectionItems)
                    .ThenInclude(ii => ii.ImageFiles)
                .Include(i => i.SignatureFile)
                .Include(i => i.InspectedBy)
                .FirstOrDefaultAsync(i => i.Id == inspectionId);
        }

        /// <summary>
        /// Gets all inspections with pagination
        /// </summary>
        public async Task<IEnumerable<Inspection>> GetInspectionsAsync(int skip = 0, int take = 50)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            return await context.Inspections
                .Include(i => i.InspectionItems)
                    .ThenInclude(ii => ii.ImageFiles)
                .Include(i => i.SignatureFile)
                .Include(i => i.InspectedBy)
                .OrderByDescending(i => i.CreatedAt)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }

        /// <summary>
        /// Gets inspections by inspector ID
        /// </summary>
        public async Task<IEnumerable<Inspection>> GetInspectionsByInspectorAsync(int inspectorId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            return await context.Inspections
                .Include(i => i.InspectionItems)
                    .ThenInclude(ii => ii.ImageFiles)
                .Include(i => i.SignatureFile)
                .Include(i => i.InspectedBy)
                .Where(i => i.InspectedById == inspectorId)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }
    }
}
