using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;

namespace GraphQLApi.Services
{
    public class PhotoService : IPhotoService
    {
        private readonly IHikvisionService _hikvisionService;
        private readonly IMinioService _minioService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<PhotoService> _logger;
        private readonly string _localStoragePath;

        public PhotoService(
            IHikvisionService hikvisionService,
            IMinioService minioService,
            IDbContextFactory<AppDbContext> contextFactory,
            IConfiguration configuration,
            ILogger<PhotoService> logger)
        {
            _hikvisionService = hikvisionService;
            _minioService = minioService;
            _contextFactory = contextFactory;
            _configuration = configuration;
            _logger = logger;
            _localStoragePath = _configuration["PhotoStorage:LocalPath"] ?? "wwwroot/photos";
            
            // Ensure storage directory exists
            if (!Directory.Exists(_localStoragePath))
            {
                Directory.CreateDirectory(_localStoragePath);
            }
        }

        public async Task<string> UploadPhotoAsync(Stream photoStream, string fileName, PhotoStorageType storageType)
        {
            try
            {
                switch (storageType)
                {
                    case PhotoStorageType.Local:
                        return await SaveLocalPhotoAsync(photoStream, fileName);
                    case PhotoStorageType.Hikvision:
                        return await _hikvisionService.UploadWorkerPhotoAsync(fileName, photoStream);
                    case PhotoStorageType.MinIO:
                        return await SaveMinIOPhotoAsync(photoStream, fileName);
                    default:
                        throw new ArgumentException("Invalid storage type", nameof(storageType));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo {FileName}", fileName);
                throw;
            }
        }

        public async Task<Stream> GetPhotoAsync(string photoUrl)
        {
            try
            {
                if (IsLocalPhoto(photoUrl))
                {
                    var path = GetLocalPhotoPath(photoUrl);
                    return new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.Read, 4096, FileOptions.Asynchronous | FileOptions.SequentialScan);
                }
                else if (IsMinIOPhoto(photoUrl))
                {
                    var fileId = ExtractFileIdFromMinIOUrl(photoUrl);
                    return await GetMinIOPhotoAsync(fileId);
                }
                else
                {
                    var workerId = ExtractWorkerIdFromHikvisionUrl(photoUrl);
                    return await _hikvisionService.GetWorkerPhotoAsync(workerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photo {PhotoUrl}", photoUrl);
                throw;
            }
        }

        public async Task DeletePhotoAsync(string photoUrl)
        {
            try
            {
                if (IsLocalPhoto(photoUrl))
                {
                    var path = GetLocalPhotoPath(photoUrl);
                    if (File.Exists(path))
                    {
                        File.Delete(path);
                    }
                }
                else if (IsMinIOPhoto(photoUrl))
                {
                    var fileId = ExtractFileIdFromMinIOUrl(photoUrl);
                    await DeleteMinIOPhotoAsync(fileId);
                }
                else
                {
                    var workerId = ExtractWorkerIdFromHikvisionUrl(photoUrl);
                    await _hikvisionService.DeleteWorkerPhotoAsync(workerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo {PhotoUrl}", photoUrl);
                throw;
            }
        }

        public async Task<string> TransferPhotoToHikvisionAsync(string sourcePhotoUrl, string workerId)
        {
            try
            {
                using var photoStream = await GetPhotoAsync(sourcePhotoUrl);
                var hikvisionUrl = await _hikvisionService.UploadWorkerPhotoAsync(workerId, photoStream);
                
                // Delete the local photo if it exists
                if (IsLocalPhoto(sourcePhotoUrl))
                {
                    await DeletePhotoAsync(sourcePhotoUrl);
                }

                return hikvisionUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transferring photo to Hikvision for worker {WorkerId}", workerId);
                throw;
            }
        }

        private async Task<string> SaveLocalPhotoAsync(Stream photoStream, string fileName)
        {
            var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
            var filePath = System.IO.Path.Combine(_localStoragePath, uniqueFileName);
            
            using var fileStream = File.Create(filePath);
            await photoStream.CopyToAsync(fileStream);
            
            return $"/photos/{uniqueFileName}";
        }

        private bool IsLocalPhoto(string photoUrl)
        {
            return photoUrl.StartsWith("/photos/");
        }

        private string GetLocalPhotoPath(string photoUrl)
        {
            var fileName = System.IO.Path.GetFileName(photoUrl);
            return System.IO.Path.Combine(_localStoragePath, fileName);
        }

        private string ExtractWorkerIdFromHikvisionUrl(string photoUrl)
        {
            //@TODO: Implement logic to extract worker ID from Hikvision URL
            // This will depend Hikvision URL format
            return photoUrl.Split('/').Last();
        }

        private async Task<string> SaveMinIOPhotoAsync(Stream photoStream, string fileName)
        {
            try
            {
                var fileMetadata = await _minioService.UploadFileAsync(
                    photoStream,
                    fileName,
                    Shared.Constants.FileStorageConstants.BucketNames.PROFILE_PICTURE,
                    "image/jpeg", // Default to JPEG for photos
                    "Worker profile picture",
                    null, // No folder path
                    false, // Not public by default
                    null); // No expiration

                // Return a URL format that can be used to identify MinIO files
                return $"/minio/{fileMetadata.Id}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo to MinIO: {FileName}", fileName);
                throw;
            }
        }

        private bool IsMinIOPhoto(string photoUrl)
        {
            return photoUrl.StartsWith("/minio/");
        }

        private int ExtractFileIdFromMinIOUrl(string photoUrl)
        {
            // Extract file ID from URL format: /minio/{fileId}
            var parts = photoUrl.Split('/');
            if (parts.Length >= 3 && int.TryParse(parts[2], out int fileId))
            {
                return fileId;
            }
            throw new ArgumentException($"Invalid MinIO photo URL format: {photoUrl}");
        }

        private async Task<Stream> GetMinIOPhotoAsync(int fileId)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

                if (fileMetadata == null)
                {
                    throw new FileNotFoundException($"File with ID {fileId} not found");
                }

                return await _minioService.DownloadFileAsync(fileMetadata);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving photo from MinIO: {FileId}", fileId);
                throw;
            }
        }

        private async Task DeleteMinIOPhotoAsync(int fileId)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

                if (fileMetadata != null)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo from MinIO: {FileId}", fileId);
                throw;
            }
        }
    }
}