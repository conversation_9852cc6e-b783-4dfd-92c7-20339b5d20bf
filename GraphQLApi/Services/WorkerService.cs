using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using Shared.GraphQL.Models;
using HotChocolate;
using GraphQLApi.GraphQL.Types;

namespace GraphQLApi.Services
{
    public class WorkerService : IWorkerService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IPhotoService _photoService;
        private readonly ILogger<WorkerService> _logger;
        private readonly IMinioService _minioService;
        private readonly IWorkerTrainingService _workerTrainingService;


        public WorkerService(
            IDbContextFactory<AppDbContext> contextFactory,
            IPhotoService photoService,
            IMinioService minioService,
            IWorkerTrainingService workerTrainingService,
            ILogger<WorkerService> logger)
        {
            _contextFactory = contextFactory;
            _photoService = photoService;
            _minioService = minioService;
            _workerTrainingService = workerTrainingService;
            _logger = logger;
        }



        public async Task<IEnumerable<Worker>> GetAllWorkersAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Workers.ToListAsync();
        }

        public async Task<Worker?> GetWorkerByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Workers.FirstOrDefaultAsync(w => w.Id == id);
        }

        public async Task<Worker> CreateWorkerAsync(Worker worker)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var existingWorker = await context.Workers
                .FirstOrDefaultAsync(w => w.NationalId == worker.NationalId);

            if (existingWorker != null)
            {
                throw new GraphQLException(new Error(
                    "Validation",
                    $"A worker with National ID '{worker.NationalId}' already exists.")
                );
            }

            context.Workers.Add(worker);
            await context.SaveChangesAsync();
            return worker;
        }
        public async Task<Worker> CreateCompleteWorkerAsync(CreateWorkerWithTrainingInput input)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            // await using var transaction = await context.Database.BeginTransactionAsync();
            int? workerId = null;
            List<FileMetadata> fileMetadataList = new List<FileMetadata>();
            List<WorkerTraining> workerTrainings = new List<WorkerTraining>();
            try
            {
                var worker = new Worker
                {
                    Name = input.Name,
                    Company = input.Company,
                    NationalId = input.NationalId,
                    Gender = input.Gender,
                    DateOfBirth = input.DateOfBirth,
                    ManHours = 0,
                    Rating = 0,
                    PhoneNumber = input.PhoneNumber,
                    MpesaNumber = input.MpesaNumber,
                    Email = input.Email,
                    InductionDate = input.InductionDate,
                    MedicalCheckDate = input.MedicalCheckDate
                };

                if (input.TradeIds != null && input.TradeIds.Count != 0)
                {
                    var trades = await context.Trades
                        .Where(t => input.TradeIds.Contains(t.Id))
                        .ToListAsync();
                    foreach (var trade in trades)
                    {
                        if (!worker.Trades.Contains(trade))
                            worker.Trades.Add(trade);
                    }
                }

                if (input.SkillIds != null && input.SkillIds.Count != 0)
                {
                    var skills = await context.Skills
                        .Where(s => input.SkillIds.Contains(s.Id))
                        .ToListAsync();
                    foreach (var skill in skills)
                    {
                        if (!worker.Skills.Contains(skill))
                            worker.Skills.Add(skill);
                    }
                }

                context.Workers.Add(worker);
                await context.SaveChangesAsync();
                if (worker.Id == 0)
                {
                    throw new GraphQLException(new Error(
                        "Validation",
                        "Failed to create worker")
                    );
                }
                workerId = worker.Id;

                // Handle profile picture upload
                if (input.ProfilePicture != null)
                {
                    using var profileStream = input.ProfilePicture.OpenReadStream();
                    var profileMetadata = await _minioService.UploadFileAsync(
                        profileStream,
                        // input.ProfilePicture.Name,
                        $"profile-picture_{worker.Id}.{GetFileExtensionFromName(input.ProfilePicture.Name)}",
                        "profile-picture",
                        input.ProfilePicture.ContentType ?? "image/jpeg",
                        "Worker profile picture",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (profileMetadata != null)
                    {
                        worker.ProfilePictureFileId = profileMetadata.Id;
                        fileMetadataList.Add(profileMetadata);
                        context.Workers.Update(worker);
                        // await context.SaveChangesAsync();
                    }
                    else
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "Failed to upload profile picture")
                        );
                    }
                }
                // Handle signature upload
                if (input.Signature != null)
                {
                    using var signatureStream = input.Signature.OpenReadStream();
                    var signatureMetadata = await _minioService.UploadFileAsync(
                        signatureStream,
                        // input.Signature.Name,
                        $"signature_{worker.Id}.{GetFileExtensionFromName(input.Signature.Name)}",
                        "signatures",
                        input.Signature.ContentType ?? "image/png",
                        "Worker signature",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (signatureMetadata != null)
                    {
                        worker.SignatureFileId = signatureMetadata.Id;
                        fileMetadataList.Add(signatureMetadata);
                        // worker.SignatureFile = signatureMetadata;
                        context.Workers.Update(worker);
                        // await context.SaveChangesAsync();
                    }
                    else
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "Failed to upload signature")
                        );
                    }
                }
                // Handle document uploads
                if (input.Documents != null && input.Documents.Count != 0)
                {
                    foreach (var doc in input.Documents)
                    {
                        using var docStream = doc.File.OpenReadStream();
                        var docMetadata = await _minioService.UploadFileAsync(
                            docStream,
                            doc.File.Name,
                            "docs",
                            doc.File.ContentType ?? "application/pdf",
                            doc.Description,
                            $"worker-{worker.Id}",
                            doc.IsPublic,
                            doc.ExpiresAt);

                        if (docMetadata != null)
                        {
                            var documentFile = new DocumentFile
                            {
                                Name = doc.Name,
                                FileMetadataId = docMetadata.Id,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System"
                            };
                            context.DocumentFiles.Add(documentFile);
                            worker.DocumentFiles.Add(documentFile);
                            fileMetadataList.Add(docMetadata);
                            // await context.SaveChangesAsync();
                        }
                        else
                        {
                            throw new GraphQLException(new Error(
                                "Validation",
                                "Failed to upload document")
                            );
                        }
                    }
                }

                // Handle training assignments with documents
                if (input.Trainings != null && input.Trainings.Count != 0)
                {
                    var trainings = await context.Trainings
                        .Where(t => input.Trainings.Select(ti => ti.TrainingId).Contains(t.Id))
                        .ToListAsync();
                    if (trainings.Count != input.Trainings.Count)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "One or more trainings not found")
                        );
                    }

                    foreach (var trainingInput in input.Trainings)
                    {
                        // Create worker-training relationship
                        var workerTraining = await _workerTrainingService.CreateWorkerTrainingAsync(
                            worker.Id,
                            trainingInput.TrainingId,
                            trainingInput.Notes);
                        workerTrainings.Add(workerTraining);
                        // Handle training-specific documents
                        if (trainingInput.Documents != null && trainingInput.Documents.Count != 0)
                        {
                            foreach (var doc in trainingInput.Documents)
                            {
                                using var docStream = doc.File.OpenReadStream();
                                var docMetadata = await _minioService.UploadFileAsync(
                                    docStream,
                                    doc.File.Name,
                                    "docs",
                                    doc.File.ContentType ?? "application/pdf",
                                    doc.Description,
                                    $"worker-{worker.Id}/training-{trainingInput.TrainingId}",
                                    doc.IsPublic,
                                    doc.ExpiresAt);

                                if (docMetadata != null)
                                {
                                    var documentFile = new DocumentFile
                                    {
                                        Name = doc.Name,
                                        FileMetadataId = docMetadata.Id,
                                        CreatedAt = DateTime.UtcNow,
                                        CreatedBy = "System"
                                    };
                                    context.DocumentFiles.Add(documentFile);
                                    workerTraining.DocumentFiles.Add(documentFile);
                                    fileMetadataList.Add(docMetadata);
                                    // await context.SaveChangesAsync();
                                }
                                else
                                {
                                    throw new GraphQLException(new Error(
                                        "Validation",
                                        "Failed to upload document")
                                    );
                                }
                            }
                        }
                    }
                }

                await context.SaveChangesAsync();
                // await transaction.CommitAsync();
                return worker;
            }
            catch (Exception ex)
            {
                // await transaction.RollbackAsync();
                _logger.LogError("Error creating worker: {ErrorMessage}", ex.Message);
                // Clean up any uploaded files
                foreach (var fileMetadata in fileMetadataList)
                {
                    await _minioService.DeleteFileAsync(fileMetadata.BucketName, fileMetadata.ObjectKey);
                }
                // Clean up any created worker-training relationships
                foreach (var workerTraining in workerTrainings)
                {
                    await _workerTrainingService.RemoveWorkerTrainingAsync(workerTraining.WorkerId, workerTraining.TrainingId);
                }
                // Clean up any partially created worker data
                if (workerId != null)
                {
                    await DeleteWorkerAsync(workerId.Value);
                }
                throw;
            }

        }

        public async Task<Worker?> UpdateWorkerAsync(int id, Worker updatedWorker)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FirstOrDefaultAsync(w => w.Id == id);

            if (worker == null)
                return null;

            // Update properties
            worker.Name = updatedWorker.Name;
            worker.Company = updatedWorker.Company;
            worker.DateOfBirth = updatedWorker.DateOfBirth;
            worker.ManHours = updatedWorker.ManHours;
            worker.Rating = updatedWorker.Rating;
            worker.Gender = updatedWorker.Gender;
            worker.PhoneNumber = updatedWorker.PhoneNumber;
            worker.Email = updatedWorker.Email;
            worker.InductionDate = updatedWorker.InductionDate;
            worker.MedicalCheckDate = updatedWorker.MedicalCheckDate;
            worker.MpesaNumber = updatedWorker.MpesaNumber;

            await context.SaveChangesAsync();
            return worker;
        }

        public async Task<bool> DeleteWorkerAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var worker = await context.Workers.FindAsync(id);
            if (worker == null)
                return false;

            try
            {
                // Note: File cleanup for profile pictures and signatures is handled by the MinIO service
                // when the worker is deleted, the associated file metadata will be cleaned up separately

                context.Workers.Remove(worker);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting worker with ID {WorkerId}", id);
                throw;
            }
        }
        private static string GetFileExtensionFromName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return string.Empty;

            int lastDotIndex = fileName.LastIndexOf('.');

            // No dot found or dot is the first character (e.g., ".bashrc")
            if (lastDotIndex <= 0 || lastDotIndex == fileName.Length - 1)
                return string.Empty;

            return fileName.Substring(lastDotIndex + 1);
        }
        // Note: Photo upload functionality has been replaced by the file upload system
        // in the GraphQL mutations. Profile pictures are now handled through the
        // ProfilePictureFile relationship and MinIO storage.
    }
}