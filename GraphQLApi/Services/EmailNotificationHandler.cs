using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Configuration;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public class EmailNotificationHandler : INotificationChannelHandler
    {
        private readonly IEmailSender _emailSender;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly NotificationOptions _options;
        private readonly ILogger<EmailNotificationHandler> _logger;

        public EmailNotificationHandler(
            IEmailSender emailSender,
            IServiceScopeFactory serviceScopeFactory,
            IOptions<NotificationOptions> options,
            ILogger<EmailNotificationHandler> logger)
        {
            _emailSender = emailSender;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
            _options = options.Value;
        }

        public bool CanHandle(NotificationChannel channel) => channel == NotificationChannel.Email && _options.EmailEnabled;

        public async Task HandleAsync(NotificationEvent evt, CancellationToken ct)
        {
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var recipientResolutionService = scope.ServiceProvider.GetRequiredService<IRecipientResolutionService>();
                var managementService = scope.ServiceProvider.GetRequiredService<NotificationManagementService>();

                List<NotificationRecipient> recipients;

                if (evt.Recipients?.Any() == true)
                {
                    // Resolve recipients from the event
                    recipients = await recipientResolutionService.ResolveRecipientsAsync(evt.Recipients, GetTenantIdFromEvent(evt));
                }
                else
                {
                    // Fallback to admin email for backward compatibility
                    recipients = new List<NotificationRecipient>
                    {
                        new NotificationRecipient(0, "<EMAIL>", "Admin", "User", new List<NotificationChannel> { NotificationChannel.Email })
                    };
                }

                foreach (var recipient in recipients)
                {
                    // Check if user has email notifications enabled
                    if (recipient.UserId > 0)
                    {
                        var preferences = await managementService.GetUserPreferencesAsync(recipient.UserId, evt.Type);
                        if (preferences?.EmailEnabled == false)
                        {
                            _logger.LogDebug("Skipping email notification for user {UserId} - email disabled in preferences", recipient.UserId);
                            continue;
                        }
                    }

                    try
                    {
                        await _emailSender.SendAsync(recipient.Email, evt.Title, evt.Message, ct);
                        _logger.LogInformation("Email notification sent to {Email} for event {Type}", recipient.Email, evt.Type);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send email notification to {Email} for event {Type}", recipient.Email, evt.Type);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process email notification for event {Type}", evt.Type);
            }
        }

        private int GetTenantIdFromEvent(NotificationEvent evt)
        {
            // Try to extract tenant ID from metadata
            if (evt.Metadata?.TryGetValue("tenantId", out var tenantIdStr) == true &&
                int.TryParse(tenantIdStr, out var tenantId))
            {
                return tenantId;
            }

            // Default to tenant 1 if not specified
            return 1;
        }
    }
}

