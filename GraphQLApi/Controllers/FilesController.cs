using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using GraphQLApi.Services;

namespace GraphQLApi.Controllers
{
    /// <summary>
    /// Controller for serving files stored in MinIO
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class FilesController : ControllerBase
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IMinioService _minioService;
        private readonly ILogger<FilesController> _logger;

        public FilesController(
            IDbContextFactory<AppDbContext> contextFactory,
            IMinioService minioService,
            ILogger<FilesController> logger)
        {
            _contextFactory = contextFactory;
            _minioService = minioService;
            _logger = logger;
        }

        /// <summary>
        /// Serves a file by its FileMetadata ID
        /// </summary>
        /// <param name="id">The FileMetadata ID</param>
        /// <returns>The file content</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetFile(int id)
        {
            Console.WriteLine("Running GetFile");
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();

                var fileMetadata = await context.FileMetadata
                    .Where(f => !f.IsDeleted)
                    .FirstOrDefaultAsync(f => f.Id == id);

                if (fileMetadata == null)
                {
                    _logger.LogWarning("File metadata with ID {FileId} not found", id);
                    return NotFound($"File with ID {id} not found");
                }

                // Check if file is expired
                if (fileMetadata.IsExpired())
                {
                    _logger.LogWarning("File with ID {FileId} has expired", id);
                    Response.Headers["X-File-Expired"] = "true";
                    Response.Headers["X-File-Expired-Warning"] = "This file is expired.";
                }

                // Get the file from MinIO
                var fileStream = await _minioService.DownloadFileAsync(
                    fileMetadata.BucketName,
                    fileMetadata.ObjectKey);

                if (fileStream == null)
                {
                    _logger.LogError("File content not found in MinIO for file ID {FileId}", id);
                    return NotFound($"File content not found for ID {id}");
                }

                // Set appropriate headers
                Response.Headers["Content-Disposition"] =
                    $"inline; filename=\"{fileMetadata.FileName}\"";

                return File(fileStream, fileMetadata.ContentType, fileMetadata.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serving file with ID {FileId}", id);
                return StatusCode(500, "An error occurred while retrieving the file");
            }
        }

        /// <summary>
        /// Downloads a file by its FileMetadata ID (forces download instead of inline display)
        /// </summary>
        /// <param name="id">The FileMetadata ID</param>
        /// <returns>The file content as download</returns>
        [HttpGet("{id:int}/download")]
        public async Task<IActionResult> DownloadFile(int id)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                
                var fileMetadata = await context.FileMetadata
                    .Where(f => !f.IsDeleted)
                    .FirstOrDefaultAsync(f => f.Id == id);

                if (fileMetadata == null)
                {
                    _logger.LogWarning("File metadata with ID {FileId} not found for download", id);
                    return NotFound($"File with ID {id} not found");
                }

                // Check if file is expired
                if (fileMetadata.IsExpired())
                {
                    _logger.LogWarning("File with ID {FileId} has expired for download", id);
                    Response.Headers["X-File-Expired"] = "true";
                    Response.Headers["X-File-Expired-Warning"] = "This file is expired.";
                }

                // Get the file from MinIO
                var fileStream = await _minioService.DownloadFileAsync(
                    fileMetadata.BucketName,
                    fileMetadata.ObjectKey);

                if (fileStream == null)
                {
                    _logger.LogError("File content not found in MinIO for download of file ID {FileId}", id);
                    return NotFound($"File content not found for ID {id}");
                }

                // Set download headers
                Response.Headers["Content-Disposition"] =
                    $"attachment; filename=\"{fileMetadata.FileName}\"";

                return File(fileStream, fileMetadata.ContentType, fileMetadata.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file with ID {FileId}", id);
                return StatusCode(500, "An error occurred while downloading the file");
            }
        }
    }
}
