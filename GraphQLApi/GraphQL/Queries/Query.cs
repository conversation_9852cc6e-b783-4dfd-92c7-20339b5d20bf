﻿using GraphQLApi.Services;
using GraphQLApi.Data;
using GraphQLApi.Auth.Authorization;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using Shared.Enums;
// using Task = Shared.GraphQL.Models.Task;
using HotChocolate.Data;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;

namespace GraphQLApi.GraphQL.Queries
{
    public class Query
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        // private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;
        private readonly IJobService _jobService;
        private readonly IToolboxService _toolboxService;
        private readonly IPermitService _permitService;
        private readonly InspectionService _inspectionService;
        private readonly ISiteService _siteService;

        public Query(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            // ITaskService taskService,
            IEquipmentService equipmentService,
            ISiteService siteService,
            IJobService jobService,
            IToolboxService toolboxService,
            IPermitService permitService,
            InspectionService inspectionService)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            // _taskService = taskService;
            _equipmentService = equipmentService;
            _jobService = jobService;
            _toolboxService = toolboxService;
            _permitService = permitService;
            _inspectionService = inspectionService;
            _siteService = siteService;
        }

        [UseProjection]
        [UseFiltering]
        [AuthorizePermission("Workers", "Read", "Site")]
        public IQueryable<Worker> GetAllWorkers([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Workers;
        }

        [UseProjection]
        [AuthorizePermission("Workers", "Read", "Site")]
        public IQueryable<Worker> GetWorkerById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            var w = context.Workers.Where(w => w.Id == id);
            return w;
        }

        [UseProjection]
        [UseFiltering]
        [AuthorizePermission("Trainings", "Read", "Site")]
        public IQueryable<LegacyTraining> GetAllTrainings([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Trainings;
        }

        [UseProjection]
        [AuthorizePermission("Trainings", "Read", "Site")]
        public IQueryable<LegacyTraining> GetTrainingById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Trainings.Where(t => t.Id == id);
        }

        [AuthorizePermission("Workers", "Read", "Site")]
        public async Task<IEnumerable<Trade>> GetAllTrades()
        {
            return await _tradeService.GetAllTradesAsync();
        }

        [AuthorizePermission("Workers", "Read", "Site")]
        public async Task<Trade?> GetTradeById(int id)
        {
            return await _tradeService.GetTradeByIdAsync(id);
        }

        [AuthorizePermission("Workers", "Read", "Site")]
        public async Task<IEnumerable<Skill>> GetAllSkills()
        {
            return await _skillService.GetAllSkillsAsync();
        }

        [AuthorizePermission("Workers", "Read", "Site")]
        public async Task<Skill?> GetSkillById(int id)
        {
            return await _skillService.GetSkillByIdAsync(id);
        }

        // Training History Queries
        [AuthorizePermission("Trainings", "Read", "Site")]
        public async Task<IEnumerable<WorkerTrainingHistory>> GetWorkerTrainingHistory(int workerId)
        {
            return await _trainingStatusService.GetWorkerTrainingHistoryAsync(workerId);
        }

        [AuthorizePermission("Trainings", "Read", "Site")]
        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiringTrainings(int daysAhead = 30)
        {
            return await _trainingStatusService.GetExpiringTrainingsAsync(daysAhead);
        }

        [AuthorizePermission("Trainings", "Read", "Site")]
        public async Task<IEnumerable<WorkerTrainingHistory>> GetExpiredTrainings()
        {
            return await _trainingStatusService.GetExpiredTrainingsAsync();
        }

        // Equipment Queries
        [AuthorizePermission("PPE", "Read", "Site")]
        public async Task<IEnumerable<Equipment>> GetAllEquipment()
        {
            return await _equipmentService.GetAllEquipmentAsync();
        }

        [AuthorizePermission("PPE", "Read", "Site")]
        public async Task<Equipment?> GetEquipmentById(int id)
        {
            return await _equipmentService.GetEquipmentByIdAsync(id);
        }

        [AuthorizePermission("PPE", "Read", "Site")]
        public async Task<IEnumerable<Equipment>> GetEquipmentByStatus(string status)
        {
            return await _equipmentService.GetEquipmentByStatusAsync(status);
        }

        [AuthorizePermission("PPE", "Read", "Site")]
        public async Task<IEnumerable<Equipment>> GetEquipmentByCategory(string category)
        {
            return await _equipmentService.GetEquipmentByCategoryAsync(category);
        }

        [AuthorizePermission("PPE", "Read", "Site")]
        public async Task<IEnumerable<Equipment>> GetEquipmentByLocation(string location)
        {
            return await _equipmentService.GetEquipmentByLocationAsync(location);
        }

        // Job Queries
        [UseProjection]
        [UseFiltering]
        [AuthorizePermission("Jobs", "Read", "Site")]
        public IQueryable<Job> GetAllJobs([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Jobs; ;
        }

        [UseProjection]
        [AuthorizePermission("Jobs", "Read", "Site")]
        public IQueryable<Job> GetJobById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Jobs.Where(j => j.Id == id);
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetRequestedJobs()
        {
            var jobs = await _jobService.GetRequestedJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetBlockedJobs()
        {
            var jobs = await _jobService.GetBlockedJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetPendingApprovalJobs()
        {
            var jobs = await _jobService.GetPendingApprovalJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetApprovedJobs()
        {
            var jobs = await _jobService.GetApprovedJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetDisapprovedJobs()
        {
            var jobs = await _jobService.GetDisapprovedJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetFinishedJobs()
        {
            var jobs = await _jobService.GetFinishedJobsAsync();
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetJobsByStatus(Shared.Enums.JobStatus status)
        {
            var jobs = await _jobService.GetJobsByStatusAsync(status);
            return jobs;
        }

        [AuthorizePermission("Jobs", "Read", "Site")]
        public async Task<IEnumerable<Job>> GetJobsByChiefEngineerId(int chiefEngineerId)
        {
            var jobs = await _jobService.GetJobsByChiefEngineerIdAsync(chiefEngineerId);
            return jobs;
        }

        // Category Queries
        [UseProjection]
        [UseFiltering]
        public IQueryable<Category> GetAllCategories([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Categories;
        }

        [UseProjection]
        public IQueryable<Category> GetCategoryById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Categories.Where(c => c.Id == id);
        }

        // Toolbox Queries
        public async Task<IEnumerable<GraphQLApi.GraphQL.Types.TodaysJobRiskAssessment>> GetTodaysJobRiskAssessment()
        {
            var serviceResult = await _toolboxService.GetTodaysJobRiskAssessmentAsync() ?? [];
            var result = serviceResult.Select(r => new GraphQLApi.GraphQL.Types.TodaysJobRiskAssessment
            {
                Id = r.Id,
                Title = r.Title,
                Hazards = [.. r.Hazards.Select(h => new GraphQLApi.GraphQL.Types.TodaysJobHazard
                {
                    Id = h.Id,
                    Description = h.Description,
                    ControlMeasures = [.. h.ControlMeasures.Select(cm => new GraphQLApi.GraphQL.Types.TodaysJobControlMeasure
                    {
                        Id = cm.Id,
                        Description = cm.Description
                    })]
                })]
            }).ToList();
            return result;
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Toolbox> GetAllToolboxes([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Toolboxes;
        }

        [UseProjection]
        public IQueryable<Toolbox> GetToolboxById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Toolboxes.Where(t => t.Id == id);
        }

        public async Task<Toolbox?> GetTodaysToolbox()
        {
            return await _toolboxService.GetTodaysToolboxAsync();
        }

        // Permit Queries
        // public async Task<IEnumerable<GraphQLApi.GraphQL.Types.ToolboxRiskAssessment>> GetToolboxRiskAssessment(int jobId)
        // {
        //     var serviceResult = await _permitService.GetToolboxRiskAssessmentAsync(jobId);
        //     var result = serviceResult.Select(r => new GraphQLApi.GraphQL.Types.ToolboxRiskAssessment
        //     {
        //         Id = r.Id,
        //         Title = r.Title,
        //         Hazards = r.Hazards.Select(h => new GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentHazard
        //         {
        //             Id = h.Id,
        //             Description = h.Description,
        //             ControlMeasures = h.ControlMeasures.Select(cm => new GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentControlMeasure
        //             {
        //                 Id = cm.Id,
        //                 Description = cm.Description
        //             }).ToList()
        //         }).ToList()
        //     });
        //     return result;
        // }

        public async Task<bool> VerifySignoff(List<int> workerIds)
        {
            return await _permitService.VerifySignoffAsync(workerIds);
        }

        [UseProjection]
        [UseFiltering]
        public IQueryable<Permit> GetAllPermits([Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Permits;
        }

        [UseProjection]
        public IQueryable<Permit> GetPermitById(int id, [Service] IDbContextFactory<AppDbContext> contextFactory)
        {
            var context = contextFactory.CreateDbContext();
            return context.Permits.Where(p => p.Id == id);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByJob(int jobId)
        {
            return await _permitService.GetPermitsByJobAsync(jobId);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByStatus(PermitStatus status)
        {
            return await _permitService.GetPermitsByStatusAsync(status);
        }

        public async Task<IEnumerable<Permit>> GetPermitsByDate(DateTime date)
        {
            return await _permitService.GetPermitsByDateAsync(date);
        }

        // Inspection Queries
        public async Task<Inspection?> GetInspectionById(int id)
        {
            return await _inspectionService.GetInspectionByIdAsync(id);
        }

        public async Task<IEnumerable<Inspection>> GetInspections(int skip = 0, int take = 50)
        {
            return await _inspectionService.GetInspectionsAsync(skip, take);
        }

        public async Task<IEnumerable<Inspection>> GetInspectionsByInspector(int inspectorId)
        {
            return await _inspectionService.GetInspectionsByInspectorAsync(inspectorId);
        }

        // Site Queries
        public async Task<IEnumerable<Site>> GetAllSites()
        {
            return await _siteService.GetAllSitesAsync();
        }

        public async Task<Site?> GetSiteById(Guid id)
        {
            return await _siteService.GetSiteByIdAsync(id);
        }

        public async Task<IEnumerable<Site>> GetSitesByStatus(string status)
        {
            return await _siteService.GetSitesByStatusAsync(status);
        }

        public async Task<IEnumerable<Site>> GetSitesByProjectManager(string projectManager)
        {
            return await _siteService.GetSitesByProjectManagerAsync(projectManager);
        }

        public async Task<IEnumerable<Site>> GetSitesByProjectType(string projectType)
        {
            return await _siteService.GetSitesByProjectTypeAsync(projectType);
        }

        public async Task<IEnumerable<Site>> SearchSites(string searchTerm)
        {
            return await _siteService.SearchSitesAsync(searchTerm);
        }
    }
}
