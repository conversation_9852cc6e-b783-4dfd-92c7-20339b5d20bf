using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Authorization;
using HotChocolate.Types;
using GraphQLApi.Services;
using Shared.GraphQL.Models.Notifications;

namespace GraphQLApi.GraphQL.Queries
{
    [ExtendObjectType<Query>]
    public class NotificationQueries
    {
        [Authorize]
        public async Task<List<Notification>> GetMyNotifications(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal,
            int skip = 0,
            int take = 50,
            bool unreadOnly = false)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            return await notificationService.GetUserNotificationsAsync(userId, skip, take, unreadOnly);
        }

        [Authorize]
        public async Task<int> GetUnreadNotificationCount(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            return await notificationService.GetUnreadCountAsync(userId);
        }

        [Authorize]
        public async Task<NotificationPreference?> GetNotificationPreferences(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal,
            string notificationType)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            return await notificationService.GetUserPreferencesAsync(userId, notificationType);
        }

        [Authorize]
        public async Task<List<string>> GetAvailableNotificationTypes(
            [Service] INotificationService notificationService)
        {
            // Return common notification types - this could be made dynamic
            return new List<string>
            {
                "training_status_changed",
                "training_expiring",
                "training_expired",
                "training_assigned",
                "training_completed",
                "worker_attendance",
                "task_assigned",
                "task_overdue",
                "system_maintenance",
                "security_alert",
                "authz_denied"
            };
        }
    }
}
