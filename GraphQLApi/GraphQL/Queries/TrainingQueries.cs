using GraphQLApi.Services;
using HotChocolate;
using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;
using GraphQLApi.GraphQL.Types;

namespace GraphQLApi.GraphQL.Queries
{
    [ExtendObjectType("Query")]
    public class TrainingQueries
    {
        // Training Session Queries (following ToolboxService pattern)
        public async Task<TrainingSession?> GetTrainingSessionAsync(
            [Service] ITrainingService trainingService,
            int sessionId)
        {
            return await trainingService.GetSessionByIdAsync(sessionId);
        }

        public async Task<IEnumerable<TrainingSession>> GetAllTrainingSessionsAsync(
            [Service] ITrainingService trainingService)
        {
            return await trainingService.GetAllSessionsAsync();
        }

        public async Task<IEnumerable<TrainingSession>> GetTrainingSessionsByDateAsync(
            [Service] ITrainingService trainingService,
            DateTime date)
        {
            return await trainingService.GetSessionsByDateAsync(date);
        }

        public async Task<TrainingSession?> GetTodaysTrainingSessionAsync(
            [Service] ITrainingService trainingService,
            int programId)
        {
            return await trainingService.GetTodaysSessionAsync(programId);
        }

        public async Task<IEnumerable<TrainingSession>> GetFilteredTrainingSessionsAsync(
            [Service] ITrainingService trainingService,
            TrainingSessionFilterInput? filter = null)
        {
            // This would be implemented with more sophisticated filtering logic
            return await trainingService.GetAllSessionsAsync();
        }

        // Training Program Queries
        public async Task<IEnumerable<TrainingProgram>> GetActiveTrainingProgramsAsync(
            [Service] ITrainingService trainingService)
        {
            return await trainingService.GetActiveProgramsAsync();
        }

        public async Task<TrainingProgram?> GetTrainingProgramAsync(
            [Service] ITrainingService trainingService,
            int programId)
        {
            return await trainingService.GetProgramByIdAsync(programId);
        }

        // Training Provider Queries
        public async Task<IEnumerable<TrainingProvider>> GetActiveTrainingProvidersAsync(
            [Service] ITrainingService trainingService)
        {
            return await trainingService.GetActiveProvidersAsync();
        }

        public async Task<TrainingProvider?> GetTrainingProviderAsync(
            [Service] ITrainingService trainingService,
            int providerId)
        {
            return await trainingService.GetProviderByIdAsync(providerId);
        }

        // Enrollment Queries
        public async Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsAsync(
            [Service] ITrainingService trainingService,
            int sessionId)
        {
            return await trainingService.GetSessionEnrollmentsAsync(sessionId);
        }

        // Certificate Queries
        public async Task<IEnumerable<TrainingCertificate>> GetWorkerCertificatesAsync(
            [Service] ITrainingService trainingService,
            int workerId)
        {
            return await trainingService.GetWorkerCertificatesAsync(workerId);
        }

        public async Task<IEnumerable<TrainingCertificate>> GetExpiringCertificatesAsync(
            [Service] ITrainingService trainingService,
            int daysAhead = 30)
        {
            return await trainingService.GetExpiringCertificatesAsync(daysAhead);
        }

        public async Task<IEnumerable<TrainingCertificate>> GetFilteredCertificatesAsync(
            [Service] ITrainingService trainingService,
            TrainingCertificateFilterInput? filter = null)
        {
            // This would be implemented with more sophisticated filtering logic
            if (filter?.WorkerId != null)
            {
                return await trainingService.GetWorkerCertificatesAsync(filter.WorkerId.Value);
            }
            
            return await trainingService.GetExpiringCertificatesAsync(365); // Get all within a year
        }

        // Worker Eligibility Queries
        public async Task<WorkerEligibilityStatus> CheckWorkerEligibilityAsync(
            [Service] ITrainingService trainingService,
            WorkerEligibilityInput input)
        {
            return await trainingService.CheckWorkerEligibilityAsync(input.WorkerId, input.TradeId);
        }

        // Per-worker certificate status queries
        public async Task<WorkerCertificateStatusSummary> GetWorkerCertificateStatusAsync(
            [Service] ITrainingService trainingService,
            int workerId)
        {
            return await trainingService.GetWorkerCertificateStatusAsync(workerId);
        }

        public async Task<Dictionary<int, WorkerCertificateStatusSummary>> GetWorkersCertificateStatusesAsync(
            [Service] ITrainingService trainingService,
            IEnumerable<int> workerIds)
        {
            return await trainingService.GetWorkersCertificateStatusesAsync(workerIds);
        }

        public async Task<IEnumerable<TrainingCertificate>> GetRequiredCertificatesForTradeAsync(
            [Service] ITrainingService trainingService,
            int workerId,
            int tradeId)
        {
            return await trainingService.GetRequiredCertificatesForTradeAsync(workerId, tradeId);
        }

        // Enrollment/Attendance Queries
        public async Task<IEnumerable<TrainingEnrollment>> GetSessionEnrollmentsWithAttendanceAsync(
            [Service] ITrainingService trainingService,
            int sessionId)
        {
            return await trainingService.GetSessionEnrollmentsWithAttendanceAsync(sessionId);
        }

        // Dashboard/Statistics Queries
        public async Task<TrainingDashboardStats> GetTrainingDashboardStatsAsync(
            [Service] ITrainingService trainingService,
            Guid? siteId = null)
        {
            var allSessions = await trainingService.GetAllSessionsAsync();
            var allCertificates = await trainingService.GetExpiringCertificatesAsync(365);
            var expiringCertificates = await trainingService.GetExpiringCertificatesAsync(30);

            var filteredSessions = siteId.HasValue 
                ? allSessions.Where(s => s.SiteId == siteId.Value)
                : allSessions;

            return new TrainingDashboardStats
            {
                TotalActiveSessions = filteredSessions.Count(s => s.Status == TrainingSessionStatus.IN_PROGRESS),
                TotalScheduledSessions = filteredSessions.Count(s => s.Status == TrainingSessionStatus.SCHEDULED),
                TotalCompletedSessions = filteredSessions.Count(s => s.Status == TrainingSessionStatus.COMPLETED),
                TotalValidCertificates = allCertificates.Count(c => c.Status == CertificateStatus.VALID),
                TotalExpiringCertificates = expiringCertificates.Count(),
                TotalExpiredCertificates = allCertificates.Count(c => c.Status == CertificateStatus.EXPIRED)
            };
        }
    }

    // Dashboard statistics type
    public class TrainingDashboardStats
    {
        public int TotalActiveSessions { get; set; }
        public int TotalScheduledSessions { get; set; }
        public int TotalCompletedSessions { get; set; }
        public int TotalValidCertificates { get; set; }
        public int TotalExpiringCertificates { get; set; }
        public int TotalExpiredCertificates { get; set; }
    }

    // Worker certificate status summary type
    public class WorkerCertificateStatusSummary
    {
        public int WorkerId { get; set; }
        public int TotalCertificates { get; set; }
        public int ValidCount { get; set; }
        public int ExpiringSoonCount { get; set; }
        public int ExpiredCount { get; set; }
        public CertificateStatus OverallStatus { get; set; }
        public DateTime? NearestExpiryDate { get; set; }
    }
}
