using HotChocolate.Types;
using Shared.DTOs;

namespace GraphQLApi.GraphQL.Types
{
    public class NotificationEventType : ObjectType<NotificationEvent>
    {
        protected override void Configure(IObjectTypeDescriptor<NotificationEvent> descriptor)
        {
            descriptor.Name("NotificationEvent");
            descriptor.Field(f => f.Type).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Title).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Message).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Entity).Type<StringType>();
            descriptor.Field(f => f.Operation).Type<StringType>();
            descriptor.Field(f => f.Metadata).Type<AnyType>();
        }
    }
}

