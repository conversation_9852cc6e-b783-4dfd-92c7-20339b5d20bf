using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace GraphQLApi.GraphQL.Types
{
    /// <summary>
    /// Input type for file upload operations
    /// </summary>
    public class FileUploadInput
    {
        /// <summary>
        /// The file to upload
        /// </summary>
        public IFile File { get; set; } = null!;

        /// <summary>
        /// Target bucket for the file (valid values: PROFILE_PICTURE, CERTIFICATION, SIGNATURES, TEMP, DOCS)
        /// </summary>
        public string BucketName { get; set; } = null!;

        /// <summary>
        /// Optional folder path within the bucket
        /// </summary>
        public string? FolderPath { get; set; }

        /// <summary>
        /// Optional description for the file
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Whether the file should be publicly accessible
        /// </summary>
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// Optional expiration date for the file
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Additional metadata as J<PERSON><PERSON> string
        /// </summary>
        public string? AdditionalMetadata { get; set; }
    }

    /// <summary>
    /// Response type for file upload operations
    /// </summary>
    public class FileUploadResponse
    {
        /// <summary>
        /// Whether the upload was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if upload failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The uploaded file metadata
        /// </summary>
        public FileMetadata? FileMetadata { get; set; }

        /// <summary>
        /// Presigned URL for accessing the file (if public or requested)
        /// </summary>
        public string? PresignedUrl { get; set; }
    }

    /// <summary>
    /// Input type for generating presigned URLs
    /// </summary>
    public class PresignedUrlInput
    {
        /// <summary>
        /// File metadata ID
        /// </summary>
        public int FileId { get; set; }

        /// <summary>
        /// Expiration time for the URL in minutes (default: 60)
        /// </summary>
        public int ExpirationMinutes { get; set; } = 60;
    }

    /// <summary>
    /// Response type for presigned URL generation
    /// </summary>
    public class PresignedUrlResponse
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The presigned URL
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// When the URL expires
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
    }
}
