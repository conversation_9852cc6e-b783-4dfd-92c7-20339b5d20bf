using HotChocolate.Types;
using Shared.DTOs;
using Shared.GraphQL.Models.Notifications;
using Shared.GraphQL.Types;

namespace GraphQLApi.GraphQL.Types
{
    public class NotificationType : ObjectType<Notification>
    {
        protected override void Configure(IObjectTypeDescriptor<Notification> descriptor)
        {
            descriptor.Name("Notification");
            descriptor.Field(f => f.Id).Type<NonNullType<IdType>>();
            descriptor.Field(f => f.Type).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Title).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Message).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Priority).Type<NonNullType<NotificationPriorityType>>();
            descriptor.Field(f => f.Status).Type<NonNullType<NotificationStatusType>>();
            descriptor.Field(f => f.Entity).Type<StringType>();
            descriptor.Field(f => f.Operation).Type<StringType>();
            descriptor.Field(f => f.Metadata).Type<StringType>();
            descriptor.Field(f => f.SentAt).Type<DateTimeType>();
            descriptor.Field(f => f.ReadAt).Type<DateTimeType>();
            descriptor.Field(f => f.ExpiresAt).Type<DateTimeType>();
            descriptor.Field(f => f.ActionUrl).Type<StringType>();
            descriptor.Field(f => f.ActionLabel).Type<StringType>();
            descriptor.Field(f => f.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(f => f.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(f => f.User).Type<ApplicationUserType>();
            descriptor.Field(f => f.Deliveries).Type<ListType<NotificationDeliveryType>>();
        }
    }

    public class NotificationDeliveryType : ObjectType<NotificationDelivery>
    {
        protected override void Configure(IObjectTypeDescriptor<NotificationDelivery> descriptor)
        {
            descriptor.Name("NotificationDelivery");
            descriptor.Field(f => f.Id).Type<NonNullType<IdType>>();
            descriptor.Field(f => f.Channel).Type<NonNullType<NotificationChannelType>>();
            descriptor.Field(f => f.Status).Type<NonNullType<NotificationStatusType>>();
            descriptor.Field(f => f.Recipient).Type<StringType>();
            descriptor.Field(f => f.SentAt).Type<DateTimeType>();
            descriptor.Field(f => f.DeliveredAt).Type<DateTimeType>();
            descriptor.Field(f => f.FailedAt).Type<DateTimeType>();
            descriptor.Field(f => f.ErrorMessage).Type<StringType>();
            descriptor.Field(f => f.RetryCount).Type<NonNullType<IntType>>();
            descriptor.Field(f => f.NextRetryAt).Type<DateTimeType>();
            descriptor.Field(f => f.CreatedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class NotificationPreferenceType : ObjectType<NotificationPreference>
    {
        protected override void Configure(IObjectTypeDescriptor<NotificationPreference> descriptor)
        {
            descriptor.Name("NotificationPreference");
            descriptor.Field(f => f.Id).Type<NonNullType<IdType>>();
            descriptor.Field(f => f.NotificationType).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.InAppEnabled).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.EmailEnabled).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.SmsEnabled).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.MinimumPriority).Type<NonNullType<NotificationPriorityType>>();
            descriptor.Field(f => f.DoNotDisturbEnabled).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.DoNotDisturbStart).Type<TimeSpanType>();
            descriptor.Field(f => f.DoNotDisturbEnd).Type<TimeSpanType>();
            descriptor.Field(f => f.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(f => f.UpdatedAt).Type<DateTimeType>();
        }
    }

    public class NotificationChannelType : EnumType<NotificationChannel>
    {
        protected override void Configure(IEnumTypeDescriptor<NotificationChannel> descriptor)
        {
            descriptor.Name("NotificationChannel");
            descriptor.Value(NotificationChannel.InApp).Name("IN_APP");
            descriptor.Value(NotificationChannel.Email).Name("EMAIL");
            descriptor.Value(NotificationChannel.Sms).Name("SMS");
        }
    }

    public class NotificationPriorityType : EnumType<NotificationPriority>
    {
        protected override void Configure(IEnumTypeDescriptor<NotificationPriority> descriptor)
        {
            descriptor.Name("NotificationPriority");
            descriptor.Value(NotificationPriority.Low).Name("LOW");
            descriptor.Value(NotificationPriority.Medium).Name("MEDIUM");
            descriptor.Value(NotificationPriority.High).Name("HIGH");
            descriptor.Value(NotificationPriority.Critical).Name("CRITICAL");
        }
    }

    public class NotificationStatusType : EnumType<NotificationStatus>
    {
        protected override void Configure(IEnumTypeDescriptor<NotificationStatus> descriptor)
        {
            descriptor.Name("NotificationStatus");
            descriptor.Value(NotificationStatus.Pending).Name("PENDING");
            descriptor.Value(NotificationStatus.Sent).Name("SENT");
            descriptor.Value(NotificationStatus.Failed).Name("FAILED");
            descriptor.Value(NotificationStatus.Read).Name("READ");
        }
    }

    // Input types for mutations
    public class NotificationPreferenceInputType : InputObjectType
    {
        protected override void Configure(IInputObjectTypeDescriptor descriptor)
        {
            descriptor.Name("NotificationPreferenceInput");
            descriptor.Field("notificationType").Type<NonNullType<StringType>>();
            descriptor.Field("inAppEnabled").Type<NonNullType<BooleanType>>();
            descriptor.Field("emailEnabled").Type<NonNullType<BooleanType>>();
            descriptor.Field("smsEnabled").Type<NonNullType<BooleanType>>();
            descriptor.Field("minimumPriority").Type<NonNullType<NotificationPriorityType>>();
            descriptor.Field("doNotDisturbEnabled").Type<NonNullType<BooleanType>>();
            descriptor.Field("doNotDisturbStart").Type<TimeSpanType>();
            descriptor.Field("doNotDisturbEnd").Type<TimeSpanType>();
        }
    }
}
