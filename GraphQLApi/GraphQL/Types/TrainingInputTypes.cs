using Shared.Enums;

namespace GraphQLApi.GraphQL.Types
{
    // Input types for Training mutations (following ToolboxInputTypes pattern)
    public class CreateTrainingSessionInput
    {
        public int ProgramId { get; set; }
        public int ProviderId { get; set; }
        public int ConductorId { get; set; }
        public Guid? SiteId { get; set; }
        public TrainingMode Mode { get; set; }
        public string Location { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int Capacity { get; set; }
        public string? Notes { get; set; }
        public IFile? SessionPictureFile { get; set; }
    }

    public class UpdateTrainingSessionInput
    {
        public int SessionId { get; set; }
        public TrainingMode? Mode { get; set; }
        public string? Location { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? Capacity { get; set; }
        public string? Notes { get; set; }
        public TrainingSessionStatus? Status { get; set; }
    }

    public class AddTrainingAttendeesInput
    {
        public int SessionId { get; set; }
        public List<int> WorkerIds { get; set; } = new();
    }

    public class TrainingAttendanceInput
    {
        public int WorkerId { get; set; }
        public TrainingOutcome Outcome { get; set; }
        public string? Notes { get; set; }
    }

    public class FinalizeTrainingSessionInput
    {
        public int SessionId { get; set; }
        public List<TrainingAttendanceInput> Attendance { get; set; } = new();
    }

    public class CreateTrainingProgramInput
    {
        public string Code { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int ValidityDays { get; set; }
        public CertificateType CertificateType { get; set; }
        public List<int>? Prerequisites { get; set; }
        public bool Active { get; set; } = true;
    }

    public class UpdateTrainingProgramInput
    {
        public int ProgramId { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public int? ValidityDays { get; set; }
        public CertificateType? CertificateType { get; set; }
        public List<int>? Prerequisites { get; set; }
        public bool? Active { get; set; }
    }

    public class CreateTrainingProviderInput
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;
        public List<string>? Certifications { get; set; }
        public bool Active { get; set; } = true;
    }

    public class UpdateTrainingProviderInput
    {
        public int ProviderId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Contact { get; set; }
        public List<string>? Certifications { get; set; }
        public bool? Active { get; set; }
    }

    public class EnrollWorkerInput
    {
        public int SessionId { get; set; }
        public int WorkerId { get; set; }
    }

    public class UpdateEnrollmentStatusInput
    {
        public int EnrollmentId { get; set; }
        public EnrollmentStatus Status { get; set; }
    }

    public class IssueCertificateInput
    {
        public int SessionId { get; set; }
        public int WorkerId { get; set; }
        public TrainingOutcome Outcome { get; set; }
        public string CertificateNo { get; set; } = string.Empty;
        public DateTime? CustomExpiryDate { get; set; }
        public IFile? CertificateFile { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateCertificateStatusInput
    {
        public int CertificateId { get; set; }
        public CertificateStatus Status { get; set; }
    }

    public class MarkTrainingAttendanceInput
    {
        public int EnrollmentId { get; set; }
        public bool Attended { get; set; }
        public string? Notes { get; set; }
    }

    public class BatchEnrollByTradeInput
    {
        public int SessionId { get; set; }
        public int TradeId { get; set; }
    }

    public class BatchEnrollBySiteInput
    {
        public int SessionId { get; set; }
        public Guid SiteId { get; set; }
    }

    public class CreateTradeRequirementInput
    {
        public int TradeId { get; set; }
        public int ProgramId { get; set; }
        public bool Mandatory { get; set; } = true;
        public string? Notes { get; set; }
    }

    public class UpdateTradeRequirementInput
    {
        public int RequirementId { get; set; }
        public bool? Mandatory { get; set; }
        public string? Notes { get; set; }
    }

    // Filter inputs for queries
    public class TrainingSessionFilterInput
    {
        public int? ProgramId { get; set; }
        public int? ProviderId { get; set; }
        public Guid? SiteId { get; set; }
        public TrainingSessionStatus? Status { get; set; }
        public TrainingMode? Mode { get; set; }
        public DateTime? StartDateFrom { get; set; }
        public DateTime? StartDateTo { get; set; }
    }

    public class TrainingCertificateFilterInput
    {
        public int? WorkerId { get; set; }
        public int? ProgramId { get; set; }
        public int? ProviderId { get; set; }
        public CertificateStatus? Status { get; set; }
        public DateTime? ExpiryDateFrom { get; set; }
        public DateTime? ExpiryDateTo { get; set; }
        public bool? ExpiringSoon { get; set; }
    }

    public class WorkerEligibilityInput
    {
        public int WorkerId { get; set; }
        public int TradeId { get; set; }
    }
}
