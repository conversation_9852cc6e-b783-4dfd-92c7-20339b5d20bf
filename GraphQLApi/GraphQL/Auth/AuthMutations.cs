using GraphQLApi.Auth.Authorization;
using GraphQLApi.Services;
using HotChocolate.Authorization;
using Microsoft.AspNetCore.Http;
using Shared.GraphQL.Models.Auth;
using System.Security.Claims;

namespace GraphQLApi.GraphQL.Auth
{
    [ExtendObjectType("Mutation")]
    public class AuthMutations
    {
        public async Task<LoginPayload> LoginAsync(
            LoginInput input,
            [Service] IAuthenticationService authService,
            [Service] IHttpContextAccessor httpContextAccessor)
        {
            var context = httpContextAccessor.HttpContext!;
            var ipAddress = GetClientIpAddress(context);
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            var request = new LoginRequest
            {
                Email = input.Email,
                Password = input.Password,
                TenantId = input.TenantId,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                RememberMe = input.RememberMe
            };

            var result = await authService.LoginAsync(request);

            if (!result.Success)
            {
                return new LoginPayload
                {
                    Success = false,
                    ErrorMessage = result.ErrorMessage
                };
            }

            // Set HTTP-only cookie for refresh token
            if (!string.IsNullOrEmpty(result.RefreshToken))
            {
                context.Response.Cookies.Append("refresh_token", result.RefreshToken, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddDays(7)
                });
            }

            // Set session cookie
            if (result.Session != null)
            {
                context.Response.Cookies.Append("session_id", result.Session.SessionId, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = result.Session.ExpiresAt
                });
            }

            return new LoginPayload
            {
                Success = true,
                AccessToken = result.AccessToken,
                ExpiresAt = result.ExpiresAt,
                User = result.User
            };
        }

        public async Task<RefreshTokenPayload> RefreshTokenAsync(
            RefreshTokenInput input,
            [Service] IAuthenticationService authService,
            [Service] IHttpContextAccessor httpContextAccessor)
        {
            var context = httpContextAccessor.HttpContext!;
            var ipAddress = GetClientIpAddress(context);
            var userAgent = context.Request.Headers["User-Agent"].ToString();

            // Get refresh token from input or cookie
            var refreshToken = input.RefreshToken ?? context.Request.Cookies["refresh_token"];

            if (string.IsNullOrEmpty(refreshToken))
            {
                return new RefreshTokenPayload
                {
                    Success = false,
                    ErrorMessage = "Refresh token not provided"
                };
            }

            var request = new RefreshTokenRequest
            {
                RefreshToken = refreshToken,
                IpAddress = ipAddress,
                UserAgent = userAgent
            };

            var result = await authService.RefreshTokenAsync(request);

            if (!result.Success)
            {
                // Clear cookies on failure
                context.Response.Cookies.Delete("refresh_token");
                context.Response.Cookies.Delete("session_id");

                return new RefreshTokenPayload
                {
                    Success = false,
                    ErrorMessage = result.ErrorMessage
                };
            }

            // Update refresh token cookie
            if (!string.IsNullOrEmpty(result.RefreshToken))
            {
                context.Response.Cookies.Append("refresh_token", result.RefreshToken, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddDays(7)
                });
            }

            return new RefreshTokenPayload
            {
                Success = true,
                AccessToken = result.AccessToken,
                ExpiresAt = result.ExpiresAt,
                User = result.User
            };
        }

        [Authorize]
        public async Task<LogoutPayload> LogoutAsync(
            LogoutInput input,
            [Service] IAuthenticationService authService,
            [Service] IHttpContextAccessor httpContextAccessor,
            ClaimsPrincipal claimsPrincipal)
        {
            var context = httpContextAccessor.HttpContext!;
            var ipAddress = GetClientIpAddress(context);

            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return new LogoutPayload
                {
                    Success = false,
                    ErrorMessage = "Invalid user"
                };
            }

            var refreshToken = input.RefreshToken ?? context.Request.Cookies["refresh_token"];
            var sessionId = input.SessionId ?? context.Request.Cookies["session_id"];

            var request = new LogoutRequest
            {
                UserId = userId,
                RefreshToken = refreshToken,
                SessionId = sessionId,
                IpAddress = ipAddress
            };

            var success = await authService.LogoutAsync(request);

            // Clear cookies
            context.Response.Cookies.Delete("refresh_token");
            context.Response.Cookies.Delete("session_id");
            context.Response.Cookies.Delete("access_token");

            return new LogoutPayload
            {
                Success = success,
                ErrorMessage = success ? null : "Logout failed"
            };
        }

        [Authorize]
        public async Task<LogoutPayload> LogoutAllSessionsAsync(
            [Service] IAuthenticationService authService,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return new LogoutPayload
                {
                    Success = false,
                    ErrorMessage = "Invalid user"
                };
            }

            var success = await authService.LogoutAllSessionsAsync(userId);

            return new LogoutPayload
            {
                Success = success,
                ErrorMessage = success ? null : "Logout all sessions failed"
            };
        }

        [Authorize]
        public async Task<ChangePasswordPayload> ChangePasswordAsync(
            ChangePasswordInput input,
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return new ChangePasswordPayload
                {
                    Success = false,
                    ErrorMessage = "Invalid user"
                };
            }

            try
            {
                var success = await userService.ChangePasswordAsync(userId, input.CurrentPassword, input.NewPassword);

                return new ChangePasswordPayload
                {
                    Success = success,
                    ErrorMessage = success ? null : "Password change failed"
                };
            }
            catch (ArgumentException ex)
            {
                return new ChangePasswordPayload
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        [AuthorizePermission("RoleManagement", "Create", "Company")]
        public async Task<CreateUserPayload> CreateUserAsync(
            CreateUserInput input,
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal)
        {
            var tenantIdClaim = claimsPrincipal.FindFirst("tenant_id")?.Value;
            if (!int.TryParse(tenantIdClaim, out var tenantId))
            {
                return new CreateUserPayload
                {
                    Success = false,
                    ErrorMessage = "Invalid tenant"
                };
            }

            var createdBy = claimsPrincipal.FindFirst(ClaimTypes.Email)?.Value ?? "System";

            try
            {
                var request = new CreateUserRequest
                {
                    FirstName = input.FirstName,
                    LastName = input.LastName,
                    Email = input.Email,
                    Phone = input.Phone,
                    Password = input.Password,
                    RoleId = input.RoleId,
                    TenantId = tenantId,
                    CreatedBy = createdBy
                };

                var user = await userService.CreateUserAsync(request);

                return new CreateUserPayload
                {
                    Success = true,
                    User = user
                };
            }
            catch (Exception ex)
            {
                return new CreateUserPayload
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static string GetClientIpAddress(HttpContext context)
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }
            return ipAddress ?? "Unknown";
        }
    }
}
