using GraphQLApi.Auth.Authorization;
using GraphQLApi.Services;
using HotChocolate.Authorization;
using Microsoft.AspNetCore.Http;
using Shared.GraphQL.Models.Auth;
using System.Security.Claims;

namespace GraphQLApi.GraphQL.Auth
{
    [ExtendObjectType("Query")]
    public class AuthQueries
    {
        [Authorize]
        public async Task<ApplicationUser?> MeAsync(
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return null;
            }

            return await userService.GetUserByIdAsync(userId);
        }

        [Authorize]
        public async Task<IEnumerable<SessionInfo>> MySessionsAsync(
            [Service] IAuthenticationService authService,
            [Service] IHttpContextAccessor httpContextAccessor,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                return new List<SessionInfo>();
            }

            var sessions = await authService.GetActiveSessionsAsync(userId);
            var currentSessionId = httpContextAccessor.HttpContext?.Request.Cookies["session_id"];

            return sessions.Select(s => new SessionInfo
            {
                SessionId = s.SessionId,
                IpAddress = s.IpAddress,
                UserAgent = s.UserAgent,
                DeviceType = s.DeviceType,
                Location = s.Location,
                CreatedAt = s.CreatedAt,
                LastActivityAt = s.LastActivityAt,
                ExpiresAt = s.ExpiresAt,
                IsActive = s.IsActive,
                IsCurrent = s.SessionId == currentSessionId
            });
        }

        [AuthorizePermission("RoleManagement", "Read", "Site")]
        public async Task<UserConnection> UsersAsync(
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal,
            int skip = 0,
            int take = 50)
        {
            var tenantIdClaim = claimsPrincipal.FindFirst("tenant_id")?.Value;
            if (!int.TryParse(tenantIdClaim, out var tenantId))
            {
                return new UserConnection();
            }

            var users = await userService.GetUsersByTenantAsync(tenantId, skip, take);
            var usersList = users.ToList();

            return new UserConnection
            {
                Nodes = usersList,
                TotalCount = usersList.Count, // In a real implementation, you'd get the actual total count
                HasNextPage = usersList.Count == take,
                HasPreviousPage = skip > 0
            };
        }

        [AuthorizePermission("RoleManagement", "Read", "Site")]
        public async Task<ApplicationUser?> UserAsync(
            int userId,
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal)
        {
            var tenantIdClaim = claimsPrincipal.FindFirst("tenant_id")?.Value;
            if (!int.TryParse(tenantIdClaim, out var tenantId))
            {
                return null;
            }

            var user = await userService.GetUserByIdAsync(userId);
            
            // Ensure user belongs to the same tenant
            if (user?.TenantId != tenantId)
            {
                return null;
            }

            return user;
        }

        [AuthorizePermission("RoleManagement", "Read", "Site")]
        public async Task<IEnumerable<ApplicationUser>> UsersByRoleAsync(
            int roleId,
            [Service] IUserService userService,
            ClaimsPrincipal claimsPrincipal,
            int skip = 0,
            int take = 50)
        {
            var tenantIdClaim = claimsPrincipal.FindFirst("tenant_id")?.Value;
            if (!int.TryParse(tenantIdClaim, out var tenantId))
            {
                return new List<ApplicationUser>();
            }

            var users = await userService.GetUsersByRoleAsync(roleId, skip, take);
            
            // Filter by tenant
            return users.Where(u => u.TenantId == tenantId);
        }

        public async Task<bool> ValidateSessionAsync(
            string sessionId,
            [Service] IAuthenticationService authService)
        {
            return await authService.ValidateSessionAsync(sessionId);
        }
    }
}
