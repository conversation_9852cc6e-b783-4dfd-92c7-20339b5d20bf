using Shared.GraphQL.Models.Auth;

namespace GraphQLApi.GraphQL.Auth
{
    // Input Types
    public record LoginInput(
        string Email,
        string Password,
        int TenantId,
        bool RememberMe = false
    );

    public record RefreshTokenInput(
        string? RefreshToken = null
    );

    public record LogoutInput(
        string? RefreshToken = null,
        string? SessionId = null
    );

    public record ChangePasswordInput(
        string CurrentPassword,
        string NewPassword
    );

    public record CreateUserInput(
        string FirstName,
        string LastName,
        string Email,
        string? Phone,
        string Password,
        int RoleId
    );

    public record UpdateUserInput(
        int UserId,
        string? FirstName = null,
        string? LastName = null,
        string? Email = null,
        string? Phone = null,
        int? RoleId = null,
        UserStatus? Status = null,
        bool? EmailConfirmed = null,
        bool? PhoneConfirmed = null,
        bool? TwoFactorEnabled = null
    );

    // Payload Types
    public class LoginPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? AccessToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public ApplicationUser? User { get; set; }
    }

    public class RefreshTokenPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? AccessToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public ApplicationUser? User { get; set; }
    }

    public class LogoutPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class ChangePasswordPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class CreateUserPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public ApplicationUser? User { get; set; }
    }

    public class UpdateUserPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public ApplicationUser? User { get; set; }
    }

    public class DeleteUserPayload
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    // Query Types
    public class UserConnection
    {
        public IEnumerable<ApplicationUser> Nodes { get; set; } = new List<ApplicationUser>();
        public int TotalCount { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    public class SessionInfo
    {
        public string SessionId { get; set; } = string.Empty;
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public string? DeviceType { get; set; }
        public string? Location { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastActivityAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsActive { get; set; }
        public bool IsCurrent { get; set; }
    }
}
