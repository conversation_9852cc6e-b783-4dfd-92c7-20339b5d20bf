using System.Security.Claims;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Authorization;
using HotChocolate.Types;
using GraphQLApi.Services;
using Shared.DTOs;
using Shared.GraphQL.Models.Notifications;

namespace GraphQLApi.GraphQL.Mutations
{
    [ExtendObjectType<Mutation>]
    public class NotificationMutations
    {
        [Authorize]
        public async Task<bool> MarkNotificationAsRead(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal,
            int notificationId)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            await notificationService.MarkAsReadAsync(notificationId, userId);
            return true;
        }

        [Authorize]
        public async Task<int> MarkAllNotificationsAsRead(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            await notificationService.MarkAllAsReadAsync(userId);
            return await notificationService.GetUnreadCountAsync(userId);
        }

        [Authorize]
        public async Task<bool> UpdateNotificationPreferences(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal,
            string notificationType,
            bool inAppEnabled,
            bool emailEnabled,
            bool smsEnabled,
            NotificationPriority minimumPriority,
            bool doNotDisturbEnabled = false,
            System.TimeSpan? doNotDisturbStart = null,
            System.TimeSpan? doNotDisturbEnd = null)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            var preferences = new NotificationPreference
            {
                NotificationType = notificationType,
                InAppEnabled = inAppEnabled,
                EmailEnabled = emailEnabled,
                SmsEnabled = smsEnabled,
                MinimumPriority = minimumPriority,
                DoNotDisturbEnabled = doNotDisturbEnabled,
                DoNotDisturbStart = doNotDisturbStart,
                DoNotDisturbEnd = doNotDisturbEnd
            };

            await notificationService.UpdateUserPreferencesAsync(userId, notificationType, preferences);
            return true;
        }

        [Authorize]
        public async Task<bool> SendTestNotification(
            [Service] INotificationService notificationService,
            ClaimsPrincipal claimsPrincipal,
            string title = "Test Notification",
            string message = "This is a test notification to verify your notification settings.",
            NotificationPriority priority = NotificationPriority.Medium)
        {
            var userIdClaim = claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (!int.TryParse(userIdClaim, out var userId))
            {
                throw new GraphQLException("User not found");
            }

            var testEvent = new NotificationEvent(
                Type: "test_notification",
                Title: title,
                Message: message,
                Priority: priority,
                Recipients: new[] { $"userid:{userId}" }.ToList()
            );

            await notificationService.PublishAsync(testEvent);
            return true;
        }
    }
}
