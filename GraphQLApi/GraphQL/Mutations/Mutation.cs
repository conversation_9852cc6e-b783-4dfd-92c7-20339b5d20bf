using GraphQLApi.Data;
using GraphQLApi.Services;
using GraphQLApi.Auth.Authorization;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Permits;
using HotChocolate;
using HotChocolate.Authorization;
using Shared.Utils;
using Shared.Enums;
using Shared.DTOs;
using GraphQLApi.GraphQL.Types;
// using Task = Shared.GraphQL.Models.Task;
using ServiceHazardInput = GraphQLApi.Services.HazardInput;
using ServiceDocumentFileInput = GraphQLApi.Services.ServiceDocumentFileInput;
using GraphQLHazardInput = GraphQLApi.GraphQL.Types.HazardInput;
using Shared.Errors;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly IWorkerTrainingService _workerTrainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        private readonly IEquipmentService _equipmentService;
        private readonly IJobService _jobService;
        private readonly IToolboxService _toolboxService;
        private readonly IPermitService _permitService;
        private readonly IRiskAssessmentService _riskAssessmentService;
        private readonly InspectionService _inspectionService;
        private readonly ISiteService _siteService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IRelationshipService _relationshipService;
        private readonly IMinioService _minioService;
        private readonly ILogger<Mutation> _logger;

        public Mutation(
            IWorkerService workerService,
            ITrainingService trainingService,
            IWorkerTrainingService workerTrainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            IEquipmentService equipmentService,
            IJobService jobService,
            IToolboxService toolboxService,
            IPermitService permitService,
            IRiskAssessmentService riskAssessmentService,
            InspectionService inspectionService,
            ISiteService siteService,
            IDbContextFactory<AppDbContext> contextFactory,
            IRelationshipService relationshipService,
            IMinioService minioService,
            ILogger<Mutation> logger)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _workerTrainingService = workerTrainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            _equipmentService = equipmentService;
            _jobService = jobService;
            _toolboxService = toolboxService;
            _permitService = permitService;
            _riskAssessmentService = riskAssessmentService;
            _inspectionService = inspectionService;
            _siteService = siteService;
            _contextFactory = contextFactory;
            _relationshipService = relationshipService;
            _minioService = minioService;
            _logger = logger;
        }

        private static string GetFileExtensionFromName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return string.Empty;

            int lastDotIndex = fileName.LastIndexOf('.');

            // No dot found or dot is the first character (e.g., ".bashrc")
            if (lastDotIndex <= 0 || lastDotIndex == fileName.Length - 1)
                return string.Empty;

            return fileName.Substring(lastDotIndex + 1);
        }
        // Worker Mutations
        [AuthorizePermission("Workers", "Create", "Site")]
        public async Task<Worker> CreateWorker(
            CreateWorkerInput input
           )
        {
            string name = input.Name;
            string company = input.Company;
            string nationalId = input.NationalId;
            string gender = input.Gender;
            string phoneNumber = input.PhoneNumber;
            DateOnly? dateOfBirth = input.DateOfBirth;
            List<int>? trainingIds = input.TrainingIds;
            List<int>? tradeIds = input.TradeIds;
            List<int>? skillIds = input.SkillIds;
            string? mpesaNumber = input.MpesaNumber;
            string? email = input.Email;
            DateTime? inductionDate = input.InductionDate;
            DateTime? medicalCheckDate = input.MedicalCheckDate;
            IFile? profilePicture = input.ProfilePicture;
            IFile? signature = input.Signature;
            List<GraphQLApi.GraphQL.Types.DocumentFileInput>? documents = input.Documents;

            trainingIds ??= new List<int>();
            tradeIds ??= new List<int>();
            skillIds ??= new List<int>();

            // Use a single database context for transaction management
            using var context = await _contextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    NationalId = nationalId,
                    Gender = gender,
                    DateOfBirth = dateOfBirth,
                    ManHours = 0,
                    Rating = 0,
                    PhoneNumber = phoneNumber,
                    MpesaNumber = mpesaNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };

                // Add worker to context but don't save yet
                context.Workers.Add(worker);
                await context.SaveChangesAsync(); // Save to get the ID

                // Upload files first before committing the transaction
                if (profilePicture != null)
                {
                    using var profileStream = profilePicture.OpenReadStream();
                    var profileMetadata = await _minioService.UploadFileAsync(
                        profileStream,
                        $"profile-picture.{GetFileExtensionFromName(profilePicture.Name)}",
                        "profile-picture",
                        profilePicture.ContentType ?? "image/jpeg",
                        "Worker profile picture",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (profileMetadata != null)
                    {
                        worker.ProfilePictureFileId = profileMetadata.Id;
                    }
                }

                if (signature != null)
                {
                    using var signatureStream = signature.OpenReadStream();
                    var signatureMetadata = await _minioService.UploadFileAsync(
                        signatureStream,
                        signature.Name,
                        "signatures",
                        signature.ContentType ?? "image/png",
                        "Worker signature",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (signatureMetadata != null)
                    {
                        worker.SignatureFileId = signatureMetadata.Id;
                    }
                }

                // Upload documents
                if (documents != null && documents.Count != 0)
                {
                    foreach (var doc in documents)
                    {
                        using var docStream = doc.File.OpenReadStream();
                        var docMetadata = await _minioService.UploadFileAsync(
                            docStream,
                            doc.File.Name,
                            "docs",
                            doc.File.ContentType ?? "application/pdf",
                            doc.Description,
                            $"worker-{worker.Id}",
                            doc.IsPublic,
                            doc.ExpiresAt);

                        if (docMetadata != null)
                        {
                            var documentFile = new DocumentFile
                            {
                                Name = doc.Name,
                                FileMetadataId = docMetadata.Id,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System" // TODO: Get from current user context
                            };

                            context.DocumentFiles.Add(documentFile);

                            // Set discriminator fields for polymorphic relationship
                            context.Entry(documentFile).Property("EntityType").CurrentValue = "Worker";
                            context.Entry(documentFile).Property("WorkerId").CurrentValue = worker.Id;
                        }
                    }
                }

                // Save all changes including file metadata
                await context.SaveChangesAsync();

                // Add relationships if specified using the relationship service
                if (trainingIds.Count != 0)
                {
                    await _relationshipService.AssignTrainingsToWorkerAsync(worker.Id, trainingIds);
                }
                if (tradeIds.Count != 0)
                {
                    await _relationshipService.AssignTradesToWorkerAsync(worker.Id, tradeIds);
                }
                if (skillIds.Count != 0)
                {
                    await _relationshipService.AssignSkillsToWorkerAsync(worker.Id, skillIds);
                }

                // Commit the transaction - everything succeeded
                await transaction.CommitAsync();

                return await _workerService.GetWorkerByIdAsync(worker.Id) ?? worker;
            }
            catch (GraphQLException)
            {
                // Rollback transaction and re-throw GraphQL exceptions
                await transaction.RollbackAsync();
                throw;
            }
            catch (Exception ex)
            {
                // Rollback transaction on any other exception
                await transaction.RollbackAsync();
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }
        
        [AuthorizePermission("Workers", "Create", "Site")]
        public async Task<Worker> CreateWorkerWithTraining(CreateWorkerWithTrainingInput input)
        {
            // Use a single database context for transaction management
            using var context = await _contextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var worker = new Worker
                {
                    Name = input.Name,
                    Company = input.Company,
                    NationalId = input.NationalId,
                    Gender = input.Gender,
                    DateOfBirth = input.DateOfBirth,
                    ManHours = 0,
                    Rating = 0,
                    PhoneNumber = input.PhoneNumber,
                    MpesaNumber = input.MpesaNumber,
                    Email = input.Email,
                    InductionDate = input.InductionDate,
                    MedicalCheckDate = input.MedicalCheckDate
                };

                // Add trades if specified
                if (input.TradeIds != null && input.TradeIds.Count != 0)
                {
                    var trades = await context.Trades
                        .Where(t => input.TradeIds.Contains(t.Id))
                        .ToListAsync();
                    foreach (var trade in trades)
                    {
                        if (!worker.Trades.Contains(trade))
                            worker.Trades.Add(trade);
                    }
                }

                // Add skills if specified
                if (input.SkillIds != null && input.SkillIds.Count != 0)
                {
                    var skills = await context.Skills
                        .Where(s => input.SkillIds.Contains(s.Id))
                        .ToListAsync();
                    foreach (var skill in skills)
                    {
                        if (!worker.Skills.Contains(skill))
                            worker.Skills.Add(skill);
                    }
                }

                // Add worker to context but don't save yet
                context.Workers.Add(worker);
                await context.SaveChangesAsync(); // Save to get the ID

                if (worker.Id == 0)
                {
                    throw new GraphQLException(new Error(
                        "Validation",
                        "Failed to create worker")
                    );
                }

                // Upload profile picture first
                if (input.ProfilePicture != null)
                {
                    using var profileStream = input.ProfilePicture.OpenReadStream();
                    var profileMetadata = await _minioService.UploadFileAsync(
                        profileStream,
                        $"profile-picture_{worker.Id}.{GetFileExtensionFromName(input.ProfilePicture.Name)}",
                        "profile-picture",
                        input.ProfilePicture.ContentType ?? "image/jpeg",
                        "Worker profile picture",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (profileMetadata != null)
                    {
                        worker.ProfilePictureFileId = profileMetadata.Id;
                    }
                    else
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "Failed to upload profile picture")
                        );
                    }
                }

                // Upload signature
                if (input.Signature != null)
                {
                    using var signatureStream = input.Signature.OpenReadStream();
                    var signatureMetadata = await _minioService.UploadFileAsync(
                        signatureStream,
                        $"signature_{worker.Id}.{GetFileExtensionFromName(input.Signature.Name)}",
                        "signatures",
                        input.Signature.ContentType ?? "image/png",
                        "Worker signature",
                        $"worker-{worker.Id}",
                        false,
                        null);

                    if (signatureMetadata != null)
                    {
                        worker.SignatureFileId = signatureMetadata.Id;
                    }
                    else
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "Failed to upload signature")
                        );
                    }
                }

                // Upload documents
                if (input.Documents != null && input.Documents.Count != 0)
                {
                    foreach (var doc in input.Documents)
                    {
                        using var docStream = doc.File.OpenReadStream();
                        var docMetadata = await _minioService.UploadFileAsync(
                            docStream,
                            doc.File.Name,
                            "docs",
                            doc.File.ContentType ?? "application/pdf",
                            doc.Description,
                            $"worker-{worker.Id}",
                            doc.IsPublic,
                            doc.ExpiresAt);

                        if (docMetadata != null)
                        {
                            var documentFile = new DocumentFile
                            {
                                Name = doc.Name,
                                FileMetadataId = docMetadata.Id,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System"
                            };
                            context.DocumentFiles.Add(documentFile);
                            worker.DocumentFiles.Add(documentFile);
                        }
                        else
                        {
                            throw new GraphQLException(new Error(
                                "Validation",
                                "Failed to upload document")
                            );
                        }
                    }
                }

                // Handle training assignments with documents
                if (input.Trainings != null && input.Trainings.Count != 0)
                {
                    var trainings = await context.Trainings
                        .Where(t => input.Trainings.Select(ti => ti.TrainingId).Contains(t.Id))
                        .ToListAsync();
                    if (trainings.Count != input.Trainings.Count)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            "One or more trainings not found")
                        );
                    }

                    foreach (var trainingInput in input.Trainings)
                    {
                        // Create worker-training relationship
                        var workerTraining = await _workerTrainingService.CreateWorkerTrainingAsync(
                            worker.Id,
                            trainingInput.TrainingId,
                            trainingInput.Notes);

                        // Handle training-specific documents
                        if (trainingInput.Documents != null && trainingInput.Documents.Count != 0)
                        {
                            foreach (var doc in trainingInput.Documents)
                            {
                                using var docStream = doc.File.OpenReadStream();
                                var docMetadata = await _minioService.UploadFileAsync(
                                    docStream,
                                    doc.File.Name,
                                    "docs",
                                    doc.File.ContentType ?? "application/pdf",
                                    doc.Description,
                                    $"worker-{worker.Id}/training-{trainingInput.TrainingId}",
                                    doc.IsPublic,
                                    doc.ExpiresAt);

                                if (docMetadata != null)
                                {
                                    var documentFile = new DocumentFile
                                    {
                                        Name = doc.Name,
                                        FileMetadataId = docMetadata.Id,
                                        CreatedAt = DateTime.UtcNow,
                                        CreatedBy = "System"
                                    };
                                    context.DocumentFiles.Add(documentFile);
                                    workerTraining.DocumentFiles.Add(documentFile);
                                }
                                else
                                {
                                    throw new GraphQLException(new Error(
                                        "Validation",
                                        "Failed to upload training document")
                                    );
                                }
                            }
                        }
                    }
                }

                // Save all changes including file metadata
                await context.SaveChangesAsync();

                // Commit the transaction - everything succeeded
                await transaction.CommitAsync();

                return await _workerService.GetWorkerByIdAsync(worker.Id) ?? worker;
            }
            catch (GraphQLException)
            {
                // Rollback transaction and re-throw GraphQL exceptions
                await transaction.RollbackAsync();
                throw;
            }
            catch (Exception ex)
            {
                // Rollback transaction on any other exception
                await transaction.RollbackAsync();
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        [AuthorizePermission("Workers", "Update", "Site")]
        public async Task<Worker?> UpdateWorker(
                UpdateWorkerInput input
               )
        {
            int id = input.Id;
            string? name = input.Name;
            string? company = input.Company;
            DateOnly? dateOfBirth = input.DateOfBirth;
            List<int>? trainingIds = input.TrainingIds;
            List<int>? tradeIds = input.TradeIds;
            List<int>? skillIds = input.SkillIds;
            int? manHours = input.ManHours;
            double? rating = input.Rating;
            string? gender = input.Gender;
            string? phoneNumber = input.PhoneNumber;
            string? mpesaNumber = input.MpesaNumber;
            string? email = input.Email;
            DateTime? inductionDate = input.InductionDate;
            DateTime? medicalCheckDate = input.MedicalCheckDate;
            IFile? profilePicture = input.ProfilePicture;
            IFile? signature = input.Signature;
            List<GraphQLApi.GraphQL.Types.DocumentFileInput>? documents = input.Documents;

            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            // Use a single database context for transaction management
            using var context = await _contextFactory.CreateDbContextAsync();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                var updatedWorker = new Worker
                {
                    Id = existingWorker.Id,
                    Name = name ?? existingWorker.Name,
                    Company = company ?? existingWorker.Company,
                    DateOfBirth = dateOfBirth ?? existingWorker.DateOfBirth,
                    ManHours = manHours ?? existingWorker.ManHours,
                    Rating = rating ?? existingWorker.Rating,
                    Gender = gender ?? existingWorker.Gender,
                    NationalId = existingWorker.NationalId,
                    PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                    MpesaNumber = mpesaNumber ?? existingWorker.MpesaNumber,
                    Email = email ?? existingWorker.Email,
                    InductionDate = inductionDate ?? existingWorker.InductionDate,
                    MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
                };

                // Update basic worker information
                var result = await _workerService.UpdateWorkerAsync(id, updatedWorker);

                // Update relationships if specified using the relationship service
                if (trainingIds != null || tradeIds != null || skillIds != null)
                {
                    await _relationshipService.UpdateWorkerRelationshipsAsync(id, trainingIds, tradeIds, skillIds);
                }

                // Handle file uploads
                var workerToUpdate = await context.Workers.FindAsync(id);
                if (workerToUpdate != null)
                {
                    // Upload profile picture
                    if (profilePicture != null)
                    {
                        using var profileStream = profilePicture.OpenReadStream();
                        var profileMetadata = await _minioService.UploadFileAsync(
                            profileStream,
                            $"profile-picture.{GetFileExtensionFromName(profilePicture.Name)}",
                            "profile-picture",
                            profilePicture.ContentType ?? "image/jpeg",
                            "Worker profile picture",
                            $"worker-{id}",
                            false,
                            null);

                        if (profileMetadata != null)
                        {
                            workerToUpdate.ProfilePictureFileId = profileMetadata.Id;
                        }
                    }

                    // Upload signature
                    if (signature != null)
                    {
                        using var signatureStream = signature.OpenReadStream();
                        var signatureMetadata = await _minioService.UploadFileAsync(
                            signatureStream,
                            signature.Name,
                            "signatures",
                            signature.ContentType ?? "image/png",
                            "Worker signature",
                            $"worker-{id}",
                            false,
                            null);

                        if (signatureMetadata != null)
                        {
                            workerToUpdate.SignatureFileId = signatureMetadata.Id;
                        }
                    }

                    // Upload documents
                    if (documents != null && documents.Count != 0)
                    {
                        foreach (var doc in documents)
                        {
                            using var docStream = doc.File.OpenReadStream();
                            var docMetadata = await _minioService.UploadFileAsync(
                                docStream,
                                doc.File.Name,
                                "docs",
                                doc.File.ContentType ?? "application/pdf",
                                doc.Description,
                                $"worker-{id}",
                                doc.IsPublic,
                                doc.ExpiresAt);

                            if (docMetadata != null)
                            {
                                var documentFile = new DocumentFile
                                {
                                    Name = doc.Name,
                                    FileMetadataId = docMetadata.Id,
                                    CreatedAt = DateTime.UtcNow,
                                    CreatedBy = "System" // TODO: Get from current user context
                                };

                                context.DocumentFiles.Add(documentFile);
                                workerToUpdate.DocumentFiles.Add(documentFile);
                                // Set discriminator fields for polymorphic relationship
                            }
                        }
                    }

                    await context.SaveChangesAsync();
                }

                // Commit the transaction - everything succeeded
                await transaction.CommitAsync();

                var finalWorker = await _workerService.GetWorkerByIdAsync(id);
                if (finalWorker == null)
                {
                    throw new InvalidOperationException($"Worker with id {id} was not found after update. It may have been deleted during the update process.");
                }
                return finalWorker;
            }
            catch (GraphQLException)
            {
                // Rollback transaction and re-throw GraphQL exceptions
                await transaction.RollbackAsync();
                throw;
            }
            catch (Exception ex)
            {
                // Rollback transaction on any other exception
                await transaction.RollbackAsync();
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }
        public async Task<Worker?> UpdateWorkerWithTraining(
                    UpdateWorkerWithTrainingInput input
                   )
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var existingWorker = await _workerService.GetWorkerByIdAsync(input.Id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = input.Name ?? existingWorker.Name,
                Company = input.Company ?? existingWorker.Company,
                DateOfBirth = input.DateOfBirth ?? existingWorker.DateOfBirth,
                ManHours = input.ManHours ?? existingWorker.ManHours,
                Rating = input.Rating ?? existingWorker.Rating,
                Gender = input.Gender ?? existingWorker.Gender,
                NationalId = input.NationalId ?? existingWorker.NationalId,
                PhoneNumber = input.PhoneNumber ?? existingWorker.PhoneNumber,
                MpesaNumber = input.MpesaNumber ?? existingWorker.MpesaNumber,
                Email = input.Email ?? existingWorker.Email,
                InductionDate = input.InductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = input.MedicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            var result = await _workerService.UpdateWorkerAsync(input.Id, updatedWorker);
            if (input.TradeIds != null || input.SkillIds != null)
            {
                await _relationshipService.UpdateWorkerRelationshipsAsync(input.Id, [], input.TradeIds, input.SkillIds);
            }

            if (input.Trainings != null && input.Trainings.Count != 0)
            {
                foreach (var training in input.Trainings)
                {
                    WorkerTraining? workerTraining = null;
                    if (existingWorker.WorkerTrainings.Any(wt => wt.TrainingId == training.TrainingId))
                    {
                        workerTraining = existingWorker.WorkerTrainings.First(wt => wt.TrainingId == training.TrainingId);
                        workerTraining.Notes = training.Notes;

                    }
                    else
                    {
                        workerTraining = await _workerTrainingService.CreateWorkerTrainingAsync(existingWorker.Id, training.TrainingId, training.Notes);
                    }

                    if (training.Documents != null && training.Documents.Count != 0)
                    {
                        foreach (var doc in training.Documents)
                        {
                            using var docStream = doc.File.OpenReadStream();
                            var docMetadata = await _minioService.UploadFileAsync(
                                docStream,
                                doc.File.Name,
                                "docs",
                                doc.File.ContentType ?? "application/pdf",
                                doc.Description,
                                $"worker-{input.Id}/training-{training.TrainingId}",
                                doc.IsPublic,
                                doc.ExpiresAt);
                            if (docMetadata != null)
                            {
                                var documentFile = new DocumentFile
                                {
                                    Name = doc.Name,
                                    FileMetadataId = docMetadata.Id,
                                    CreatedAt = DateTime.UtcNow,
                                    CreatedBy = "System"
                                };
                                context.DocumentFiles.Add(documentFile);
                                workerTraining.DocumentFiles.Add(documentFile);
                            }
                        }
                    }
                }
            }
            // Handle file uploads
            if (input.ProfilePicture != null)
            {
                using var profileStream = input.ProfilePicture.OpenReadStream();
                var profileMetadata = await _minioService.UploadFileAsync(
                    profileStream,
                    // input.ProfilePicture.Name,
                    $"profile-picture.{GetFileExtensionFromName(input.ProfilePicture.Name)}",
                    "profile-picture",
                    input.ProfilePicture.ContentType ?? "image/jpeg",
                    "Worker profile picture",
                    $"worker-{input.Id}",
                    false,
                    null);
                if (profileMetadata != null)
                {
                    existingWorker.ProfilePictureFileId = profileMetadata.Id;
                }
            }

            if (input.Signature != null)
            {
                using var signatureStream = input.Signature.OpenReadStream();
                var signatureMetadata = await _minioService.UploadFileAsync(
                    signatureStream,
                    input.Signature.Name,
                    "signatures",
                    input.Signature.ContentType ?? "image/png",
                    "Worker signature",
                    $"worker-{input.Id}",
                    false,
                    null);
                if (signatureMetadata != null)
                {
                    existingWorker.SignatureFileId = signatureMetadata.Id;
                }
            }
            if (input.Documents != null && input.Documents.Count != 0)
            {
                foreach (var doc in input.Documents)
                {
                    using var docStream = doc.File.OpenReadStream();
                    var docMetadata = await _minioService.UploadFileAsync(
                        docStream,
                        doc.File.Name,
                        "docs",
                        doc.File.ContentType ?? "application/pdf",
                        doc.Description,
                        $"worker-{input.Id}",
                        doc.IsPublic,
                        doc.ExpiresAt);
                    if (docMetadata != null)
                    {
                        var documentFile = new DocumentFile
                        {
                            Name = doc.Name,
                            FileMetadataId = docMetadata.Id,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "System"
                        };
                        context.DocumentFiles.Add(documentFile);
                        existingWorker.DocumentFiles.Add(documentFile);
                    }
                }
            }
            await context.SaveChangesAsync();
            var finalWorker = await _workerService.GetWorkerByIdAsync(input.Id);
            if (finalWorker == null)
            {
                throw new InvalidOperationException($"Worker with id {input.Id} was not found after update. It may have been deleted during the update process.");
            }
            return finalWorker;
        }
        [AuthorizePermission("Workers", "Delete", "Site")]
        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }

        // Training Mutations
        [AuthorizePermission("Trainings", "Create", "Site")]
        public async Task<LegacyTraining> CreateTraining(
            string name,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus status = TrainingStatus.Scheduled
            )
        {

            try
            {
                // Validate duration format if provided
                if (!string.IsNullOrEmpty(duration))
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }

                var training = new LegacyTraining
                {
                    Name = name,
                    Description = description,
                    StartDate = startDate,
                    EndDate = endDate,
                    Duration = duration,
                    ValidityPeriodMonths = validityPeriodMonths,
                    TrainingType = trainingType,
                    Trainer = trainer,
                    Frequency = frequency,
                    Status = status
                };

                var createdTraining = await _trainingService.CreateTrainingAsync(training);

                return await _trainingService.GetTrainingByIdAsync(createdTraining.Id) ?? createdTraining;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        [AuthorizePermission("Trainings", "Update", "Site")]
        public async Task<LegacyTraining?> UpdateTraining(
            int id,
            string? name = null,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus? status = null
            )
        {
            var existingTraining = await _trainingService.GetTrainingByIdAsync(id);
            if (existingTraining == null)
            {
                throw new GraphQLException(new Error(
                    "NotFound",
                    $"Training with ID {id} not found.")
                );
            }

            // Validate duration format if provided
            if (duration != null)
            {
                if (string.IsNullOrEmpty(duration))
                {
                    duration = null;
                }
                else
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }
            }

            var updatedTraining = new LegacyTraining
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
                Description = description ?? existingTraining.Description,
                StartDate = startDate ?? existingTraining.StartDate,
                EndDate = endDate ?? existingTraining.EndDate,
                Duration = duration ?? existingTraining.Duration,
                ValidityPeriodMonths = validityPeriodMonths ?? existingTraining.ValidityPeriodMonths,
                TrainingType = trainingType ?? existingTraining.TrainingType,
                Trainer = trainer ?? existingTraining.Trainer,
                Frequency = frequency ?? existingTraining.Frequency,
                Status = status ?? existingTraining.Status
            };

            var result = await _trainingService.UpdateTrainingAsync(id, updatedTraining);

            // Handle document uploads

            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<bool> DeleteTraining(int id)
        {
            return await _trainingService.DeleteTrainingAsync(id);
        }

        public async Task<int> AssignTrainingToWorkersByTrade(
            int trainingId,
            List<int> tradeIds)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                // Get the training
                var training = await context.Trainings
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == trainingId);

                if (training == null)
                {
                    throw new GraphQLException(new Error(
                        "NotFound",
                        $"Training with ID {trainingId} not found.")
                    );
                }

                // Get all workers with the specified trades
                var workersToAssign = await context.Workers
                    .Include(w => w.Trades)
                    .Where(w => w.Trades.Any(t => tradeIds.Contains(t.Id)))
                    .ToListAsync();

                int assignedCount = 0;
                foreach (var worker in workersToAssign)
                {
                    // Only add if not already assigned
                    if (!training.Workers.Contains(worker))
                    {
                        training.Workers.Add(worker);
                        assignedCount++;
                    }
                }

                await context.SaveChangesAsync();
                return assignedCount;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<WorkerTrainingHistory> CompleteTraining(
            int workerId,
            int trainingId,
            DateTime? completionDate = null,
            decimal? score = null,
            string? notes = null)
        {
            var actualCompletionDate = completionDate ?? DateTime.UtcNow;
            return await _trainingStatusService.CompleteTrainingAsync(workerId, trainingId, actualCompletionDate, score, notes);
        }

        public async Task<bool> UpdateTrainingStatuses()
        {
            try
            {
                await _trainingStatusService.UpdateTrainingStatusesAsync();
                await _trainingStatusService.UpdateTrainingHistoryStatusesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while updating training statuses.");
                return false;
            }
        }

        // Trade Mutations
        public async Task<Trade> CreateTrade(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var trade = new Trade
                {
                    Name = name,
                    Description = description
                };

                var createdTrade = await _tradeService.CreateTradeAsync(trade);

                // Add workers if specified
                if (workerIds.Count != 0)
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var tradeEntity = await context.Trades
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTrade.Id);

                    if (tradeEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                tradeEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _tradeService.GetTradeByIdAsync(createdTrade.Id) ?? createdTrade;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Trade?> UpdateTrade(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingTrade = await _tradeService.GetTradeByIdAsync(id);
            if (existingTrade == null)
                return null;

            var updatedTrade = new Trade
            {
                Id = existingTrade.Id,
                Name = name ?? existingTrade.Name,
                Description = description ?? existingTrade.Description
            };

            var result = await _tradeService.UpdateTradeAsync(id, updatedTrade, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var tradeEntity = await context.Trades
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (tradeEntity != null)
                {
                    // Clear existing workers
                    tradeEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            tradeEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<bool> DeleteTrade(int id)
        {
            return await _tradeService.DeleteTradeAsync(id);
        }

        // Skill Mutations
        public async Task<Skill> CreateSkill(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var skill = new Skill
                {
                    Name = name,
                    Description = description
                };

                var createdSkill = await _skillService.CreateSkillAsync(skill);

                // Add workers if specified
                if (workerIds.Count != 0)
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var skillEntity = await context.Skills
                        .Include(s => s.Workers)
                        .FirstOrDefaultAsync(s => s.Id == createdSkill.Id);

                    if (skillEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                skillEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _skillService.GetSkillByIdAsync(createdSkill.Id) ?? createdSkill;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Skill?> UpdateSkill(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingSkill = await _skillService.GetSkillByIdAsync(id);
            if (existingSkill == null)
                return null;

            var updatedSkill = new Skill
            {
                Id = existingSkill.Id,
                Name = name ?? existingSkill.Name,
                Description = description ?? existingSkill.Description
            };

            var result = await _skillService.UpdateSkillAsync(id, updatedSkill, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var skillEntity = await context.Skills
                    .Include(s => s.Workers)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (skillEntity != null)
                {
                    // Clear existing workers
                    skillEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            skillEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _skillService.GetSkillByIdAsync(id);
        }

        public async Task<bool> DeleteSkill(int id)
        {
            return await _skillService.DeleteSkillAsync(id);
        }

        // Equipment Mutations
        [AuthorizePermission("PPE", "Create", "Site")]
        public async Task<Equipment> CreateEquipment(
            string name,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            try
            {
                var equipment = new Equipment
                {
                    Name = name,
                    Description = description,
                    SerialNumber = serialNumber,
                    Model = model,
                    Manufacturer = manufacturer,
                    PurchaseDate = purchaseDate,
                    LastMaintenanceDate = lastMaintenanceDate,
                    NextMaintenanceDate = nextMaintenanceDate,
                    Location = location,
                    Status = status ?? "Available",
                    PurchasePrice = purchasePrice,
                    Category = category
                };

                return await _equipmentService.CreateEquipmentAsync(equipment);
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        [AuthorizePermission("PPE", "Update", "Site")]
        public async Task<Equipment?> UpdateEquipment(
            int id,
            string? name = null,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            var existingEquipment = await _equipmentService.GetEquipmentByIdAsync(id);
            if (existingEquipment == null)
                return null;

            var updatedEquipment = new Equipment
            {
                Id = existingEquipment.Id,
                Name = name ?? existingEquipment.Name,
                Description = description ?? existingEquipment.Description,
                SerialNumber = serialNumber ?? existingEquipment.SerialNumber,
                Model = model ?? existingEquipment.Model,
                Manufacturer = manufacturer ?? existingEquipment.Manufacturer,
                PurchaseDate = purchaseDate ?? existingEquipment.PurchaseDate,
                LastMaintenanceDate = lastMaintenanceDate ?? existingEquipment.LastMaintenanceDate,
                NextMaintenanceDate = nextMaintenanceDate ?? existingEquipment.NextMaintenanceDate,
                Location = location ?? existingEquipment.Location,
                Status = status ?? existingEquipment.Status,
                PurchasePrice = purchasePrice ?? existingEquipment.PurchasePrice,
                Category = category ?? existingEquipment.Category
            };

            return await _equipmentService.UpdateEquipmentAsync(id, updatedEquipment);
        }

        public async Task<bool> DeleteEquipment(int id)
        {
            return await _equipmentService.DeleteEquipmentAsync(id);
        }

        // // File Upload Mutations
        public async Task<GraphQLApi.GraphQL.Types.FileUploadResponse> UploadFile(
            GraphQLApi.GraphQL.Types.FileUploadInput input)
        {
            try
            {
                // Validate and convert bucket name string to enum
                if (!Enum.TryParse<Shared.Enums.BucketName>(input.BucketName, true, out var bucketName))
                {
                    return new GraphQLApi.GraphQL.Types.FileUploadResponse
                    {
                        Success = false,
                        ErrorMessage = $"Invalid bucket name: {input.BucketName}. Valid values are: {string.Join(", ", Enum.GetNames<Shared.Enums.BucketName>())}"
                    };
                }

                using var stream = input.File.OpenReadStream();

                var fileMetadata = await _minioService.UploadFileAsync(
                    stream,
                    input.File.Name,
                    // bucketName.ToString().ToLowerInvariant(),
                    "docs",
                    input.File.ContentType ?? "unkown/unkown",
                    input.Description,
                    input.FolderPath,
                    input.IsPublic,
                    input.ExpiresAt);

                // Update additional metadata if provided
                if (!string.IsNullOrEmpty(input.AdditionalMetadata) && fileMetadata != null)
                {
                    using var context = await _contextFactory.CreateDbContextAsync();
                    var dbFileMetadata = await context.FileMetadata.FindAsync(fileMetadata.Id);
                    if (dbFileMetadata != null)
                    {
                        dbFileMetadata.AdditionalMetadata = input.AdditionalMetadata;
                        await context.SaveChangesAsync();
                        fileMetadata.AdditionalMetadata = input.AdditionalMetadata;
                    }
                }

                // Generate presigned URL if requested or if file is public
                string? presignedUrl = null;
                if (input.IsPublic && fileMetadata != null)
                {
                    presignedUrl = await _minioService.GetPresignedUrlAsync(fileMetadata, 3600); // 1 hour expiration
                }

                return new GraphQLApi.GraphQL.Types.FileUploadResponse
                {
                    Success = fileMetadata != null,
                    FileMetadata = fileMetadata,
                    PresignedUrl = presignedUrl,
                    ErrorMessage = fileMetadata == null ? "Failed to upload file" : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName}", input.File.Name);
                return new GraphQLApi.GraphQL.Types.FileUploadResponse
                {
                    Success = false,
                    ErrorMessage = "An error occurred while uploading the file"
                };
            }
        }

        public async Task<GraphQLApi.GraphQL.Types.PresignedUrlResponse> GetPresignedUrl(
            GraphQLApi.GraphQL.Types.PresignedUrlInput input)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == input.FileId && !f.IsDeleted);

                if (fileMetadata == null)
                {
                    return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                    {
                        Success = false,
                        ErrorMessage = "File not found"
                    };
                }

                var presignedUrl = await _minioService.GetPresignedUrlAsync(
                    fileMetadata,
                    input.ExpirationMinutes * 60); // Convert minutes to seconds

                return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                {
                    Success = !string.IsNullOrEmpty(presignedUrl),
                    Url = presignedUrl,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(input.ExpirationMinutes),
                    ErrorMessage = string.IsNullOrEmpty(presignedUrl) ? "Failed to generate presigned URL" : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned URL for file {FileId}", input.FileId);
                return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                {
                    Success = false,
                    ErrorMessage = "An error occurred while generating the presigned URL"
                };
            }
        }

        public async Task<bool> DeleteFile(int fileId)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

                if (fileMetadata == null)
                {
                    return false;
                }

                var success = await _minioService.DeleteFileAsync(
                    fileMetadata.BucketName,
                    fileMetadata.ObjectKey);

                if (success)
                {
                    // Soft delete the metadata record
                    fileMetadata.IsDeleted = true;
                    fileMetadata.DeletedAt = DateTime.UtcNow;
                    fileMetadata.DeletedBy = "System"; // TODO: Get current user

                    await context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {FileId}", fileId);
                return false;
            }
        }

        // Job Mutations
        public async Task<Job> CreateJob(CreateJobInput input)
        {
            try
            {
                var createdJob = await _jobService.CreateJobAsync(input.Title, input.ChiefEngineerId, input.Location, input.TradeIds, input.TimeForCompletion, input.StartDate, input.Description);
                return createdJob;
            }
            catch (MultipleValidationException ex)
            {
                throw new GraphQLException(ex.Errors.Select(e => new Error("Validation", e.ErrorMessage)));
            }
        }
        public async Task<Job[]> CreateJobs(List<CreateJobInput> inputs)
        {
            return await Task.WhenAll(inputs.Select(CreateJob));
        }

        public async Task<Job> BlockJob(BlockJobInput input)
        {
            var job = await _jobService.BlockJobAsync(input.JobId, input.BlockedById);
            return job;
        }
        public async Task<Job[]> BlockJobs(List<BlockJobInput> inputs)
        {
            return await Task.WhenAll(inputs.Select(BlockJob));
        }
        public async Task<Job> ReviewJob(ReviewJobInput input)
        {
            try
            {
                var job = await _jobService.ReviewJobAsync(input.JobId,
                    input.ReviewedById,
                    input.Hazards,
                    input.RequiredPermits,
                    input.PPEs.ToList(),
                    input.PrecautionsRequired,
                    input.ModesOfAccessToBeUsed,
                    input.TrainingIds,
                    input.FireExtinguishers,
                    input.ExcavationProtectionSystems,
                    input.DepthOfExcavation,
                    input.ExcavationEquipmentsToBeUsed,
                    input.NatureOfHotWork,
                    input.Documents
                    );
                return job;
            }
            catch (MultipleValidationException ex)
            {
                throw new GraphQLException(ex.Errors.Select(e => new Error("Validation", e.ErrorMessage)));
            }
        }
        public async Task<Job[]> ReviewJobs(List<ReviewJobInput> inputs)
        {
            try
            {
                return await Task.WhenAll(inputs.Select(ReviewJob));
            }
            catch (MultipleValidationException ex)
            {
                // throw new GraphQLException(new Error("Validation", ex.Message));
                throw new GraphQLException(ex.Errors.Select(e => new Error("Validation", e.ErrorMessage)));
            }
        }

        public async Task<Job> ApproveJob(ApproveJobInput input)
        {
            var job = await _jobService.ApproveJobAsync(input.JobId, input.ApprovedById);
            return job;
        }
        public async Task<Job[]> ApproveJobs(List<ApproveJobInput> inputs)
        {
            return await Task.WhenAll(inputs.Select(ApproveJob));
        }

        public async Task<Job> DisapproveJob(DisapproveJobInput input)
        {
            var job = await _jobService.DisapproveJobAsync(input.JobId, input.DisapprovedById);
            return job;
        }
        public async Task<Job[]> DisapproveJobs(List<DisapproveJobInput> inputs)
        {
            return await Task.WhenAll(inputs.Select(DisapproveJob));
        }
        public async Task<Job> FinishJob(FinishJobInput input)
        {
            var job = await _jobService.FinishJobAsync(input.JobId, input.FinishedById);
            return job;
        }
        public async Task<Job[]> FinishJobs(List<FinishJobInput> inputs)
        {
            return await Task.WhenAll(inputs.Select(FinishJob));
        }
        public async Task<Job?> GetJob(int jobId)
        {
            var job = await _jobService.GetJobByIdAsync(jobId);
            return job;
        }
        // Toolbox Mutations
        public async Task<Toolbox> CreateToolbox(Types.CreateToolboxInput input)
        {
            var serviceInput = new Services.CreateToolboxInput
            {
                ConductorId = input.ConductorId,
                EmergencyProcedures = string.Join(", ", input.EmergencyProcedures),
                ToolboxTrainingTopics = string.Join(", ", input.ToolboxTrainingTopics),
                AttendeePictureFile = input.AttendeePictureFile,
                Jobs = input.Jobs.Select(j => new Services.CreateToolboxJobInput
                {
                    JobId = j.JobId,
                    ExistingHazards = j.ExistingHazards.Select(eh => new Services.CreateToolboxExistingHazardInput
                    {
                        Id = eh.Id,
                        Description = eh.Description,
                        ExistingControlMeasures = eh.ExistingControlMeasures.Select(ecm => new Services.CreateToolboxExistingControlMeasureInput
                        {
                            Id = ecm.Id,
                            Description = ecm.Description
                        }),
                        NewControlMeasures = eh.NewControlMeasures.Select(ncm => new Services.CreateToolboxNewControlMeasureInput
                        {
                            Description = ncm.Description
                        })
                    }),
                    NewHazards = j.NewHazards.Select(nh => new Services.CreateToolboxNewHazardInput
                    {
                        Description = nh.Description,
                        ControlMeasures = nh.ControlMeasures.Select(cm => new Services.CreateToolboxNewControlMeasureInput
                        {
                            Description = cm.Description
                        })
                    })
                })
            };

            return await _toolboxService.CreateToolboxAsync(serviceInput);
        }

        public async Task<bool> AddAttendees(int toolboxId, List<int> workerIds)
        {
            await _toolboxService.AddAttendeesAsync(toolboxId, workerIds);
            return true;
        }

        public async Task<bool> SummarizeToolbox(GraphQLApi.GraphQL.Types.SummarizeToolboxInput input)
        {
            var jobs = input.Jobs.Select(j => new Services.CreateToolboxJobInput
            {
                JobId = j.JobId,
                ExistingHazards = j.ExistingHazards.Select(eh => new Services.CreateToolboxExistingHazardInput
                {
                    Id = eh.Id,
                    Description = eh.Description,
                    ExistingControlMeasures = eh.ExistingControlMeasures.Select(ecm => new Services.CreateToolboxExistingControlMeasureInput
                    {
                        Id = ecm.Id,
                        Description = ecm.Description
                    }),
                    NewControlMeasures = eh.NewControlMeasures.Select(ncm => new Services.CreateToolboxNewControlMeasureInput
                    {
                        Description = ncm.Description
                    })
                }),
                NewHazards = j.NewHazards.Select(nh => new Services.CreateToolboxNewHazardInput
                {
                    Description = nh.Description,
                    ControlMeasures = nh.ControlMeasures.Select(cm => new Services.CreateToolboxNewControlMeasureInput
                    {
                        Description = cm.Description
                    })
                })
            });

            await _toolboxService.SummarizeToolboxAsync(input.ToolboxId, jobs);
            return true;
        }

        public async Task<bool> AddHazard(GraphQLApi.GraphQL.Types.AddHazardInput input)
        {
            var serviceInput = new Services.AddHazardInput
            {
                JobId = input.JobId,
                Description = input.Description,
                ControlMeasures = input.ControlMeasures
            };

            await _toolboxService.AddHazardAsync(serviceInput);
            return true;
        }

        public async Task<bool> AddControlMeasure(GraphQLApi.GraphQL.Types.AddControlMeasureInput input)
        {
            var serviceInput = new Services.AddControlMeasureInput
            {
                HazardId = input.HazardId,
                Description = input.Description
            };

            await _toolboxService.AddControlMeasureAsync(serviceInput);
            return true;
        }

        // Permit Mutations
        public async Task<GeneralWorkPermit> CreateGeneralWorkPermit(Types.CreateGeneralWorkPermitInput input)
        {
            var serviceInput = new Services.CreateGeneralWorkPermitInput
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                Isolation = input.Isolation,
                Inspections = input.Inspections,
                AttendancePictureFile = input.AttendancePictureFile,
                PermitIssuer = new Services.PermitIssuerInput
                {
                    CompetentPersonIds = input.PermitIssuer.CompetentPersonIds,
                    AuthorisedPersonsIds = input.PermitIssuer.AuthorisedPersonsIds
                },
                SignOff = new Services.SignOffInput
                {
                    WorkerIds = input.SignOff.WorkerIds
                }
            };

            return await _permitService.CreateGeneralWorkPermitAsync(serviceInput);
        }

        public async Task<HotWorkPermit> CreateHotWorkPermit(Types.CreateHotWorkPermitInput input)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var serviceInput = new Services.CreateHotWorkPermitInput
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                Isolation = input.Isolation,
                Inspections = input.Inspections,
                AttendancePictureFile = input.AttendancePictureFile,
                FireSafetySupervisorId = input.FireSafetySupervisorId,
                PermitIssuer = new Services.PermitIssuerInput
                {
                    CompetentPersonIds = input.PermitIssuer.CompetentPersonIds,
                    AuthorisedPersonsIds = input.PermitIssuer.AuthorisedPersonsIds
                },
                SignOff = new Services.SignOffInput
                {
                    WorkerIds = input.SignOff.WorkerIds
                }
            };

            return await _permitService.CreateHotWorkPermitAsync(serviceInput);
        }

        public async Task<ExcavationWorkPermit> CreateExcavationWorkPermit(Types.CreateExcavationWorkPermitInput input)
        {
            var serviceInput = new Services.CreateExcavationWorkPermitInput
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                Inspections = input.Inspections,
                Isolation = input.Isolation,
                AttendancePictureFile = input.AttendancePictureFile,
                InspectionAuthorization = new Services.InspectionAuthorizationInput
                {
                    NameOfInspector = input.InspectionAuthorization.NameOfInspector,
                    Designation = input.InspectionAuthorization.Designation,
                    DateOfInspection = input.InspectionAuthorization.DateOfInspection,
                    Comments = input.InspectionAuthorization.Comments
                },
                PermitIssuer = new Services.PermitIssuerInput
                {
                    CompetentPersonIds = input.PermitIssuer.CompetentPersonIds,
                    AuthorisedPersonsIds = input.PermitIssuer.AuthorisedPersonsIds
                },
                SignOff = new Services.SignOffInput
                {
                    WorkerIds = input.SignOff.WorkerIds
                }
            };

            return await _permitService.CreateExcavationWorkPermitAsync(serviceInput);
        }

        public async Task<WorkAtHeightPermit> CreateWorkAtHeightPermit(GraphQLApi.GraphQL.Types.CreateWorkAtHeightPermitInput input)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var serviceInput = new Services.CreateWorkAtHeightPermitInput
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                Inspections = input.Inspections,
                Isolation = input.Isolation,
                AttendancePictureFile = input.AttendancePictureFile,
                InspectionAuthorization = new Services.InspectionAuthorizationInput
                {
                    NameOfInspector = input.InspectionAuthorization.NameOfInspector,
                    Designation = input.InspectionAuthorization.Designation,
                    DateOfInspection = input.InspectionAuthorization.DateOfInspection,
                    Comments = input.InspectionAuthorization.Comments
                },
                PermitIssuer = new Services.PermitIssuerInput
                {
                    CompetentPersonIds = input.PermitIssuer.CompetentPersonIds,
                    AuthorisedPersonsIds = input.PermitIssuer.AuthorisedPersonsIds
                },
                SignOff = new Services.SignOffInput
                {
                    WorkerIds = input.SignOff.WorkerIds
                }
            };

            return await _permitService.CreateWorkAtHeightPermitAsync(serviceInput);
        }

        public async Task<ConfinedSpacePermit> CreateConfinedSpacePermit(GraphQLApi.GraphQL.Types.CreateConfinedSpacePermitInput input)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var serviceInput = new Services.CreateConfinedSpacePermitInput
            {
                JobId = input.JobId,
                PTWRefNumber = input.PTWRefNumber,
                ProjectName = input.ProjectName,
                Inspections = input.Inspections,
                Isolation = input.Isolation,
                WorkersHaveBeenTrained = input.WorkersHaveBeenTrained,
                NameOfTrainingOrganization = input.NameOfTrainingOrganization,
                AttendancePictureFile = input.AttendancePictureFile,
                TopReading = new Services.AtmosphericReadingInput
                {
                    Oxygen = input.TopReading.Oxygen,
                    Explosive = input.TopReading.Explosive,
                    Toxic = input.TopReading.Toxic,
                    Co2 = input.TopReading.Co2
                },
                MidReading = new Services.AtmosphericReadingInput
                {
                    Oxygen = input.MidReading.Oxygen,
                    Explosive = input.MidReading.Explosive,
                    Toxic = input.MidReading.Toxic,
                    Co2 = input.MidReading.Co2
                },
                BottomReading = new Services.AtmosphericReadingInput
                {
                    Oxygen = input.BottomReading.Oxygen,
                    Explosive = input.BottomReading.Explosive,
                    Toxic = input.BottomReading.Toxic,
                    Co2 = input.BottomReading.Co2
                },
                EmergencyGuidelines = input.EmergencyGuidelines,
                TaskObserver = new Services.TaskObserverInput
                {
                    WorkerId = input.TaskObserver.WorkerId,
                    Name = input.TaskObserver.Name,
                    SignatureFileId = input.TaskObserver.SignatureFileId,
                    SignedAt = input.TaskObserver.SignedAt
                },
                PermitIssuer = new Services.PermitIssuerInput
                {
                    CompetentPersonIds = input.PermitIssuer.CompetentPersonIds,
                    AuthorisedPersonsIds = input.PermitIssuer.AuthorisedPersonsIds
                },
                SignOff = new Services.SignOffInput
                {
                    WorkerIds = input.SignOff.WorkerIds
                }
            };

            return await _permitService.CreateConfinedSpacePermitAsync(serviceInput);
        }

        public async Task<Permit> CancelPermit(int permitId)
        {
            return await _permitService.CancelPermitAsync(permitId);
        }

        public async Task<Permit> ReturnPermit(ReturnPermitInput input)
        {
            var serviceInput = new Services.PermitReturnInput
            {
                CompetentPersons = input.PermitReturn.CompetentPersons.Select(cp => new Services.CompetentPersonInput
                {
                    WorkerId = cp.WorkerId,
                    Name = cp.Name,
                    SignatureFileId = cp.SignatureFileId,
                    SignedAt = cp.SignedAt
                }).ToList(),
                AuthorisedPersons = input.PermitReturn.AuthorisedPersons.Select(ap => new Services.AuthorisedPersonInput
                {
                    WorkerId = ap.WorkerId,
                    Name = ap.Name,
                    SignatureFileId = ap.SignatureFileId,
                    SignedAt = ap.SignedAt
                }).ToList()
            };

            return await _permitService.ReturnPermitAsync(input.PermitId, serviceInput);
        }

        public async Task<Permit> VoidPermit(int permitId)
        {
            return await _permitService.VoidPermitAsync(permitId);
        }

        public async Task<Permit> ClosePermit(int permitId)
        {
            return await _permitService.ClosePermitAsync(permitId);
        }



        // Inspection Mutations
        public async Task<Inspection> CreateInspection(CreateInspectionInput input)
        {
            try
            {
                var context = await _contextFactory.CreateDbContextAsync();

                var inspectionInput = new Shared.GraphQL.InputTypes.CreateInspectionInput
                {
                    Approved = input.Approved,
                    Comments = input.Comments,
                    InspectedById = input.InspectedById,
                    InspectionType = input.InspectionType,
                    InspectionItems = []
                };
                var i = await _inspectionService.CreateInspectionAsync(inspectionInput);
                // Console.WriteLine(i.Id);
                var inspection = await context.Inspections
                    .Include(i => i.InspectionItems)
                        .ThenInclude(ii => ii.ImageFiles)
                    .Include(i => i.SignatureFile)
                    .Include(i => i.InspectedBy)
                    .FirstOrDefaultAsync(item => item.Id == i.Id);

                if (inspection == null)
                    throw new Exception("Failed to create inspection");

                foreach (var item in input.InspectionItems)
                {
                    var inspectionItem = new InspectionItem
                    {
                        Description = item.Description,
                        IsTrue = item.IsTrue,
                        Remarks = item.Remarks ?? "",
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System"
                    };
                    inspection.InspectionItems.Add(inspectionItem);
                    await context.SaveChangesAsync();

                    if (item.ImageFiles != null && item.ImageFiles.Count != 0)
                    {
                        foreach (var file in item.ImageFiles)
                        {
                            using var stream = file.OpenReadStream();
                            var fileMetadata = await _minioService.UploadFileAsync(
                                stream,
                                file.Name,
                                Shared.Constants.FileStorageConstants.BucketNames.INSPECTIONS,
                                file.ContentType ?? "image/jpeg",
                                "Inspection item image",
                                $"inspections_{inspection.Id}/images_{inspectionItem.Id}",
                                false,
                                null);
                            if (fileMetadata != null)
                            {
                                // Add to inspection item images
                                inspectionItem.ImageFiles.Add(fileMetadata);
                            }
                        }

                    }
                }
                await context.SaveChangesAsync();
                return inspection;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inspection for worker {InspectorId}", input.InspectedById);
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        // Site Mutations
        [AuthorizePermission("Sites", "Create", "Company")]
        public async Task<Site> CreateSite(string name, SiteDataInput? siteData = null)
        {
            var siteDataDto = siteData?.ToDto();
            return await _siteService.CreateSiteAsync(name, siteDataDto);
        }

        [AuthorizePermission("Sites", "Update", "Site")]
        public async Task<Site?> UpdateSite(Guid id, string? name = null, SiteDataInput? siteData = null)
        {
            var siteDataDto = siteData?.ToDto();
            return await _siteService.UpdateSiteAsync(id, name, siteDataDto);
        }

        public async Task<Site?> UpdateSiteData(Guid id, SiteDataInput siteData)
        {
            var siteDataDto = siteData.ToDto();
            return await _siteService.UpdateSiteDataAsync(id, siteDataDto);
        }

        public async Task<Site?> PatchSiteData(Guid id, string jsonPatch)
        {
            return await _siteService.PatchSiteDataAsync(id, jsonPatch);
        }

        [AuthorizePermission("Sites", "Delete", "Company")]
        public async Task<bool> DeleteSite(Guid id)
        {
            return await _siteService.DeleteSiteAsync(id);
        }

        public async Task<Site?> CloneSite(Guid sourceId, string newName)
        {
            return await _siteService.CloneSiteAsync(sourceId, newName);
        }
    }
    public class UpdateWorkerInput
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Company { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<int>? TrainingIds { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public int? ManHours { get; set; }
        public double? Rating { get; set; }
        public string? Gender { get; set; }
        public string? PhoneNumber { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public IFile? ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<GraphQLApi.GraphQL.Types.DocumentFileInput>? Documents { get; set; }
    }
    public class BlockJobInput
    {
        public int JobId { get; set; }
        public int BlockedById { get; set; }
    }
    public class ApproveJobInput
    {
        public int JobId { get; set; }
        public int ApprovedById { get; set; }
    }
    public class DisapproveJobInput
    {
        public int JobId { get; set; }
        public int DisapprovedById { get; set; }
    }
    public class FinishJobInput
    {
        public int JobId { get; set; }
        public int FinishedById { get; set; }
    }
    public class CreateWorkerInput
    {
        public required string Name { get; set; }
        public required string Company { get; set; }
        public required string NationalId { get; set; }
        public required string Gender { get; set; }
        public required string PhoneNumber { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<int>? TrainingIds { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public IFile? ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<GraphQLApi.GraphQL.Types.DocumentFileInput>? Documents { get; set; }
    }

    public class CreateJobInput
    {
        public required string Title { get; set; }
        public List<int>? TradeIds { get; set; } = [];
        public int ChiefEngineerId { get; set; }
        public TimeSpan? TimeForCompletion { get; set; }
        public string Location { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public string? Description { get; set; }

    }
    public class ReviewJobInput
    {
        public int JobId { get; set; }
        public int ReviewedById { get; set; }
        public List<GraphQLApi.Services.HazardInput> Hazards { get; set; } = new();
        public List<PermitType> RequiredPermits { get; set; } = new();
        public IEnumerable<string> PPEs { get; set; } = [];
        public HashSet<string> PrecautionsRequired { get; set; } = [];
        public List<string>? ModesOfAccessToBeUsed { get; set; } = [];
        public List<int>? TrainingIds { get; set; } = [];
        public List<string>? FireExtinguishers { get; set; }
        public List<string>? ExcavationProtectionSystems { get; set; }
        public string? DepthOfExcavation { get; set; }
        public List<string>? ExcavationEquipmentsToBeUsed { get; set; }
        public List<string>? NatureOfHotWork { get; set; } = [];
        public List<GraphQLApi.GraphQL.Types.DocumentFileInput>? Documents { get; set; }
    }

    public class CreateInspectionInput
    {
        public List<CreateInspectionItem> InspectionItems { get; set; } = new();
        public bool Approved { get; set; }
        public string Comments { get; set; } = string.Empty;
        public int InspectedById { get; set; }
        public InspectionType InspectionType { get; set; } // Default value
    }

    public class CreateInspectionItem
    {
        public string Description { get; set; } = string.Empty;
        public bool IsTrue { get; set; }
        public string? Remarks { get; set; }
        public List<IFile> ImageFiles { get; set; } = [];

    }
}
