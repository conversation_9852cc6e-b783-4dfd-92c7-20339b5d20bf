using GraphQLApi.Services;
using HotChocolate;
using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;
using GraphQLApi.GraphQL.Types;

namespace GraphQLApi.GraphQL.Mutations
{
    [ExtendObjectType("Mutation")]
    public class TrainingMutations
    {
        // Training Session Mutations (following ToolboxService pattern)
        public async Task<TrainingSession> CreateTrainingSessionAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            GraphQL.Types.CreateTrainingSessionInput input)
        {
            var session = await trainingService.CreateSessionAsync(new Services.CreateTrainingSessionInput
            {
                ProgramId = input.ProgramId,
                ProviderId = input.ProviderId,
                ConductorId = input.ConductorId,
                SiteId = input.SiteId,
                Mode = input.Mode,
                Location = input.Location,
                StartDate = input.StartDate,
                EndDate = input.EndDate,
                Capacity = input.Capacity,
                Notes = input.Notes,
                SessionPictureFile = input.SessionPictureFile
            });

            return session;
        }

        public async Task<TrainingSession> UpdateTrainingSessionAsync(
            [Service] ITrainingService trainingService,
            UpdateTrainingSessionInput input)
        {
            if (input.Status.HasValue)
            {
                await trainingService.UpdateSessionStatusAsync(input.SessionId, input.Status.Value);
            }

            // For other updates, you'd implement UpdateSessionAsync in the service
            return await trainingService.GetSessionByIdAsync(input.SessionId) 
                ?? throw new ArgumentException("Session not found");
        }

        public async Task<TrainingSession> AddTrainingAttendeesAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            AddTrainingAttendeesInput input)
        {
            await trainingService.AddAttendeesAsync(input.SessionId, input.WorkerIds);

            // Send invitations to all added workers
            foreach (var workerId in input.WorkerIds)
            {
                await notificationService.SendSessionInvitationAsync(input.SessionId, workerId);
            }

            return await trainingService.GetSessionByIdAsync(input.SessionId) 
                ?? throw new ArgumentException("Session not found");
        }

        public async Task<TrainingSession> FinalizeTrainingSessionAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            FinalizeTrainingSessionInput input)
        {
            var attendanceInputs = input.Attendance.Select(a => new Services.TrainingAttendanceInput
            {
                WorkerId = a.WorkerId,
                Outcome = a.Outcome,
                Notes = a.Notes
            });

            await trainingService.FinalizeSessionAsync(input.SessionId, attendanceInputs);

            // Send completion notifications
            await notificationService.SendSessionCompletedNotificationAsync(input.SessionId);

            return await trainingService.GetSessionByIdAsync(input.SessionId) 
                ?? throw new ArgumentException("Session not found");
        }

        // Training Program Mutations
        public async Task<TrainingProgram> CreateTrainingProgramAsync(
            [Service] ITrainingService trainingService,
            CreateTrainingProgramInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("CreateTrainingProgramAsync not yet implemented in service");
        }

        public async Task<TrainingProgram> UpdateTrainingProgramAsync(
            [Service] ITrainingService trainingService,
            UpdateTrainingProgramInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("UpdateTrainingProgramAsync not yet implemented in service");
        }

        // Training Provider Mutations
        public async Task<TrainingProvider> CreateTrainingProviderAsync(
            [Service] ITrainingService trainingService,
            CreateTrainingProviderInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("CreateTrainingProviderAsync not yet implemented in service");
        }

        public async Task<TrainingProvider> UpdateTrainingProviderAsync(
            [Service] ITrainingService trainingService,
            UpdateTrainingProviderInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("UpdateTrainingProviderAsync not yet implemented in service");
        }

        // Enrollment Mutations
        public async Task<TrainingEnrollment> EnrollWorkerAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            EnrollWorkerInput input)
        {
            var enrollment = await trainingService.EnrollWorkerAsync(input.SessionId, input.WorkerId);

            // Send invitation notification
            await notificationService.SendSessionInvitationAsync(input.SessionId, input.WorkerId);

            return enrollment;
        }

        public async Task<TrainingEnrollment> UpdateEnrollmentStatusAsync(
            [Service] ITrainingService trainingService,
            UpdateEnrollmentStatusInput input)
        {
            await trainingService.UpdateEnrollmentStatusAsync(input.EnrollmentId, input.Status);

            var enrollments = await trainingService.GetSessionEnrollmentsAsync(0); // This needs to be fixed in service
            return enrollments.FirstOrDefault(e => e.Id == input.EnrollmentId) 
                ?? throw new ArgumentException("Enrollment not found");
        }

        // Certificate Mutations
        public async Task<TrainingCertificate> IssueCertificateAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            GraphQL.Types.IssueCertificateInput input)
        {
            var certificate = await trainingService.IssueCertificateAsync(new Services.IssueCertificateInput
            {
                SessionId = input.SessionId,
                WorkerId = input.WorkerId,
                Outcome = input.Outcome,
                CertificateNo = input.CertificateNo,
                CustomExpiryDate = input.CustomExpiryDate,
                CertificateFile = input.CertificateFile,
                Notes = input.Notes
            });

            // Send certificate issued notification
            await notificationService.SendCertificateIssuedNotificationAsync(certificate.Id);

            return certificate;
        }

        public async Task<TrainingCertificate> UpdateCertificateStatusAsync(
            [Service] ITrainingService trainingService,
            UpdateCertificateStatusInput input)
        {
            await trainingService.UpdateCertificateStatusAsync(input.CertificateId, input.Status);

            var certificates = await trainingService.GetWorkerCertificatesAsync(0); // This needs to be fixed in service
            return certificates.FirstOrDefault(c => c.Id == input.CertificateId) 
                ?? throw new ArgumentException("Certificate not found");
        }

        // Attendance Mutations
        public async Task<bool> MarkTrainingAttendanceAsync(
            [Service] ITrainingService trainingService,
            MarkTrainingAttendanceInput input)
        {
            await trainingService.MarkAttendanceAsync(
                input.EnrollmentId, 
                input.Attended, 
                input.Notes);

            return true;
        }

        // Batch Enrollment Mutations
        public async Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersByTradeAsync(
            [Service] ITrainingService trainingService,
            BatchEnrollByTradeInput input)
        {
            return await trainingService.BatchEnrollWorkersByTradeAsync(input.SessionId, input.TradeId);
        }

        public async Task<IEnumerable<TrainingEnrollment>> BatchEnrollWorkersBySiteAsync(
            [Service] ITrainingService trainingService,
            BatchEnrollBySiteInput input)
        {
            return await trainingService.BatchEnrollWorkersBySiteAsync(input.SessionId, input.SiteId);
        }

        // Trade Requirement Mutations
        public async Task<TradeRequirement> CreateTradeRequirementAsync(
            [Service] ITrainingService trainingService,
            CreateTradeRequirementInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("CreateTradeRequirementAsync not yet implemented in service");
        }

        public async Task<TradeRequirement> UpdateTradeRequirementAsync(
            [Service] ITrainingService trainingService,
            UpdateTradeRequirementInput input)
        {
            // This would be implemented in the service
            throw new NotImplementedException("UpdateTradeRequirementAsync not yet implemented in service");
        }

        // Bulk operations (like toolbox system)
        public async Task<bool> SendSessionRemindersAsync(
            [Service] TrainingNotificationService notificationService,
            int sessionId)
        {
            await notificationService.SendSessionReminderAsync(sessionId);
            return true;
        }

        public async Task<bool> ProcessExpiredCertificatesAsync(
            [Service] TrainingNotificationService notificationService)
        {
            await notificationService.ProcessExpiredCertificatesAsync();
            return true;
        }

        public async Task<bool> SendCertificateExpiryNotificationsAsync(
            [Service] TrainingNotificationService notificationService)
        {
            await notificationService.SendCertificateExpiryNotificationsAsync();
            return true;
        }

        public async Task<WorkerEligibilityStatus> UpdateWorkerEligibilityAsync(
            [Service] ITrainingService trainingService,
            [Service] TrainingNotificationService notificationService,
            WorkerEligibilityInput input)
        {
            var status = await trainingService.CheckWorkerEligibilityAsync(input.WorkerId, input.TradeId);

            // Send eligibility notification
            await notificationService.SendTradeEligibilityNotificationAsync(input.WorkerId, input.TradeId, status);

            return status;
        }

        // File management (like toolbox)
        public async Task<bool> ClearSessionTempFolderAsync(
            [Service] ITrainingService trainingService,
            int sessionId)
        {
            await trainingService.ClearSessionTempFolderAsync(sessionId);
            return true;
        }
    }
}
