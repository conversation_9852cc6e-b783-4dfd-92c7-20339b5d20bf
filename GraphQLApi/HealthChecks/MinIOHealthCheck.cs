using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Shared.Configuration;

namespace GraphQLApi.HealthChecks
{
    /// <summary>
    /// Health check for MinIO connectivity and bucket availability
    /// </summary>
    public class MinIOHealthCheck : IHealthCheck
    {
        private readonly IMinioClient _minioClient;
        private readonly MinIOConfiguration _config;
        private readonly ILogger<MinIOHealthCheck> _logger;

        public MinIOHealthCheck(
            IMinioClient minioClient,
            IOptions<MinIOConfiguration> config,
            ILogger<MinIOHealthCheck> logger)
        {
            _minioClient = minioClient;
            _config = config.Value;
            _logger = logger;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(
            HealthCheckContext context, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var healthData = new Dictionary<string, object>();

                // Test basic connectivity by listing buckets
                var buckets = await _minioClient.ListBucketsAsync(cancellationToken);
                healthData.Add("ConnectedBuckets", buckets.Buckets.Count);

                // Check if required buckets exist
                var requiredBuckets = _config.Buckets.GetAllBuckets();
                var existingBuckets = buckets.Buckets.Select(b => b.Name).ToList();
                var missingBuckets = requiredBuckets.Except(existingBuckets).ToList();

                healthData.Add("RequiredBuckets", requiredBuckets.Count);
                healthData.Add("ExistingBuckets", existingBuckets.Count);
                healthData.Add("MissingBuckets", missingBuckets.Count);

                if (missingBuckets.Any())
                {
                    healthData.Add("MissingBucketNames", missingBuckets);
                    _logger.LogWarning("MinIO health check found missing buckets: {MissingBuckets}", 
                        string.Join(", ", missingBuckets));
                    
                    return HealthCheckResult.Degraded(
                        $"MinIO is accessible but {missingBuckets.Count} required buckets are missing: {string.Join(", ", missingBuckets)}", 
                        data: healthData);
                }

                // Test write/read/delete operations on temp bucket
                await TestBasicOperationsAsync(cancellationToken);
                healthData.Add("BasicOperationsTest", "Passed");

                _logger.LogDebug("MinIO health check passed successfully");
                return HealthCheckResult.Healthy("MinIO is accessible and all required buckets exist", healthData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MinIO health check failed");
                return HealthCheckResult.Unhealthy($"MinIO health check failed: {ex.Message}", ex);
            }
        }

        private async Task TestBasicOperationsAsync(CancellationToken cancellationToken)
        {
            var testBucket = _config.Buckets.Temp;
            var testObjectKey = $"health-check-{Guid.NewGuid()}.txt";
            var testContent = "MinIO health check test content";

            try
            {
                // Test upload
                using var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(testContent));
                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(testBucket)
                    .WithObject(testObjectKey)
                    .WithStreamData(stream)
                    .WithObjectSize(stream.Length)
                    .WithContentType("text/plain");

                await _minioClient.PutObjectAsync(putObjectArgs, cancellationToken);

                // Test download
                var downloadStream = new MemoryStream();
                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(testBucket)
                    .WithObject(testObjectKey)
                    .WithCallbackStream(s => s.CopyTo(downloadStream));

                await _minioClient.GetObjectAsync(getObjectArgs, cancellationToken);

                // Verify content
                downloadStream.Position = 0;
                var downloadedContent = System.Text.Encoding.UTF8.GetString(downloadStream.ToArray());
                if (downloadedContent != testContent)
                {
                    throw new InvalidOperationException("Downloaded content does not match uploaded content");
                }

                // Test delete
                var removeObjectArgs = new RemoveObjectArgs()
                    .WithBucket(testBucket)
                    .WithObject(testObjectKey);

                await _minioClient.RemoveObjectAsync(removeObjectArgs, cancellationToken);
            }
            catch (Exception ex)
            {
                // Cleanup attempt in case of failure
                try
                {
                    var removeObjectArgs = new RemoveObjectArgs()
                        .WithBucket(testBucket)
                        .WithObject(testObjectKey);
                    await _minioClient.RemoveObjectAsync(removeObjectArgs, cancellationToken);
                }
                catch
                {
                    // Ignore cleanup errors
                }

                throw new InvalidOperationException($"Basic operations test failed: {ex.Message}", ex);
            }
        }
    }
}
