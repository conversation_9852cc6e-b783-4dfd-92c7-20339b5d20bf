using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using GraphQLApi.Data;
using GraphQLApi.Auth;
using GraphQLApi.Auth.Authorization;
using Shared.GraphQL.Models.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;

namespace GraphQLApi.Extensions
{
    /// <summary>
    /// Identity service extensions with OWASP Top 10 security measures
    /// Implements industry-standard security practices for authentication and authorization
    /// </summary>
    public static class IdentityServiceExtensions
    {
        /// <summary>
        /// Configures ASP.NET Core Identity with strict security settings
        /// Addresses OWASP A01 (Broken Access Control), A02 (Cryptographic Failures), A07 (Identification and Authentication Failures)
        /// </summary>
        public static IServiceCollection AddIdentityWithStrictSecurity(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure Identity with military-grade security settings
            services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
            {
                // Password requirements - OWASP compliant
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequiredLength = 12; // Minimum 12 characters
                options.Password.RequiredUniqueChars = 6; // Prevent simple patterns

                // Lockout settings - Zero tolerance for brute force
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
                options.Lockout.MaxFailedAccessAttempts = 3; // Lock after 3 attempts
                options.Lockout.AllowedForNewUsers = false; // Don't enable lockout for new users by default

                // User settings - Strict validation
                options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
                options.User.RequireUniqueEmail = true;

                // Sign-in requirements - Multi-factor verification
                options.SignIn.RequireConfirmedEmail = true;
                options.SignIn.RequireConfirmedPhoneNumber = false; // Can be enabled for additional security

                // Security stamp validation - Force re-authentication on security changes
                // Note: This is configured in AddSecurityStampValidator

                // Token settings for enhanced security
                options.Tokens.EmailConfirmationTokenProvider = TokenOptions.DefaultEmailProvider;
                options.Tokens.PasswordResetTokenProvider = TokenOptions.DefaultEmailProvider;
                options.Tokens.ChangeEmailTokenProvider = TokenOptions.DefaultEmailProvider;
            })
            .AddEntityFrameworkStores<AppDbContext>()
            .AddDefaultTokenProviders()
            .AddTokenProvider<DataProtectorTokenProvider<ApplicationUser>>("SecureTokenProvider")
            .AddSignInManager<SignInManager<ApplicationUser>>()
            .AddRoleManager<RoleManager<ApplicationRole>>();

            // Configure application cookie separately
            services.ConfigureApplicationCookie(options =>
            {
                options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
                options.SlidingExpiration = true;
            });

            // Configure Data Protection for enhanced security (OWASP A02 - Cryptographic Failures)
            services.AddDataProtection(options =>
            {
                options.ApplicationDiscriminator = "WorkforceManagement";
            })
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90)) // Rotate keys every 90 days
            .SetApplicationName("WorkforceManagement");

            // Configure password hashers with enhanced security
            services.Configure<PasswordHasherOptions>(options =>
            {
                options.IterationCount = 100000; // High iteration count for PBKDF2
                options.CompatibilityMode = PasswordHasherCompatibilityMode.IdentityV3;
            });

            return services;
        }

        /// <summary>
        /// Configures JWT authentication with Identity integration and enhanced security
        /// Addresses OWASP A02 (Cryptographic Failures), A07 (Identification and Authentication Failures)
        /// </summary>
        public static IServiceCollection AddSecureJwtAuthentication(this IServiceCollection services, IConfiguration configuration)
        {
            var jwtSettings = configuration.GetSection("JwtSettings").Get<JwtSettings>()
                ?? throw new InvalidOperationException("JWT settings not found in configuration.");

            services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = true; // Force HTTPS in production
                options.SaveToken = false; // Don't store tokens in AuthenticationProperties for security
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    // Issuer validation
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings.Issuer,

                    // Audience validation
                    ValidateAudience = true,
                    ValidAudience = jwtSettings.Audience,

                    // Lifetime validation
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero, // No clock skew tolerance for strict security

                    // Signing key validation
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.SecretKey)),

                    // Additional security validations
                    RequireExpirationTime = true,
                    RequireSignedTokens = true,
                    ValidateActor = true,
                    ValidateTokenReplay = true,

                    // Security algorithm restriction
                    ValidAlgorithms = new[] { SecurityAlgorithms.HmacSha256 }
                };

                options.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        // Support WebSocket connections for GraphQL subscriptions
                        var accessToken = context.Request.Query["access_token"];
                        var path = context.HttpContext.Request.Path;

                        if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/graphql"))
                        {
                            context.Token = accessToken;
                        }

                        return Task.CompletedTask;
                    },
                    OnTokenValidated = async context =>
                    {
                        // Additional security validation with Identity
                        var userManager = context.HttpContext.RequestServices.GetRequiredService<UserManager<ApplicationUser>>();
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        
                        var userIdClaim = context.Principal?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                        var securityStampClaim = context.Principal?.FindFirst("security_stamp")?.Value;
                        
                        if (int.TryParse(userIdClaim, out var userId))
                        {
                            var user = await userManager.FindByIdAsync(userId.ToString());
                            if (user == null)
                            {
                                logger.LogWarning("JWT token contains invalid user ID: {UserId}", userId);
                                context.Fail("Invalid user");
                                return;
                            }

                            // Validate security stamp to detect compromised accounts
                            var currentSecurityStamp = await userManager.GetSecurityStampAsync(user);
                            if (securityStampClaim != currentSecurityStamp)
                            {
                                logger.LogWarning("Security stamp mismatch for user {UserId}. Token may be compromised.", userId);
                                context.Fail("Security stamp validation failed");
                                return;
                            }

                            // Check if user account is still active
                            if (!user.IsActive || await userManager.IsLockedOutAsync(user))
                            {
                                logger.LogWarning("Access denied for inactive/locked user {UserId}", userId);
                                context.Fail("User account is not active");
                                return;
                            }

                            // Validate tenant context
                            var tenantIdClaim = context.Principal?.FindFirst("tenant_id")?.Value;
                            if (string.IsNullOrEmpty(tenantIdClaim) || !int.TryParse(tenantIdClaim, out var tenantId) || tenantId != user.TenantId)
                            {
                                logger.LogWarning("Tenant context mismatch for user {UserId}", userId);
                                context.Fail("Invalid tenant context");
                                return;
                            }
                        }
                    },
                    OnAuthenticationFailed = context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        logger.LogWarning("JWT authentication failed: {Error} | IP: {IP} | UserAgent: {UserAgent}", 
                            context.Exception.Message,
                            context.HttpContext.Connection.RemoteIpAddress,
                            context.HttpContext.Request.Headers["User-Agent"].ToString());
                        
                        return Task.CompletedTask;
                    },
                    OnChallenge = context =>
                    {
                        // Log unauthorized access attempts
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
                        logger.LogWarning("Unauthorized access attempt to {Path} | IP: {IP} | UserAgent: {UserAgent}",
                            context.HttpContext.Request.Path,
                            context.HttpContext.Connection.RemoteIpAddress,
                            context.HttpContext.Request.Headers["User-Agent"].ToString());
                        
                        return Task.CompletedTask;
                    }
                };
            });

            return services;
        }

        /// <summary>
        /// Configures enhanced authorization with custom handlers
        /// Addresses OWASP A01 (Broken Access Control), A05 (Security Misconfiguration)
        /// </summary>
        public static IServiceCollection AddEnhancedAuthorizationServices(this IServiceCollection services)
        {
            // Register authorization handlers
            services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, TenantAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, SecurityStampValidationHandler>();
            services.AddScoped<IAuthorizationHandler, RateLimitAuthorizationHandler>();

            services.AddAuthorization(options =>
            {
                // Configure existing permission-based policies
                var resourceTypes = new[] { "Workers", "Sites", "Trainings", "Docs", "PPE", "RoleManagement", "Reports" };
                var operations = new[] { "Create", "Read", "Update", "Delete" };
                var levels = new[] { "Site", "Company" };

                foreach (var resource in resourceTypes)
                {
                    foreach (var operation in operations)
                    {
                        foreach (var level in levels)
                        {
                            var policyName = $"{resource}.{operation}.{level}";
                            options.AddPolicy(policyName, policy =>
                            {
                                policy.RequireAuthenticatedUser();
                                policy.Requirements.Add(new PermissionRequirement(resource, operation, level));
                                policy.Requirements.Add(new TenantRequirement());
                                policy.Requirements.Add(new SecurityStampRequirement());
                                policy.Requirements.Add(new RateLimitRequirement());
                            });
                        }
                    }
                }

                // Additional security policies
                options.AddPolicy("RequireEmailConfirmed", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireClaim("email_verified", "true");
                });

                options.AddPolicy("RequireActiveTenant", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.Requirements.Add(new TenantRequirement());
                });

                options.AddPolicy("RequireValidSecurityStamp", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.Requirements.Add(new SecurityStampRequirement());
                });

                options.AddPolicy("AdminOnly", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.RequireRole("Admin");
                    policy.Requirements.Add(new TenantRequirement());
                    policy.Requirements.Add(new SecurityStampRequirement());
                });

                // Note: Removed fallback policy to allow GraphQL playground access
                // Authorization is handled at the GraphQL field level with [Authorize] attributes

                // Default policy with enhanced security
                options.DefaultPolicy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .AddRequirements(new TenantRequirement())
                    .AddRequirements(new SecurityStampRequirement())
                    .Build();
            });

            return services;
        }

        /// <summary>
        /// Configures security headers middleware for OWASP compliance
        /// Addresses OWASP A05 (Security Misconfiguration), A03 (Injection)
        /// </summary>
        public static IServiceCollection AddSecurityHeaders(this IServiceCollection services)
        {
            services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(365);
            });

            services.AddAntiforgery(options =>
            {
                options.HeaderName = "X-XSRF-TOKEN";
                options.Cookie.SameSite = SameSiteMode.Strict;
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                options.Cookie.HttpOnly = true;
            });

            return services;
        }
    }
}
