using GraphQLApi.Services;
using GraphQLApi.GraphQL.Queries;
using GraphQLApi.GraphQL.Mutations;
using GraphQLApi.GraphQL.Auth;
using GraphQLApi.GraphQL.Subscriptions;
using GraphQLApi.GraphQL.Types;
using GraphQLApi.Auth;
using GraphQLApi.Auth.Authorization;
using Shared.GraphQL.Models;
using Shared.GraphQL.Models.Auth;
using Shared.GraphQL.Types;
using HotChocolate.Data;
using Microsoft.Extensions.Options;
using Minio;
using Shared.Configuration;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace GraphQLApi.Extensions
{
    public static class ServiceCollectionExtensions
    {

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddScoped<IWorkerService, WorkerService>();
            
            // NEW Training System (replaces old training services)
            services.AddScoped<GraphQLApi.Services.ITrainingService, GraphQLApi.Services.TrainingService>();
            services.AddScoped<TrainingNotificationService>();
            
            // Old training services (keeping for compatibility until migration)
            services.AddScoped<IWorkerTrainingService, WorkerTrainingService>();
            services.AddScoped<ITradeService, TradeService>();
            services.AddScoped<ISkillService, SkillService>();
            services.AddScoped<IPhotoService, PhotoService>();
            services.AddScoped<IWorkerAttendanceService, WorkerAttendanceService>();
            services.AddScoped<ITrainingStatusService, TrainingStatusService>();
            // services.AddScoped<ITaskService, TaskService>();
            services.AddScoped<IEquipmentService, EquipmentService>();
            services.AddScoped<IJobService, JobService>();
            services.AddScoped<IToolboxService, ToolboxService>();
            services.AddScoped<IPermitService, PermitService>();
            services.AddScoped<IRiskAssessmentService, RiskAssessmentService>();
            services.AddScoped<ISiteService, SiteService>();
            services.AddScoped<IRelationshipService, RelationshipService>();
            services.AddScoped<InspectionService>();
            services.AddHostedService<TrainingStatusBackgroundService>();

            // Notifications
            services.AddSingleton<NotificationEventBus>();
            services.AddHostedService(sp => sp.GetRequiredService<NotificationEventBus>());
            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<INotificationChannelHandler, InAppNotificationHandler>();
            services.AddScoped<INotificationChannelHandler, EmailNotificationHandler>();
            services.AddScoped<IRecipientResolutionService, RecipientResolutionService>();
            services.AddScoped<NotificationManagementService>();
            services.AddScoped<ISmsSender, NoOpSmsSender>();

            return services;
        }

        public static IServiceCollection AddAuthenticationServices(this IServiceCollection services)
        {
            services.AddScoped<IPasswordService, PasswordService>();
            services.AddScoped<IJwtService, IdentityJwtService>(); // Use Identity JWT service
            services.AddScoped<IdentityJwtService>(); // Register concrete type
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAuthenticationService, IdentityAuthenticationService>(); // Use Identity auth service
            services.AddScoped<IAuditService, AuditService>();
            services.AddHostedService<SessionCleanupService>();

            // Notification config
            services.AddOptions<Shared.Configuration.NotificationOptions>()
                .BindConfiguration("Notifications");

            // Resend email config & sender
            services.AddOptions<Shared.Configuration.ResendOptions>()
                .BindConfiguration("Resend");
            services.AddScoped<IEmailSender, ResendEmailSender>();

            // EF interceptor
            services.AddSingleton<GraphQLApi.Data.Notifications.NotificationSaveChangesInterceptor>();

            return services;
        }

        public static IServiceCollection AddJwtServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure JWT settings
            var jwtSettings = configuration.GetSection("JwtSettings").Get<JwtSettings>()
                ?? throw new InvalidOperationException("JWT settings not found in configuration.");

            services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));

            // Configure JWT authentication
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = jwtSettings.RequireHttpsMetadata;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = jwtSettings.ValidateIssuer,
                    ValidateAudience = jwtSettings.ValidateAudience,
                    ValidateLifetime = jwtSettings.ValidateLifetime,
                    ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings.SecretKey)),
                    ClockSkew = TimeSpan.FromSeconds(jwtSettings.ClockSkewSeconds)
                };

                // Configure events for GraphQL
                options.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];
                        var path = context.HttpContext.Request.Path;

                        if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/graphql"))
                        {
                            context.Token = accessToken;
                        }

                        return System.Threading.Tasks.Task.CompletedTask;
                    }
                };
            });

            return services;
        }

        public static IServiceCollection AddAuthorizationServices(this IServiceCollection services)
        {
            // Add authorization handlers
            services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

            // Configure authorization policies
            services.AddAuthorization(options =>
            {
                // Add permission-based policies
                var resourceTypes = new[] { "Workers", "Sites", "Trainings", "Docs", "PPE", "RoleManagement", "Reports" };
                var operations = new[] { "Create", "Read", "Update", "Delete" };
                var levels = new[] { "Site", "Company" };

                foreach (var resource in resourceTypes)
                {
                    foreach (var operation in operations)
                    {
                        foreach (var level in levels)
                        {
                            var policyName = $"{resource}.{operation}.{level}";
                            options.AddPolicy(policyName, policy =>
                                policy.Requirements.Add(new PermissionRequirement(resource, operation, level)));
                        }
                    }
                }
            });

            return services;
        }

        public static IServiceCollection AddHikvisionServices(this IServiceCollection services, IConfiguration configuration)
        {
            var baseUrl = configuration["HikvisionApi:BaseUrl"]
                ?? throw new ArgumentNullException("HikvisionApi:BaseUrl is missing in configuration");

            services.AddHttpClient<IHikvisionService, HikvisionService>(client =>
            {
                client.BaseAddress = new Uri(baseUrl);
            });
            services.AddScoped<IHikvisionService, HikvisionService>();

            return services;
        }

        public static IServiceCollection AddMinIOServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure MinIO settings
            services.Configure<MinIOConfiguration>(configuration.GetSection("MinIO"));

            // Register MinIO client
            services.AddSingleton<IMinioClient>(serviceProvider =>
            {
                var config = serviceProvider.GetRequiredService<IOptions<MinIOConfiguration>>().Value;

                var minioClient = new MinioClient()
                    .WithEndpoint(config.Endpoint)
                    .WithCredentials(config.AccessKey, config.SecretKey);

                if (config.UseSSL)
                {
                    minioClient = minioClient.WithSSL();
                }

                if (!string.IsNullOrEmpty(config.Region))
                {
                    minioClient = minioClient.WithRegion(config.Region);
                }

                return minioClient.Build();
            });

            // Register MinIO services
            services.AddScoped<IMinioService, MinioService>();
            services.AddScoped<IBucketInitializationService, BucketInitializationService>();
            services.AddHostedService<BucketInitializationHostedService>();

            return services;
        }

        public static IServiceCollection AddGraphQLServices(this IServiceCollection services)
        {
            services
                .AddGraphQLServer()
                .AddInMemorySubscriptions()
                .ModifyRequestOptions(opt => opt.IncludeExceptionDetails = true)
                .AddQueryType<Query>()
                .AddMutationType<Mutation>()
                .AddSubscriptionType<NotificationSubscriptions>()
                .AddType<NotificationEventType>()
                .AddType<NotificationType>()
                .AddType<NotificationDeliveryType>()
                .AddType<NotificationPreferenceType>()
                .AddType<NotificationChannelType>()
                .AddType<NotificationPriorityType>()
                .AddType<NotificationStatusType>()
                .AddType<NotificationPreferenceInputType>()
                .AddTypeExtension<AuthQueries>()
                .AddTypeExtension<AuthMutations>()
                .AddTypeExtension<NotificationQueries>()
                .AddTypeExtension<NotificationMutations>()
                .AddTypeExtension<TrainingQueries>()
                .AddTypeExtension<TrainingMutations>()
                .AddAuthorization()
                .AddProjections()
                .AddFiltering()
                // .AddMutationConventions()
                .AddType<UploadType>()
                .AddType<ApplicationUserType>()
                .AddType<RoleType>()
                .AddType<WorkersPermissionsGraphQLType>()
                .AddType<SitesPermissionsGraphQLType>()
                .AddType<TenantType>()
                .AddType<UserSessionType>()
                .AddType<RefreshTokenType>()
                .AddType<UserAuditLogType>()
                .AddType<EnumType<UserStatus>>()
                .AddType<EnumType<AuditAction>>()
                .AddType<WorkerType>()
                .AddType<LegacyTrainingType>()
                .AddType<WorkerTrainingType>()
                // New Training System Types
                .AddType<Shared.GraphQL.Types.Training.TrainingProgramType>()
                .AddType<Shared.GraphQL.Types.Training.TrainingProviderType>()
                .AddType<Shared.GraphQL.Types.Training.TrainingSessionType>()
                .AddType<Shared.GraphQL.Types.Training.TrainingEnrollmentType>()
                .AddType<Shared.GraphQL.Types.Training.TrainingCertificateType>()
                .AddType<Shared.GraphQL.Types.Training.TradeRequirementType>()
                .AddType<TradeType>()
                .AddType<SkillType>()
                // .AddType<TaskType>()
                .AddType<EquipmentType>()
                .AddType<WorkerAttendanceType>()
                .AddType<ToolboxSessionType>()
                .AddType<WorkerTrainingHistoryType>()
                .AddType<IncidentType>()
                .AddType<SiteType>()
                .AddType<SiteDataDtoType>()
                .AddType<ProjectDetailsDtoType>()
                .AddType<KeyPersonelDtoType>()
                .AddType<SiteSpecificationDtoType>()
                .AddType<SiteLocationDtoType>()
                .AddType<BuildingStatsDtoType>()
                .AddType<BuildingFootprintDtoType>()
                .AddType<UtilitiesServicesDtoType>()
                .AddType<AccessRoadsDtoType>()
                .AddType<RegulatoryComplianceDtoType>()
                .AddType<BuildingPermitDtoType>()
                .AddType<ClassificationDtoType>()
                .AddType<ComplianceStandardDtoType>()
                .AddType<SiteCommitteeDtoType>()
                .AddType<EmergencyContactsDtoType>()
                .AddType<ProjectTimelineDtoType>()
                .AddType<EnumType<Shared.Enums.TrainingStatus>>()
                .AddType<EnumType<Shared.Enums.TaskStatus>>()
                .AddType<EnumType<Shared.Enums.TaskPriority>>()
                .AddType<Shared.GraphQL.Types.InspectionType>()
                .AddType<Shared.GraphQL.Types.InspectionItemType>()
                .AddType<EnumType<Shared.Enums.InspectionStatus>>()
                .AddType<EnumType<Shared.Enums.IncidentStatus>>()
                .AddType<EnumType<Shared.Enums.JobStatus>>()
                .AddType<EnumType<Shared.Enums.PermitType>>()
                .AddType<EnumType<Shared.Enums.PermitStatus>>()
                .AddType<Shared.GraphQL.Types.FileMetadataType>()
                .AddType<Shared.GraphQL.Types.DocumentFileType>()
                .AddType<Shared.GraphQL.Types.JobType>()
                // .AddType<Shared.GraphQL.Types.JobDTOType>()
                .AddType<Shared.GraphQL.Types.HazardType>()
                .AddType<Shared.GraphQL.Types.ControlMeasureType>()
                .AddType<Shared.GraphQL.Types.CategoryType>()
                .AddType<Shared.GraphQL.Types.ToolboxType>()
                .AddType<GraphQLApi.GraphQL.Types.TodaysJobRiskAssessmentType>()
                .AddType<GraphQLApi.GraphQL.Types.TodaysJobHazardType>()
                .AddType<GraphQLApi.GraphQL.Types.TodaysJobControlMeasureType>()
                .AddType<Shared.GraphQL.Types.PermitType>()
                .AddType<Shared.GraphQL.Types.GeneralWorkPermitType>()
                .AddType<Shared.GraphQL.Types.HotWorkPermitType>()
                .AddType<Shared.GraphQL.Types.ExcavationWorkPermitType>()
                .AddType<Shared.GraphQL.Types.WorkAtHeightPermitType>()
                .AddType<Shared.GraphQL.Types.ConfinedSpacePermitType>()
                .AddType<GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentType>()
                .AddType<GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentHazardType>()
                .AddType<GraphQLApi.GraphQL.Types.ToolboxRiskAssessmentControlMeasureType>()
                .AddType<EnumType<Shared.Enums.ToolboxStatus>>()
                .AddType<EnumType<Shared.Enums.PermitStatus>>()
                .AddType<EnumType<Shared.Enums.PermitType>>()
                .AddType<EnumType<Shared.Enums.AllowedFileType>>()
                .AddType<EnumType<Shared.Enums.BucketName>>()
                .AddType<EnumType<Shared.GraphQL.Models.InspectionType>>()
                .AddType<TimeSpanType>();

            return services;
        }
    }
}