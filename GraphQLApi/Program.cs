using GraphQLApi.Data;
using GraphQLApi.Extensions;
using GraphQLApi.HealthChecks;
using GraphQLApi.Auth.Middleware;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.HttpOverrides;

var builder = WebApplication.CreateBuilder(args);

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
    ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

// Add memory cache for rate limiting
builder.Services.AddMemoryCache();



// Register DbContext for Identity and general use
builder.Services.AddDbContext<AppDbContext>((sp, options) =>
{
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorNumbersToAdd: null);
    });
    options.AddInterceptors(sp.GetRequiredService<GraphQLApi.Data.Notifications.NotificationSaveChangesInterceptor>());
});

// Register manual factory for services that specifically need it
builder.Services.AddScoped<IDbContextFactory<AppDbContext>>(sp => 
{
    var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
    optionsBuilder.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorNumbersToAdd: null);
    });
    
    return new ManualDbContextFactory(optionsBuilder.Options, sp);
});


builder.Services
    .AddApplicationServices()
    .AddAuthenticationServices()
    .AddIdentityWithStrictSecurity(builder.Configuration)
    .AddSecureJwtAuthentication(builder.Configuration)
    .AddEnhancedAuthorizationServices()
    .AddSecurityHeaders()
    .AddHikvisionServices(builder.Configuration)
    .AddMinIOServices(builder.Configuration)
    .AddGraphQLServices()
    .AddHealthChecks()
    .AddCheck<MinIOHealthCheck>("minio");
// Configure CORS with enhanced security
builder.Services.AddCors(opt =>
{
    opt.AddDefaultPolicy(bui =>
    {
        bui.WithOrigins("http://localhost:5173", "https://localhost:5173")
        .AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials() // Required for cookies
        .SetPreflightMaxAge(TimeSpan.FromMinutes(10)); // Cache preflight requests
    });
});

// Configure forwarded headers for reverse proxy scenarios
builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
    options.KnownNetworks.Clear();
    options.KnownProxies.Clear();
});

builder.Services.AddControllers();
builder.Services.AddHttpContextAccessor();

var app = builder.Build();

await DatabaseInitializer.InitializeDatabaseAsync(app.Services);

// Enhanced security middleware pipeline (order is critical)
app.UseForwardedHeaders(); // Must be early for proper IP detection
app.UseSecurityHeaders();
app.UseHsts(); // HTTP Strict Transport Security
app.UseRateLimiting();
app.UseCors();
app.UseAuthentication();
app.UseMiddleware<JwtMiddleware>();
app.UseAuthorization();

app.MapGraphQL().AllowAnonymous();
app.MapHealthChecks("/health");
app.MapControllers();
app.UseWebSockets();

// Seed authentication data
try
{
    using (var scope = app.Services.CreateScope())
    {
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogInformation("Starting authentication data seeding...");

        var contextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<AppDbContext>>();
        await using var context = await contextFactory.CreateDbContextAsync();
        await GraphQLApi.Data.AuthSeedData.SeedAsync(context, scope.ServiceProvider);

        logger.LogInformation("Authentication data seeding completed successfully.");
    }
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "Failed to seed authentication data: {Message}", ex.Message);
}

app.Run();

public partial class Program { }

public class DbContextHealthCheck : IHealthCheck
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory;

    public DbContextHealthCheck(IDbContextFactory<AppDbContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await _contextFactory.CreateDbContextAsync(cancellationToken);
            await dbContext.Database.CanConnectAsync(cancellationToken);
            return HealthCheckResult.Healthy();
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy(ex.Message);
        }
    }
}
