namespace Shared.GraphQL.InputTypes
{
    /// <summary>
    /// Input type for creating or updating an inspection item
    /// </summary>
    public class InspectionItemInput
    {
        /// <summary>
        /// Description of what is being inspected
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Whether the inspection item passed (true) or failed (false)
        /// </summary>
        public bool IsTrue { get; set; }

        /// <summary>
        /// Additional remarks or comments for this inspection item
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// Array of image file IDs associated with this inspection item
        /// </summary>
        public int[]? ImageFileIds { get; set; }
    }
}
