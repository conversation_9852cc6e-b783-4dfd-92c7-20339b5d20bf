using Shared.GraphQL.Models;

namespace Shared.GraphQL.InputTypes
{
    /// <summary>
    /// Input type for creating a new inspection
    /// </summary>
    public class CreateInspectionInput
    {
        /// <summary>
        /// Collection of inspection items that make up this inspection
        /// </summary>
        public List<InspectionItem> InspectionItems { get; set; } = [];
        public InspectionType InspectionType { get; set; }
        /// <summary>
        /// Whether the overall inspection is approved
        /// </summary>
        public bool Approved { get; set; }

        /// <summary>
        /// General comments about the inspection
        /// </summary>
        public string? Comments { get; set; }

        /// <summary>
        /// ID of the worker who performed the inspection
        /// </summary>
        public int InspectedById { get; set; }
    }
}
