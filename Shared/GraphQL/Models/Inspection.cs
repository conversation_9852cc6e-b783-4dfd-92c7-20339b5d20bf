using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    /// <summary>
    /// Entity representing an inspection with multiple inspection items
    /// </summary>
    public class Inspection : IAuditableEntity, ISoftDeletable
    {
        /// <summary>
        /// Unique identifier for the inspection
        /// </summary>
        public int Id { get; set; }

        public InspectionType InspectionType { get; set; }

        /// <summary>
        /// Collection of inspection items that make up this inspection
        /// </summary>
        public virtual ICollection<InspectionItem> InspectionItems { get; set; } = [];

        /// <summary>
        /// Whether the overall inspection is approved
        /// </summary>
        public bool Approved { get; set; }

        /// <summary>
        /// General comments about the inspection
        /// </summary>
        public string Comments { get; set; } = string.Empty;

        /// <summary>
        /// Foreign key to the inspector's signature file
        /// </summary>
        public int? SignatureFileId { get; set; }

        /// <summary>
        /// Navigation property to the inspector's signature file
        /// </summary>
        public virtual FileMetadata? SignatureFile { get; set; }

        /// <summary>
        /// Foreign key to the worker who performed the inspection
        /// </summary>
        public int InspectedById { get; set; }

        /// <summary>
        /// Navigation property to the worker who performed the inspection
        /// </summary>
        public virtual Worker InspectedBy { get; set; } = null!;

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
    public enum InspectionType
    {
        AIR_COMPRESSOR_MACHINE_INSPECTION,
        ANGLE_GRINDING_MACHINE_INSPECTION,
        BACKHOE_LOADER_INSPECTION,
        BAR_BENDING_MACHINE_INSPECTION,
        BENCH_CUTTING_MACHINE_INSPECTION,
        CIRCULAR_SAW_MACHINE_INSPECTION,
        CONCRETE_BOOM_PLACER_INSPECTION,
        CONCRETE_MIXER_INSPECTION,
        CONCRETE_PUMP_INSPECTION,
        DIESEL_GENERATOR_INSPECTION,
        DRILLING_MACHINE_INSPECTION,
        DRUM_ROLLER_INSPECTION,
        EXCAVATOR_INSPECTION,
        FIRE_EXTINGUISHERS_INSPECTION,
        GRADER_INSPECTION,
        JACK_HAMMER_INSPECTION,
        POCKER_VIBRATOR_INSPECTION,
        READY_MIX_INSPECTION,
        STEEL_REBAR_CUTTING_MACHINE_INSPECTION,
        TIPPER_TRUCK_INSPECTION,
        WELDING_MACHINE_INSPECTION
    }
}
