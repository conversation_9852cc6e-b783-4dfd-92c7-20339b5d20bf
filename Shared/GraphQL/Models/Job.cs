using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HotChocolate;
using Shared.Enums;
using Shared.Errors;
using Shared.Interfaces;

namespace Shared.GraphQL.Models;

public class Job : IAuditableEntity, ISoftDeletable, IValidatableObject
{
    // Basic information
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public List<string> PPEs { get; set; } = []; // todo: make it a list of PPEs, comma separated string, this is not used anymore, use PPEs instead.
    public JobStatus Status { get; set; } = JobStatus.REQUESTED;
    public HashSet<PermitType> RequiredPermits { get; set; } = [];
    public HashSet<string> PrecautionsRequired { get; set; } = [];
    public TimeSpan TimeForCompletion { get; set; } = TimeSpan.FromDays(1); // Default to 1 day
    public DateTime StartDate { get; set; } = DateTime.Today.AddDays(1); // Default to tomorrow
    public DateTime? DueDate { get; set; }
    

    // Excavation specific fields
    public ExcavationFields? ExcavationFields { get; set; }

    // Hot work specific fields
    public HotWorkFields? HotWorkFields { get; set; }

    // Work at height specific fields
    public WorkAtHeightFields? WorkAtHeightFields { get; set; }

    // Category relationship
    public int? CategoryId { get; set; }
    public Category? Category { get; set; }

    // Request information
    public int? RequestedById { get; set; }
    public Worker? RequestedBy { get; set; }
    public DateTime? RequestedDate { get; set; }

    // Block information
    public int? BlockedById { get; set; }
    public Worker? BlockedBy { get; set; }
    public DateTime? BlockedDate { get; set; }

    // Review information
    public int? ReviewedById { get; set; }
    public Worker? ReviewedBy { get; set; }
    public DateTime? ReviewedDate { get; set; }

    // Approval information (used for both approved and disapproved)
    public int? ApprovedById { get; set; }
    public Worker? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }

    // Finish information
    public int? FinishedById { get; set; }
    public Worker? FinishedBy { get; set; }
    public DateTime? FinishedDate { get; set; }

    // Chief Engineer and Workers
    public int? ChiefEngineerId { get; set; }
    public Worker? ChiefEngineer { get; set; }
    // public ICollection<Worker> Workers { get; set; } = [];
    public ICollection<Trade> RequiredTrades { get; set; } = [];
    public ICollection<LegacyTraining> RequiredTrainings { get; set; } = [];

    // Hazards, Documents, and Permits
    public ICollection<Hazard> Hazards { get; set; } = [];
    public ICollection<DocumentFile> Documents { get; set; } = [];
    public ICollection<Permits.Permit> Permits { get; set; } = [];

    // Audit Fields
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? UpdatedAt { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;

    // Soft Delete Fields
    public bool IsDeleted { get; set; }
    public DateTime? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }

    // Computed property for due date
    public DateTime CalculatedDueDate
    {
        get
        {
            if (DueDate.HasValue)
                return DueDate.Value;

            // Calculate based on start date + time for completion

            return StartDate.Add(TimeForCompletion);

            // Default to start date + 1 day if no time for completion specified, this should never happen since we have a default value for time for completion
            //return StartDate.AddDays(1);
        }
    }
    [GraphQLIgnore]
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (validationContext.MemberName == null || validationContext.MemberName == nameof(CalculatedDueDate))
        {
            if (CalculatedDueDate < StartDate)
            {
                yield return new ValidationResult("Due date cannot be before start date", [nameof(DueDate)]);
            }
            if (CalculatedDueDate < DateTime.UtcNow)
            {
                yield return new ValidationResult("Due date cannot be in the past", [nameof(DueDate)]);
            }
        }

        if (validationContext.MemberName == null || validationContext.MemberName == nameof(StartDate))
        {
            if (StartDate.ToUniversalTime() < DateTime.UtcNow)
            {
                yield return new ValidationResult("Start date cannot be in the past", [nameof(StartDate)]);
            }
        }

        if (validationContext.MemberName == null || validationContext.MemberName == nameof(RequiredPermits))
        {
            if (RequiredPermits.Contains(PermitType.EXCAVATION_PERMIT))
            {
                if (ExcavationFields == null || string.IsNullOrEmpty(ExcavationFields.DepthOfExcavation))
                    yield return new ValidationResult("Depth of excavation is required for excavation permits", [nameof(ExcavationFields)]);
                if (ExcavationFields == null || ExcavationFields.ExcavationProtectionSystems.Count == 0)
                    yield return new ValidationResult("Excavation protection systems is required for excavation permits", [nameof(ExcavationFields)]);
                if (ExcavationFields == null || ExcavationFields.ExcavationEquipmentsToBeUsed.Count == 0)
                    yield return new ValidationResult("Excavation equipments to be used is required for excavation permits", [nameof(ExcavationFields)]);
            }
            else if (ExcavationFields != null)
            {
                yield return new ValidationResult("Depth of excavation is not required for non-excavation permits", [nameof(ExcavationFields)]);
                yield return new ValidationResult("Excavation protection systems is not required for non-excavation permits", [nameof(ExcavationFields)]);
                yield return new ValidationResult("Excavation equipments to be used is not required for non-excavation permits", [nameof(ExcavationFields)]);
            }

            if (RequiredPermits.Contains(PermitType.HOT_WORK_PERMIT))
            {
                if (HotWorkFields == null || HotWorkFields.FireExtinguishers.Count == 0)
                    yield return new ValidationResult("Fire extinguishers is required for hot work permits", [nameof(HotWorkFields)]);
                if (HotWorkFields == null || HotWorkFields.NatureOfHotWorks.Count == 0)
                    yield return new ValidationResult("Nature of hot work is required for hot work permits", [nameof(HotWorkFields)]);
            }
            else if (HotWorkFields != null)
            {
                yield return new ValidationResult("Fire extinguishers is not required for non-hot work permits", [nameof(HotWorkFields)]);
                yield return new ValidationResult("Nature of hot work is not required for non-hot work permits", [nameof(HotWorkFields)]);
            }

            if (RequiredPermits.Contains(PermitType.WORK_AT_HEIGHT_PERMIT))
            {
                if (WorkAtHeightFields == null || WorkAtHeightFields.ModesOfAccessToBeUsed.Count == 0)
                    yield return new ValidationResult("Mode of access to be used is required for work at height permits", [nameof(WorkAtHeightFields)]);
            }
            else if (WorkAtHeightFields != null)
            {
                yield return new ValidationResult("Mode of access to be used is not required for non-work at height permits", [nameof(WorkAtHeightFields)]);
            }
        }
    }
    public static void SelfValidate(Job job)
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(job, null, null);

        Validator.TryValidateObject(job, context, results, true);

        if (results.Count > 0)
        {
            // Console.WriteLine(results);
            throw new MultipleValidationException(results);
        }
    }

    public static void ValidatePermits(Job job)
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(job);

        Validator.TryValidateObject(job, context, results, true);

        var permitResults = results
                .Where(r => r.MemberNames.Contains(nameof(RequiredPermits))
                || r.MemberNames.Contains(nameof(ExcavationFields))
                || r.MemberNames.Contains(nameof(HotWorkFields))
                || r.MemberNames.Contains(nameof(WorkAtHeightFields))
                )
                .ToList();



        if (permitResults.Count > 0)
        {
            // Console.WriteLine(results);
            throw new MultipleValidationException(permitResults);
        }
    }
    public static void ValidateStartDate(Job job)
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(job);
        Validator.TryValidateObject(job, context, results, true);

        var startDateResults = results
                .Where(r => r.MemberNames.Contains(nameof(StartDate)))
                .ToList();

        if (startDateResults.Count > 0)
        {
            // Console.WriteLine(results);
            throw new MultipleValidationException(startDateResults);
        }
    }
    public static void ValidateDueDate(Job job)
    {
        var results = new List<ValidationResult>();
        var context = new ValidationContext(job);
        Validator.TryValidateObject(job, context, results, true);

        var dueDateResults = results
                .Where(r => r.MemberNames.Contains(nameof(CalculatedDueDate)))
                .ToList();

        if (dueDateResults.Count > 0)
        {
            // Console.WriteLine(results);
            throw new MultipleValidationException(dueDateResults);
        }
    }

}
public class ExcavationFields
{
    public string DepthOfExcavation { get; set; } = string.Empty;
    public List<string> ExcavationProtectionSystems { get; set; } = [];
    public List<string> ExcavationEquipmentsToBeUsed { get; set; } = [];
}
public class HotWorkFields
{
    public List<string> FireExtinguishers { get; set; } = [];
    public List<string> NatureOfHotWorks { get; set; } = [];
}
public class WorkAtHeightFields
{
    public List<string> ModesOfAccessToBeUsed { get; set; } = [];
}
