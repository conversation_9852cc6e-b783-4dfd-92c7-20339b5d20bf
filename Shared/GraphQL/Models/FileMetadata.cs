using Shared.Interfaces;
using Shared.Enums;

namespace Shared.GraphQL.Models
{
    /// <summary>
    /// Entity representing file metadata stored in MinIO
    /// </summary>
    public class FileMetadata : IAuditableEntity, ISoftDeletable
    {
        /// <summary>
        /// Unique identifier for the file metadata record
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Original filename as uploaded by the user
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// MIME content type of the file
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Optional description of the file
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// MinIO bucket name where the file is stored
        /// </summary>
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// MinIO object key (path) for the file
        /// </summary>
        public string ObjectKey { get; set; } = string.Empty;

        /// <summary>
        /// MinIO ETag for file integrity verification (optional)
        /// </summary>
        public string? ETag { get; set; }

        /// <summary>
        /// Version information for file versioning support
        /// </summary>
        public string? Version { get; set; }

        /// <summary>
        /// File type category for easier filtering
        /// </summary>
        public AllowedFileType FileType { get; set; }

        /// <summary>
        /// Folder path within the bucket for organization
        /// </summary>
        public string? FolderPath { get; set; }

        /// <summary>
        /// Whether the file is publicly accessible
        /// </summary>
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// Expiration date for temporary files (null for permanent files)
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Additional metadata as JSON string for extensibility
        /// </summary>
        public string? AdditionalMetadata { get; set; }

        /// <summary>
        /// URL to access the file through the file controller
        /// </summary>
        public string Url => $"/files/{Id}";

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        /// <summary>
        /// Navigation property for related entities (if needed)
        /// </summary>
        public virtual ICollection<Worker>? WorkersWithProfilePicture { get; set; }
        
        /// <summary>
        /// Navigation property for toolbox sessions (if needed)
        /// </summary>
        public virtual ICollection<ToolboxSession>? ToolboxSessions { get; set; }

        /// <summary>
        /// Gets the full MinIO URL for the file
        /// </summary>
        public string GetFullObjectPath()
        {
            if (string.IsNullOrEmpty(FolderPath))
                return ObjectKey;
            
            return $"{FolderPath.TrimEnd('/')}/{ObjectKey}";
        }

        /// <summary>
        /// Checks if the file is expired (for temporary files)
        /// </summary>
        public bool IsExpired()
        {
            return ExpiresAt.HasValue && ExpiresAt.Value <= DateTime.UtcNow;
        }
    }
}
