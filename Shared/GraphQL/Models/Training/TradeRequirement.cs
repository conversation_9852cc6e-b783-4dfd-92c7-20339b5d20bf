using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    public class TradeRequirement : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int TradeId { get; set; }
        public int ProgramId { get; set; }
        public bool Mandatory { get; set; } = true;
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Trade Trade { get; set; } = null!;
        public virtual TrainingProgram Program { get; set; } = null!;

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}

