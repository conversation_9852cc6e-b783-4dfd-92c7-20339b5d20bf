using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    public class TrainingSession : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int ProgramId { get; set; }
        public int ProviderId { get; set; }
        public Guid? SiteId { get; set; }
        public TrainingMode Mode { get; set; }
        public string Location { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TrainingSessionStatus Status { get; set; } = TrainingSessionStatus.DRAFT;
        public int Capacity { get; set; }
        public string? Notes { get; set; }

        // Session conductor info (JSON - like toolbox)
        public TrainingConductor Conductor { get; set; } = null!;

        // Session photos
        public int? SessionPictureFileId { get; set; }
        public virtual FileMetadata? SessionPictureFile { get; set; }

        // Navigation Properties
        public virtual TrainingProgram Program { get; set; } = null!;
        public virtual TrainingProvider Provider { get; set; } = null!;
        public virtual Site? Site { get; set; }

        public double? Cost { get; set; }

        // Enrollments track attendance - no need for separate Attendees collection
        public ICollection<TrainingEnrollment> Enrollments { get; set; } = new List<TrainingEnrollment>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }

    public class TrainingConductor
    {
        public int WorkerId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SignatureFileId { get; set; } = string.Empty;
    }
}

