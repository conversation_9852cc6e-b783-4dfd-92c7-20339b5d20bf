using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    public class TrainingCertificate : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int WorkerId { get; set; }
        public int ProgramId { get; set; }
        public int? SessionId { get; set; } // Link to session that issued this certificate
        public string? CertificateNo { get; set; } // Nullable - certificate might not have been issued yet
        public string? ProviderName { get; set; } // Nullable - provider name for reference
        public DateTime IssueDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public CertificateStatus Status { get; set; } = CertificateStatus.ISSUED;

        public CertificateType CertType { get; set; } = CertificateType.Internal;
        public string? FileUrl { get; set; }
        public int? CertificateFileId { get; set; }
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Worker Worker { get; set; } = null!;
        public virtual TrainingProgram Program { get; set; } = null!;
        public virtual TrainingSession? Session { get; set; }
        public virtual FileMetadata? CertificateFile { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }

    public enum CertificateType
    {
        Internal,
        External
    }
}

