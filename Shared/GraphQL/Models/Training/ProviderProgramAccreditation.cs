// using Shared.Interfaces;
// using Shared.Enums;

// namespace Shared.GraphQL.Models.Training
// {
//     /// <summary>
//     /// Junction table tracking provider accreditation/certification to deliver specific training programs.
//     /// This validates that a provider is authorized to train people in a certain program.
//     /// Also contains pricing and duration information for the provider-program relationship.
//     /// </summary>
//     public class ProviderProgram : IAuditableEntity, ISoftDeletable
//     {
//         public int Id { get; set; }
//         public int ProviderId { get; set; }
//         public int ProgramId { get; set; }
        
//         // Accreditation/Certification details
//         public string? CertificateNumber { get; set; } // Cert number for this specific program
//         public DateTime? IssueDate { get; set; }
//         public DateTime? ExpiryDate { get; set; }
//         public string? CertifyingOrganization { get; set; } // Who issued this accreditation
//         public string? Notes { get; set; }
//         public bool Active { get; set; } = true;

//         // Pricing details (moved from TrainingProvider)
//         public PricingStrategy PricingStrategy { get; set; } = PricingStrategy.PerPerson;
//         public decimal Charges { get; set; }

//         // Duration in hours - how long the provider delivers this program
//         public int DurationHours { get; set; }

//         // Navigation Properties
//         public virtual TrainingProvider Provider { get; set; } = null!;
//         public virtual TrainingProgram Program { get; set; } = null!;

//         // Audit Fields
//         public DateTime CreatedAt { get; set; }
//         public string CreatedBy { get; set; } = string.Empty;
//         public DateTime? UpdatedAt { get; set; }
//         public string UpdatedBy { get; set; } = string.Empty;

//         // Soft Delete Fields
//         public bool IsDeleted { get; set; }
//         public DateTime? DeletedAt { get; set; }
//         public string? DeletedBy { get; set; }
//     }
// }

