using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    public class TrainingEnrollment : IAuditableEntity
    {
        public int Id { get; set; }
        public int SessionId { get; set; }
        public int WorkerId { get; set; }
        public EnrollmentStatus Status { get; set; } = EnrollmentStatus.REGISTERED;
        public DateTime? CompletedAt { get; set; }
        public TrainingOutcome? Outcome { get; set; }
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual TrainingSession Session { get; set; } = null!;
        public virtual Worker Worker { get; set; } = null!;

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}

