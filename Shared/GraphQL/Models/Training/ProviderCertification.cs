using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    /// <summary>
    /// Tracks key certifications for training providers (e.g., ISO certifications, general trainer certifications).
    /// These are provider-level certifications, not program-specific accreditations.
    /// </summary>
    public class ProviderCertification : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int ProviderId { get; set; }
        
        // Certification details
        public string CertificationType { get; set; } = string.Empty; // e.g., "ISO 9001", "OSHA Trainer", etc.
        public string CertificateNumber { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string? IssuingAuthority { get; set; } // Organization that issued the certification
        public string? DocumentUrl { get; set; }
        public int? CertificateFileId { get; set; }
        public string? Notes { get; set; }
        public bool Active { get; set; } = true;

        // Navigation Properties
        public virtual TrainingProvider Provider { get; set; } = null!;
        public virtual FileMetadata? CertificateFile { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}

