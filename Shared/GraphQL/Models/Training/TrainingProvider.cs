using Shared.Interfaces;
using Shared.GraphQL.Models.Auth;
using Shared.GraphQL.Models.Certificates;

namespace Shared.GraphQL.Models.Training
{
    public class TrainingProvider : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int TenantId { get; set; } // Company/Tenant scope
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Contact { get; set; } = string.Empty;

        public ProviderType Type { get; set; } = ProviderType.External;
        public bool Active { get; set; } = true;

        // Navigation Properties
        public virtual Tenant Tenant { get; set; } = null!;
        public ICollection<TrainingSession> Sessions { get; set; } = new List<TrainingSession>();
        public ICollection<Certificate> IssuedCertificates { get; set; } = new List<Certificate>();

        // Provider certifications (ISO, general trainer certs, etc.)
        public ICollection<ProviderCertification> Certifications { get; set; } = new List<ProviderCertification>();

        // Program-specific mappings (junction table with programs) - includes pricing and duration
        public ICollection<ProviderProgram> Programs { get; set; } = new List<ProviderProgram>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }

    public enum ProviderType
    {
        Internal,    // Company's own trainers
        External,    // Third-party providers
        Partner,     // Strategic partners
        Certified    // Certified external providers
    }
}

