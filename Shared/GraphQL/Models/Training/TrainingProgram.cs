using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Training
{
    public class TrainingProgram : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int ValidityDays { get; set; }
        public CertificateType CertificateType { get; set; }
        public string? Prerequisites { get; set; } // JSON array of program IDs
        public bool Active { get; set; } = true;

        // Navigation Properties
        public ICollection<TrainingSession> Sessions { get; set; } = new List<TrainingSession>();
        public ICollection<TrainingCertificate> Certificates { get; set; } = new List<TrainingCertificate>();
        public ICollection<TradeRequirement> TradeRequirements { get; set; } = new List<TradeRequirement>();

        // Provider mappings for this program - includes accreditation, pricing, and duration
        public ICollection<ProviderProgram> Providers { get; set; } = new List<ProviderProgram>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}

