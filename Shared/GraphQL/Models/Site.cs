﻿using Shared.Interfaces;
using Shared.GraphQL.Models.Auth;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Shared.GraphQL.Models
{
    public class Site : IAuditableEntity, ISoftDeletable
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; }

        // Schema version for future migrations/compatibility
        [MaxLength(20)]
        public string SchemaVersion { get; set; } = "1.0";

        // Core JSON document containing all site data
        [Column(TypeName = "nvarchar(max)")]
        public string SiteDataJson { get; set; } = "{}";

        // Computed/cached fields for quick queries and indexing
        [MaxLength(500)]
        public string? Location { get; set; }

        [MaxLength(100)]
        public string? ProjectManager { get; set; }

        [MaxLength(20)]
        public string? Status { get; set; } // planning, active, on-hold, completed, cancelled

        [MaxLength(20)]
        public string? HealthStatus { get; set; } // green, amber, red

        [MaxLength(50)]
        public string? ProjectType { get; set; } // Residential, Commercial, Industrial, MixedUse

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? ProgressPercentage { get; set; }

        // Foreign Key
        public int TenantId { get; set; }

        // Navigation Properties
        public virtual Tenant Tenant { get; set; } = null!;

        // Audit fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }

        // Soft delete fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        // Helper methods for JSON manipulation
        public T? GetSiteData<T>() where T : class
        {
            if (string.IsNullOrEmpty(SiteDataJson))
                return null;

            try
            {
                return JsonSerializer.Deserialize<T>(SiteDataJson);
            }
            catch
            {
                return null;
            }
        }

        public void SetSiteData<T>(T data) where T : class
        {
            SiteDataJson = JsonSerializer.Serialize(data, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
            UpdateCachedFields();
        }

        public JsonDocument? GetSiteDataAsDocument()
        {
            if (string.IsNullOrEmpty(SiteDataJson))
                return null;

            try
            {
                return JsonDocument.Parse(SiteDataJson);
            }
            catch
            {
                return null;
            }
        }

        public void UpdateCachedFields()
        {
            var doc = GetSiteDataAsDocument();
            if (doc == null) return;

            try
            {
                // Update cached fields from JSON for indexing/quick queries
                if (doc.RootElement.TryGetProperty("projectDetails", out var projectDetails))
                {
                    if (projectDetails.TryGetProperty("name", out var nameElement) && !string.IsNullOrEmpty(nameElement.GetString()))
                        Name = nameElement.GetString()!;

                    if (projectDetails.TryGetProperty("projectType", out var typeElement))
                        ProjectType = typeElement.GetString();

                    if (projectDetails.TryGetProperty("keyPersonel", out var personnel))
                    {
                        if (personnel.TryGetProperty("engineer", out var engineerElement))
                            ProjectManager = engineerElement.GetString();
                    }
                }

                if (doc.RootElement.TryGetProperty("siteSpecification", out var siteSpec))
                {
                    if (siteSpec.TryGetProperty("siteLocation", out var siteLocation))
                    {
                        if (siteLocation.TryGetProperty("locationMap", out var locationElement))
                            Location = locationElement.GetString();
                    }
                }

                if (doc.RootElement.TryGetProperty("projectTimeline", out var timeline))
                {
                    if (timeline.TryGetProperty("startDate", out var startElement) &&
                        DateTime.TryParse(startElement.GetString(), out var start))
                        StartDate = start;

                    if (timeline.TryGetProperty("endDate", out var endElement) &&
                        DateTime.TryParse(endElement.GetString(), out var end))
                        EndDate = end;
                }
            }
            catch
            {
                // Ignore parsing errors for cached fields
            }
        }
    }
}
