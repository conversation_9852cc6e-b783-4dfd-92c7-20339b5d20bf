using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Shared.GraphQL.Models.Auth;
using Shared.Interfaces;
using Shared.DTOs;

namespace Shared.GraphQL.Models.Notifications
{
    public class Notification : IAuditableEntity
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Type { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Message { get; set; } = string.Empty;

        public NotificationPriority Priority { get; set; } = NotificationPriority.Medium;

        public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

        [MaxLength(100)]
        public string? Entity { get; set; }

        [MaxLength(50)]
        public string? Operation { get; set; }

        public string? Metadata { get; set; } // JSON string

        public DateTime? SentAt { get; set; }
        public DateTime? ReadAt { get; set; }
        public DateTime? ExpiresAt { get; set; }

        [MaxLength(500)]
        public string? ActionUrl { get; set; }

        [MaxLength(100)]
        public string? ActionLabel { get; set; }

        // Foreign Keys
        public int UserId { get; set; }
        public int TenantId { get; set; }

        // Navigation Properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Tenant Tenant { get; set; } = null!;
        public virtual ICollection<NotificationDelivery> Deliveries { get; set; } = new List<NotificationDelivery>();

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
