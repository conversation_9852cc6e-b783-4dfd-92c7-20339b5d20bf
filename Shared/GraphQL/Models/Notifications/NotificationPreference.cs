using System.ComponentModel.DataAnnotations;
using Shared.GraphQL.Models.Auth;
using Shared.Interfaces;
using Shared.DTOs;

namespace Shared.GraphQL.Models.Notifications
{
    public class NotificationPreference : IAuditableEntity
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string NotificationType { get; set; } = string.Empty;

        public bool InAppEnabled { get; set; } = true;
        public bool EmailEnabled { get; set; } = true;
        public bool SmsEnabled { get; set; } = false;

        public NotificationPriority MinimumPriority { get; set; } = NotificationPriority.Low;

        public bool DoNotDisturbEnabled { get; set; } = false;
        public TimeSpan? DoNotDisturbStart { get; set; }
        public TimeSpan? DoNotDisturbEnd { get; set; }

        // Foreign Keys
        public int UserId { get; set; }

        // Navigation Properties
        public virtual ApplicationUser User { get; set; } = null!;

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
