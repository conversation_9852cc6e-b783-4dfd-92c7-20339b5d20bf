using System;
using System.ComponentModel.DataAnnotations;
using Shared.Interfaces;
using Shared.DTOs;

namespace Shared.GraphQL.Models.Notifications
{
    public class NotificationDelivery : IAuditableEntity
    {
        public int Id { get; set; }

        public NotificationChannel Channel { get; set; }

        public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

        [MaxLength(500)]
        public string? Recipient { get; set; } // Email address, phone number, etc.

        public DateTime? SentAt { get; set; }
        public DateTime? DeliveredAt { get; set; }
        public DateTime? FailedAt { get; set; }

        [MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;
        public DateTime? NextRetryAt { get; set; }

        // Foreign Keys
        public int NotificationId { get; set; }

        // Navigation Properties
        public virtual Notification Notification { get; set; } = null!;

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
