using Shared.Interfaces;

namespace Shared.GraphQL.Models;

public class Skill : IAuditableEntity
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public ICollection<Worker> Workers { get; set; } = new List<Worker>();

    // Audit Fields
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }
}
