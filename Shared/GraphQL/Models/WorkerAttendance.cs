using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class WorkerAttendance : IAuditableEntity
    {
        public int Id { get; set; }
        public int WorkerId { get; set; }
        public Worker Worker { get; set; }
        public DateTime CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public string Status { get; set; } // Present, Late, Absent, etc.
        public string? Notes { get; set; }
        public bool IsVerifiedByHikvision { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
} 