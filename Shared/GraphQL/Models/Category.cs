using System;
using System.Collections.Generic;
using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    public class Category : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;

        // Jobs using this category
        public ICollection<Job> Jobs { get; set; } = new List<Job>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
