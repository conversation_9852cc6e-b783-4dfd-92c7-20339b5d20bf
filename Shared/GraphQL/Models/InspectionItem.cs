using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    /// <summary>
    /// Entity representing an individual inspection item
    /// </summary>
    public class InspectionItem : IAuditableEntity, ISoftDeletable
    {
        /// <summary>
        /// Unique identifier for the inspection item
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Description of what is being inspected
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Whether the inspection item passed (true) or failed (false)
        /// </summary>
        public bool IsTrue { get; set; }

        /// <summary>
        /// Additional remarks or comments for this inspection item
        /// </summary>
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// Foreign key to the parent inspection
        /// </summary>
        public int InspectionId { get; set; }

        /// <summary>
        /// Navigation property to the parent inspection
        /// </summary>
        public virtual Inspection Inspection { get; set; } = null!;

        /// <summary>
        /// Collection of image files associated with this inspection item
        /// </summary>
        public virtual ICollection<FileMetadata> ImageFiles { get; set; } = [];

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
