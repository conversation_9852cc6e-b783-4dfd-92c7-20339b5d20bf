using System.ComponentModel.DataAnnotations;
using Shared.Enums;

namespace Shared.GraphQL.Models.Permits
{
    /// <summary>
    /// Hot Work Permit - derived from base Permit class
    /// </summary>
    public class HotWorkPermit : Permit
    {
        public HotWorkPermit()
        {
            PermitType = PermitType.HOT_WORK_PERMIT;
        }

        // [StringLength(500)]
        // public string NatureOfWork { get; set; } = string.Empty;

        // [StringLength(500)]
        // public string FireExtinguishers { get; set; } = string.Empty;

        // JSON object for fire safety supervisor
        public FireSafetySupervisor FireSafetySupervisor { get; set; } = null!;
    }
}
