using Shared.Enums;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Certificates
{
    /// <summary>
    /// Generic Certificate model - not tied to any specific type (training, medical, etc.)
    /// Uses composition pattern for maximum flexibility
    /// </summary>
    public class Certificate : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public int WorkerId { get; set; }
        public string CertificateNo { get; set; } = string.Empty;
        public string CertificateType { get; set; } = string.Empty; // "TrainingCertificate", "MedicalCertificate", "SafetyCertificate"
        public string Source { get; set; } = string.Empty; // "TrainingSession", "ExternalProvider", "Manual", "Medical"
        public string SourceId { get; set; } = string.Empty; // Reference to source (session ID, provider ID, etc.)
        public DateTime IssueDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public CertificateStatus Status { get; set; } = CertificateStatus.ISSUED;
        public string IssuedBy { get; set; } = string.Empty;
        public string? Metadata { get; set; } // JSON for flexible data storage
        public string? Notes { get; set; }

        // File attachments
        public int? CertificateFileId { get; set; }
        public virtual FileMetadata? CertificateFile { get; set; }

        // Navigation Properties
        public virtual Worker Worker { get; set; } = null!;

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}





