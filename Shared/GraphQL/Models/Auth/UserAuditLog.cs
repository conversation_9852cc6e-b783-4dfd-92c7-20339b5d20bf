using System.ComponentModel.DataAnnotations;

namespace Shared.GraphQL.Models.Auth
{
    public class UserAuditLog
    {
        public int Id { get; set; }

        public AuditAction Action { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [MaxLength(4000)]
        public string? AdditionalData { get; set; }

        public DateTime CreatedAt { get; set; }

        // Foreign Key - nullable for system actions
        public int? ApplicationUserId { get; set; }

        // Navigation Property - Identity user only
        public virtual ApplicationUser? ApplicationUser { get; set; }
    }

    public enum AuditAction
    {
        Login = 1,
        Logout = 2,
        FailedLogin = 3,
        PasswordChanged = 4,
        EmailChanged = 5,
        ProfileUpdated = 6,
        AccountLocked = 7,
        AccountUnlocked = 8,
        PermissionsChanged = 9,
        TwoFactorEnabled = 10,
        TwoFactorDisabled = 11,
        SessionTerminated = 12,
        DataAccessed = 13,
        DataModified = 14,
        SecurityEvent = 15
    }
}
