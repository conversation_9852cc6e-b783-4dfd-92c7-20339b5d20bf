using System.ComponentModel.DataAnnotations;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Auth
{
    public class RefreshToken : IAuditableEntity
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string Token { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string JwtId { get; set; } = string.Empty;

        public DateTime ExpiresAt { get; set; }
        public bool IsRevoked { get; set; } = false;
        public DateTime? RevokedAt { get; set; }
        public string? RevokedReason { get; set; }

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        // Foreign Key - Identity user only
        public int ApplicationUserId { get; set; } // Identity user ID (required)

        // Navigation Property
        public virtual ApplicationUser ApplicationUser { get; set; } = null!; // Identity user

        // Helper Properties
        public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
        public bool IsActive => !IsRevoked && !IsExpired;

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
