using System.ComponentModel.DataAnnotations;
using Shared.Interfaces;
using Shared.Enums;

namespace Shared.GraphQL.Models.Auth
{
    public class Role : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        public bool IsSystemRole { get; set; } = false;

        // Permission flags for each resource type
        public byte WorkerPermissions { get; set; } = 0;
        public byte SitePermissions { get; set; } = 0;
        public byte TrainingPermissions { get; set; } = 0;
        public byte DocumentPermissions { get; set; } = 0;
        public byte PPEPermissions { get; set; } = 0;
        public byte RoleManagementPermissions { get; set; } = 0;
        public byte ReportPermissions { get; set; } = 0;

        // Foreign Key
        public int TenantId { get; set; }

        // Navigation Properties
        public virtual Tenant Tenant { get; set; } = null!;
        public virtual ICollection<ApplicationUser> ApplicationUsers { get; set; } = new List<ApplicationUser>();

        // Helper methods to check permissions
        public bool HasWorkersPermission(WorkersPermissions permission)
        {
            return (WorkerPermissions & (byte)permission) == (byte)permission;
        }

        public bool HasSitesPermission(SitesPermissions permission)
        {
            return (SitePermissions & (byte)permission) == (byte)permission;
        }

        public bool HasTrainingsPermission(TrainingsPermissions permission)
        {
            return (TrainingPermissions & (byte)permission) == (byte)permission;
        }

        public bool HasDocsPermission(DocsPermissions permission)
        {
            return (DocumentPermissions & (byte)permission) == (byte)permission;
        }

        public bool HasPPEPermission(PPEPermissions permission)
        {
            return (PPEPermissions & (byte)permission) == (byte)permission;
        }

        public bool HasRoleManagementPermission(RoleManagementPermissions permission)
        {
            return (RoleManagementPermissions & (byte)permission) == (byte)permission;
        }

        public bool HasReportsPermission(ReportsPermissions permission)
        {
            return (ReportPermissions & (byte)permission) == (byte)permission;
        }

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }

        // ISoftDeletable
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
