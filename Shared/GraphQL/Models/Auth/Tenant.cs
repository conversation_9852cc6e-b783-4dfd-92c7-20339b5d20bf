using System.ComponentModel.DataAnnotations;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Auth
{
    public class Tenant : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        [MaxLength(100)]
        public string Subdomain { get; set; } = string.Empty;

        [MaxLength(255)]
        public string? LogoUrl { get; set; }

        [MaxLength(255)]
        public string? Website { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(255)]
        [EmailAddress]
        public string? Email { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        public TenantStatus Status { get; set; } = TenantStatus.Active;

        public DateTime? SubscriptionExpiresAt { get; set; }
        public int MaxUsers { get; set; } = 100;
        public int MaxSites { get; set; } = 10;

        // Configuration settings stored as JSON
        [MaxLength(4000)]
        public string? Settings { get; set; }

        // Navigation Properties
        public virtual ICollection<ApplicationUser> ApplicationUsers { get; set; } = new List<ApplicationUser>(); // Identity users
        public virtual ICollection<Role> Roles { get; set; } = new List<Role>();
        public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }

        // ISoftDeletable
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }

    public enum TenantStatus
    {
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        Trial = 4,
        Expired = 5
    }
}
