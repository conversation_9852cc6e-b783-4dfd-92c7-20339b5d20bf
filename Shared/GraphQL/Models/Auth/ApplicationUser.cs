using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Auth
{
    /// <summary>
    /// Application user extending IdentityUser with custom properties
    /// Maintains compatibility with existing User model while adding Identity features
    /// </summary>
    public class ApplicationUser : IdentityUser<int>, IAuditableEntity, ISoftDeletable
    {
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(20)]
        public override string? PhoneNumber { get; set; }
        
        // Backward compatibility properties
        [NotMapped]
        public string? Phone { get => PhoneNumber; set => PhoneNumber = value; }
        
        [NotMapped] 
        public bool PhoneConfirmed { get => PhoneNumberConfirmed; set => PhoneNumberConfirmed = value; }
        
        // Legacy compatibility - Identity doesn't use Salt
        [NotMapped]
        public string Salt { get; set; } = string.Empty;

        // Security and audit fields
        public DateTime? LastLoginAt { get; set; }
        public string? LastLoginIp { get; set; }
        public int FailedLoginAttempts { get; set; } = 0;
        public DateTime? LockedOutUntil { get; set; }

        public UserStatus Status { get; set; } = UserStatus.Active;

        // Foreign Keys - maintain existing relationships
        public int? RoleId { get; set; } // Nullable for Identity users
        public int TenantId { get; set; }

        // Navigation Properties - preserve existing relationships
        public virtual Role? Role { get; set; }
        public virtual Tenant Tenant { get; set; } = null!;
        public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();
        public virtual ICollection<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();
        public virtual ICollection<UserAuditLog> AuditLogs { get; set; } = new List<UserAuditLog>();

        // Computed Properties
        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        [NotMapped]
        public bool IsLocked => LockedOutUntil.HasValue && LockedOutUntil > DateTime.UtcNow;

        [NotMapped]
        public bool IsActive => Status == UserStatus.Active && !IsDeleted && !IsLocked && !LockoutEnabled;

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        // Security methods
        public bool IsAccountLocked()
        {
            return LockoutEnd.HasValue && LockoutEnd > DateTimeOffset.UtcNow;
        }

        public void ResetFailedAttempts()
        {
            FailedLoginAttempts = 0;
            AccessFailedCount = 0;
        }

        public void IncrementFailedAttempts()
        {
            FailedLoginAttempts++;
        }
    }

    /// <summary>
    /// Custom Identity Role for multi-tenant support
    /// </summary>
    public class ApplicationRole : IdentityRole<int>
    {
        public int TenantId { get; set; }
        public virtual Tenant Tenant { get; set; } = null!;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        public bool IsSystemRole { get; set; } = false;
    }

    // Custom Identity entities for type safety
    public class ApplicationUserClaim : IdentityUserClaim<int> { }
    public class ApplicationUserRole : IdentityUserRole<int> { }
    public class ApplicationUserLogin : IdentityUserLogin<int> { }
    public class ApplicationUserToken : IdentityUserToken<int> { }
    public class ApplicationRoleClaim : IdentityRoleClaim<int> { }

    public enum UserStatus
    {
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        PendingVerification = 4
    }
}
