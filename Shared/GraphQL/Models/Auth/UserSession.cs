using System.ComponentModel.DataAnnotations;
using Shared.Interfaces;

namespace Shared.GraphQL.Models.Auth
{
    public class UserSession : IAuditableEntity
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string SessionId { get; set; } = string.Empty;

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [MaxLength(100)]
        public string? DeviceType { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        public DateTime ExpiresAt { get; set; }
        public DateTime? LastActivityAt { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? EndedAt { get; set; }
        public string? EndReason { get; set; }

        // Foreign Key
        public int ApplicationUserId { get; set; }

        // Navigation Properties
        public virtual ApplicationUser ApplicationUser { get; set; } = null!;

        // Helper Properties
        public bool IsExpired => DateTime.UtcNow >= ExpiresAt;
        public bool IsValid => IsActive && !IsExpired;

        // IAuditableEntity
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
