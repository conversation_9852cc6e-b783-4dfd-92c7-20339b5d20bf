using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types;

public class JobType : ObjectType<Job>
{
    protected override void Configure(IObjectTypeDescriptor<Job> descriptor)
    {
        descriptor.Field(j => j.Id).Type<NonNullType<IntType>>();
        descriptor.Field(j => j.Title).Type<NonNullType<StringType>>();
        descriptor.Field(j => j.Description).Type<StringType>();
        descriptor.Field(j => j.Location).Type<StringType>();
        descriptor.Field(j => j.PPEs).Type<ListType<StringType>>();
        descriptor.Field(j => j.Status).Type<NonNullType<EnumType<Shared.Enums.JobStatus>>>();
        descriptor.Field(j => j.PrecautionsRequired).Type<ListType<StringType>>();
        descriptor.Field(j => j.RequiredPermits).Type<ListType<EnumType<Shared.Enums.PermitType>>>();
        descriptor.Field(j => j.TimeForCompletion).Type<TimeSpanType>();
        descriptor.Field(j => j.StartDate).Type<NonNullType<DateTimeType>>();
        descriptor.Field(j => j.DueDate).Type<DateTimeType>();

        descriptor.Field(j => j.ExcavationFields).Type<ExcavationFieldsType>();
        descriptor.Field(j => j.HotWorkFields).Type<HotWorkFieldsType>();
        descriptor.Field(j => j.WorkAtHeightFields).Type<WorkAtHeightFieldsType>();
        
        descriptor.Field(j => j.CalculatedDueDate).Type<NonNullType<DateTimeType>>();

        // Category
        descriptor.Field(j => j.CategoryId).Type<IntType>();
        descriptor.Field(j => j.Category).Type<CategoryType>();

        // Request information
        descriptor.Field(j => j.RequestedById).Type<IntType>();
        descriptor.Field(j => j.RequestedBy).Type<WorkerType>();
        descriptor.Field(j => j.RequestedDate).Type<DateTimeType>();

        // Block information
        descriptor.Field(j => j.BlockedById).Type<IntType>();
        descriptor.Field(j => j.BlockedBy).Type<WorkerType>();
        descriptor.Field(j => j.BlockedDate).Type<DateTimeType>();

        // Review information
        descriptor.Field(j => j.ReviewedById).Type<IntType>();
        descriptor.Field(j => j.ReviewedBy).Type<WorkerType>();
        descriptor.Field(j => j.ReviewedDate).Type<DateTimeType>();

        // Approval information
        descriptor.Field(j => j.ApprovedById).Type<IntType>();
        descriptor.Field(j => j.ApprovedBy).Type<WorkerType>();
        descriptor.Field(j => j.ApprovedDate).Type<DateTimeType>();

        // Finish information
        descriptor.Field(j => j.FinishedById).Type<IntType>();
        descriptor.Field(j => j.FinishedBy).Type<WorkerType>();
        descriptor.Field(j => j.FinishedDate).Type<DateTimeType>();

        // Chief Engineer and Workers
        descriptor.Field(j => j.ChiefEngineerId).Type<IntType>();
        descriptor.Field(j => j.ChiefEngineer).Type<WorkerType>();
        // descriptor.Field(j => j.Workers).Type<ListType<WorkerType>>();
        descriptor.Field(j => j.RequiredTrades).Type<ListType<TradeType>>();
        descriptor.Field(j => j.RequiredTrainings).Type<ListType<LegacyTrainingType>>();

        // Hazards and Documents
        descriptor.Field(j => j.Hazards).Type<ListType<HazardType>>();
        descriptor.Field(j => j.Documents).Type<ListType<DocumentFileType>>();

        // Audit fields
        descriptor.Field(j => j.CreatedAt).Type<NonNullType<DateTimeType>>();
        descriptor.Field(j => j.CreatedBy).Type<NonNullType<StringType>>();
        descriptor.Field(j => j.UpdatedAt).Type<DateTimeType>();
        descriptor.Field(j => j.UpdatedBy).Type<StringType>();

        descriptor.Ignore(t => t.Validate(default!));

        // Soft delete fields are typically not exposed in GraphQL
    }
}

public class ExcavationFieldsType : ObjectType<ExcavationFields>
{
    protected override void Configure(IObjectTypeDescriptor<ExcavationFields> descriptor)
    {
        descriptor.Field(e => e.DepthOfExcavation).Type<ListType<StringType>>();
        descriptor.Field(e => e.ExcavationProtectionSystems).Type<ListType<StringType>>();
        descriptor.Field(e => e.ExcavationEquipmentsToBeUsed).Type<ListType<StringType>>();
    }
}

public class HotWorkFieldsType : ObjectType<HotWorkFields>
{
    protected override void Configure(IObjectTypeDescriptor<HotWorkFields> descriptor)
    {
        descriptor.Field(h => h.FireExtinguishers).Type<ListType<StringType>>();
        descriptor.Field(h => h.NatureOfHotWorks).Type<ListType<StringType>>();
    }
}

public class WorkAtHeightFieldsType : ObjectType<WorkAtHeightFields>
{
    protected override void Configure(IObjectTypeDescriptor<WorkAtHeightFields> descriptor)
    {
        descriptor.Field(w => w.ModesOfAccessToBeUsed).Type<ListType<StringType>>();
    }
}
