using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;

namespace Shared.GraphQL.Types.Training
{
    public class TrainingSessionType : ObjectType<TrainingSession>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingSession> descriptor)
        {
            descriptor.Field(ts => ts.Id).Type<NonNullType<IntType>>();
            descriptor.Field(ts => ts.ProgramId).Type<NonNullType<IntType>>();
            descriptor.Field(ts => ts.ProviderId).Type<NonNullType<IntType>>();
            descriptor.Field(ts => ts.SiteId).Type<IntType>();
            descriptor.Field(ts => ts.Mode).Type<NonNullType<EnumType<TrainingMode>>>();
            descriptor.Field(ts => ts.Location).Type<NonNullType<StringType>>();
            descriptor.Field(ts => ts.StartDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(ts => ts.EndDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(ts => ts.Status).Type<NonNullType<EnumType<TrainingSessionStatus>>>();
            descriptor.Field(ts => ts.Capacity).Type<NonNullType<IntType>>();
            descriptor.Field(ts => ts.Notes).Type<StringType>();

            // JSON objects (like toolbox)
            descriptor.Field(ts => ts.Conductor).Type<TrainingConductorType>();

            // Navigation properties
            descriptor.Field(ts => ts.Program).Type<TrainingProgramType>();
            descriptor.Field(ts => ts.Provider).Type<TrainingProviderType>();
            descriptor.Field(ts => ts.Site).Type<SiteType>();
            descriptor.Field(ts => ts.SessionPictureFile).Type<FileMetadataType>();
            descriptor.Field(ts => ts.Enrollments).Type<ListType<TrainingEnrollmentType>>();

            // Audit fields
            descriptor.Field(ts => ts.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(ts => ts.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(ts => ts.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(ts => ts.UpdatedBy).Type<StringType>();
        }
    }

    public class TrainingConductorType : ObjectType<TrainingConductor>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingConductor> descriptor)
        {
            descriptor.Field(tc => tc.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(tc => tc.Name).Type<NonNullType<StringType>>();
            descriptor.Field(tc => tc.SignatureFileId).Type<NonNullType<StringType>>();
        }
    }
}
