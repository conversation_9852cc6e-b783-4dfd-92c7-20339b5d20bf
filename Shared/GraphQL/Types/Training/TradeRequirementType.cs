using HotChocolate.Types;
using Shared.GraphQL.Models.Training;

namespace Shared.GraphQL.Types.Training
{
    public class TradeRequirementType : ObjectType<TradeRequirement>
    {
        protected override void Configure(IObjectTypeDescriptor<TradeRequirement> descriptor)
        {
            descriptor.Field(tr => tr.Id).Type<NonNullType<IntType>>();
            descriptor.Field(tr => tr.TradeId).Type<NonNullType<IntType>>();
            descriptor.Field(tr => tr.ProgramId).Type<NonNullType<IntType>>();
            descriptor.Field(tr => tr.Mandatory).Type<NonNullType<BooleanType>>();
            descriptor.Field(tr => tr.Notes).Type<StringType>();

            // Navigation properties
            descriptor.Field(tr => tr.Trade).Type<TradeType>();
            descriptor.Field(tr => tr.Program).Type<TrainingProgramType>();

            // Audit fields
            descriptor.Field(tr => tr.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tr => tr.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(tr => tr.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(tr => tr.UpdatedBy).Type<StringType>();
        }
    }
}
