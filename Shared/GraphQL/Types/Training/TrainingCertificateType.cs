using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;

namespace Shared.GraphQL.Types.Training
{
    public class TrainingCertificateType : ObjectType<TrainingCertificate>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingCertificate> descriptor)
        {
            descriptor.Field(tc => tc.Id).Type<NonNullType<IntType>>();
            descriptor.Field(tc => tc.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(tc => tc.ProgramId).Type<NonNullType<IntType>>();
            descriptor.Field(tc => tc.SessionId).Type<IntType>();
            descriptor.Field(tc => tc.CertificateNo).Type<StringType>(); // Now nullable
            descriptor.Field(tc => tc.ProviderName).Type<StringType>(); // Provider name instead of ID
            descriptor.Field(tc => tc.IssueDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tc => tc.ExpiryDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tc => tc.Status).Type<NonNullType<EnumType<CertificateStatus>>>();
            descriptor.Field(tc => tc.FileUrl).Type<StringType>();
            descriptor.Field(tc => tc.Notes).Type<StringType>();

            // Navigation properties
            descriptor.Field(tc => tc.Worker).Type<WorkerType>();
            descriptor.Field(tc => tc.Program).Type<TrainingProgramType>();
            descriptor.Field(tc => tc.Session).Type<TrainingSessionType>();
            descriptor.Field(tc => tc.CertificateFile).Type<FileMetadataType>();

            // Computed fields
            descriptor.Field("daysUntilExpiry")
                .Type<IntType>()
                .Resolve(context =>
                {
                    var certificate = context.Parent<TrainingCertificate>();
                    return (certificate.ExpiryDate - DateTime.UtcNow).Days;
                });

            descriptor.Field("isExpired")
                .Type<NonNullType<BooleanType>>()
                .Resolve(context =>
                {
                    var certificate = context.Parent<TrainingCertificate>();
                    return certificate.ExpiryDate < DateTime.UtcNow;
                });

            descriptor.Field("isExpiringSoon")
                .Type<NonNullType<BooleanType>>()
                .Resolve(context =>
                {
                    var certificate = context.Parent<TrainingCertificate>();
                    return certificate.ExpiryDate <= DateTime.UtcNow.AddDays(30);
                });

            // Audit fields
            descriptor.Field(tc => tc.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tc => tc.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(tc => tc.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(tc => tc.UpdatedBy).Type<StringType>();
        }
    }
}
