using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;

namespace Shared.GraphQL.Types.Training
{
    public class TrainingProgramType : ObjectType<TrainingProgram>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingProgram> descriptor)
        {
            descriptor.Field(tp => tp.Id).Type<NonNullType<IntType>>();
            descriptor.Field(tp => tp.Code).Type<NonNullType<StringType>>();
            descriptor.Field(tp => tp.Title).Type<NonNullType<StringType>>();
            descriptor.Field(tp => tp.Description).Type<StringType>();
            descriptor.Field(tp => tp.ValidityDays).Type<NonNullType<IntType>>();
            descriptor.Field(tp => tp.CertificateType).Type<NonNullType<EnumType<Shared.GraphQL.Models.Training.CertificateType>>>();
            descriptor.Field(tp => tp.Prerequisites).Type<StringType>();
            descriptor.Field(tp => tp.Active).Type<NonNullType<BooleanType>>();

            // Navigation properties
            descriptor.Field(tp => tp.Sessions).Type<ListType<TrainingSessionType>>();
            descriptor.Field(tp => tp.Certificates).Type<ListType<TrainingCertificateType>>();
            descriptor.Field(tp => tp.TradeRequirements).Type<ListType<TradeRequirementType>>();

            // Audit fields
            descriptor.Field(tp => tp.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tp => tp.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(tp => tp.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(tp => tp.UpdatedBy).Type<StringType>();
        }
    }
}


