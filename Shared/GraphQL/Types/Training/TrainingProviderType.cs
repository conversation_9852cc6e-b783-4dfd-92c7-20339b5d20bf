using HotChocolate.Types;
using Shared.GraphQL.Models.Training;

namespace Shared.GraphQL.Types.Training
{
    public class TrainingProviderType : ObjectType<TrainingProvider>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingProvider> descriptor)
        {
            descriptor.Field(tp => tp.Id).Type<NonNullType<IntType>>();
            descriptor.Field(tp => tp.Name).Type<NonNullType<StringType>>();
            descriptor.Field(tp => tp.Description).Type<StringType>();
            descriptor.Field(tp => tp.Contact).Type<StringType>();
            descriptor.Field(tp => tp.Certifications).Type<StringType>();
            descriptor.Field(tp => tp.Active).Type<NonNullType<BooleanType>>();

            // Navigation properties
            descriptor.Field(tp => tp.Sessions).Type<ListType<TrainingSessionType>>();
            descriptor.Field(tp => tp.IssuedCertificates).Type<ListType<TrainingCertificateType>>();

            // Audit fields
            descriptor.Field(tp => tp.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(tp => tp.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(tp => tp.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(tp => tp.UpdatedBy).Type<StringType>();
        }
    }
}


