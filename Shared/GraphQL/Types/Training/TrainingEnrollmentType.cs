using HotChocolate.Types;
using Shared.GraphQL.Models.Training;
using Shared.Enums;

namespace Shared.GraphQL.Types.Training
{
    public class TrainingEnrollmentType : ObjectType<TrainingEnrollment>
    {
        protected override void Configure(IObjectTypeDescriptor<TrainingEnrollment> descriptor)
        {
            descriptor.Field(te => te.Id).Type<NonNullType<IntType>>();
            descriptor.Field(te => te.SessionId).Type<NonNullType<IntType>>();
            descriptor.Field(te => te.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(te => te.Status).Type<NonNullType<EnumType<EnrollmentStatus>>>();
            descriptor.Field(te => te.CompletedAt).Type<DateTimeType>();
            descriptor.Field(te => te.Outcome).Type<EnumType<TrainingOutcome>>();
            descriptor.Field(te => te.Notes).Type<StringType>();

            // Navigation properties
            descriptor.Field(te => te.Session).Type<TrainingSessionType>();
            descriptor.Field(te => te.Worker).Type<WorkerType>();

            // Audit fields
            descriptor.Field(te => te.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(te => te.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(te => te.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(te => te.UpdatedBy).Type<StringType>();
        }
    }
}
