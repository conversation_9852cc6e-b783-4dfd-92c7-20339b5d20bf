﻿// using HotChocolate.Types;
// using Shared.GraphQL.Models;
// using HotChocolate;
// using Shared.Enums;
// using Task = Shared.GraphQL.Models.Task;

// namespace Shared.GraphQL.Types
// {
//     public class TaskType : ObjectType<Task>
//     {
//         protected override void Configure(IObjectTypeDescriptor<Task> descriptor)
//         {
//             descriptor.Field(x => x.Id).Type<NonNullType<IntType>>();
//             descriptor.Field(x => x.Name).Type<NonNullType<StringType>>();
//             descriptor.Field(x => x.Type).Type<NonNullType<StringType>>();
//             descriptor.Field(x => x.Description).Type<NonNullType<StringType>>();
//             descriptor.Field(x => x.Status).Type<NonNullType<EnumType<Shared.Enums.TaskStatus>>>();
//             descriptor.Field(x => x.TaskNumber).Type<NonNullType<StringType>>();
//             descriptor.Field(x => x.Priority).Type<NonNullType<EnumType<Shared.Enums.TaskPriority>>>();
//             descriptor.Field(x => x.TimeForCompletion).Type<StringType>();
//             descriptor.Field(x => x.DueDate).Type<DateTimeType>();
//             descriptor.Field(x => x.StartDate).Type<DateTimeType>();
//             descriptor.Field(x => x.Category).Type<StringType>();
//             descriptor.Field(x => x.InspectionStatus).Type<NonNullType<EnumType<Shared.Enums.InspectionStatus>>>();
//             descriptor.Field(x => x.AssociatedMethodStatement).Type<StringType>();
//             descriptor.Field(x => x.ChiefEngineerId).Type<IntType>();
//             descriptor.Field(x => x.ChiefEngineer).Type<WorkerType>();
//             descriptor.Field(x => x.WorkersAssigned).Type<ListType<WorkerType>>();
//             descriptor.Field(x => x.EquipmentInvolved).Type<ListType<EquipmentType>>();
//             descriptor.Field(x => x.CreatedAt).Type<NonNullType<DateTimeType>>();
//             descriptor.Field(x => x.CreatedBy).Type<NonNullType<StringType>>();
//             descriptor.Field(x => x.UpdatedAt).Type<DateTimeType>();
//             descriptor.Field(x => x.UpdatedBy).Type<StringType>();
//         }
//     }
// }
