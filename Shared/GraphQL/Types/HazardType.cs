using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class HazardType : ObjectType<Hazard>
    {
        protected override void Configure(IObjectTypeDescriptor<Hazard> descriptor)
        {
            descriptor.Field(h => h.Id).Type<NonNullType<IntType>>();
            descriptor.Field(h => h.Description).Type<NonNullType<StringType>>();

            // Job relationship
            descriptor.Field(h => h.JobId).Type<NonNullType<IntType>>();
            descriptor.Field(h => h.Job).Type<JobType>();

            // Control measures
            descriptor.Field(h => h.ControlMeasures).Type<ListType<ControlMeasureType>>();

            // Audit fields
            descriptor.Field(h => h.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(h => h.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(h => h.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(h => h.UpdatedBy).Type<StringType>();
        }
    }
}
