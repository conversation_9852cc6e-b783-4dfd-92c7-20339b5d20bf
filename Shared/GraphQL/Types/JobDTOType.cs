
// using HotChocolate.Types;
// using Shared.GraphQL.DTOs;

// namespace Shared.GraphQL.Types;

// public class JobDTOType : ObjectType<JobDTO>
//     {
//         protected override void Configure(IObjectTypeDescriptor<JobDTO> descriptor)
//         {
//             descriptor.Field(j => j.Id).Type<NonNullType<IntType>>();
//             descriptor.Field(j => j.Title).Type<NonNullType<StringType>>();
//             descriptor.Field(j => j.Description).Type<StringType>();
//             descriptor.Field(j => j.Location).Type<StringType>();
//             descriptor.Field(j => j.PPE).Type<StringType>();
//             descriptor.Field(j => j.Status).Type<NonNullType<EnumType<Shared.Enums.JobStatus>>>();
//             descriptor.Field(j => j.RequiredPermits).Type<ListType<EnumType<Shared.Enums.PermitType>>>();
//             descriptor.Field(j => j.TimeForCompletion).Type<TimeSpanType>();
//             descriptor.Field(j => j.StartDate).Type<NonNullType<DateTimeType>>();
//             descriptor.Field(j => j.DueDate).Type<DateTimeType>();
//             descriptor.Field(j => j.DepthOfExcavation).Type<StringType>();
//             descriptor.Field(j => j.ExcavationProtectionSystems).Type<StringType>();
//             descriptor.Field(j => j.ExcavationEquipmentsToBeUsed).Type<StringType>();
//             descriptor.Field(j => j.FireExtinguishers).Type<StringType>();
//             descriptor.Field(j => j.NatureOfHotWork).Type<StringType>();
//             descriptor.Field(j => j.CalculatedDueDate).Type<NonNullType<DateTimeType>>();

//             // Category
//             descriptor.Field(j => j.CategoryId).Type<IntType>();
//             descriptor.Field(j => j.Category).Type<CategoryType>();

//             // Request information
//             descriptor.Field(j => j.RequestedById).Type<IntType>();
//             descriptor.Field(j => j.RequestedBy).Type<WorkerType>();
//             descriptor.Field(j => j.RequestedDate).Type<DateTimeType>();

//             // Block information
//             descriptor.Field(j => j.BlockedById).Type<IntType>();
//             descriptor.Field(j => j.BlockedBy).Type<WorkerType>();
//             descriptor.Field(j => j.BlockedDate).Type<DateTimeType>();

//             // Review information
//             descriptor.Field(j => j.ReviewedById).Type<IntType>();
//             descriptor.Field(j => j.ReviewedBy).Type<WorkerType>();
//             descriptor.Field(j => j.ReviewedDate).Type<DateTimeType>();

//             // Approval information
//             descriptor.Field(j => j.ApprovedById).Type<IntType>();
//             descriptor.Field(j => j.ApprovedBy).Type<WorkerType>();
//             descriptor.Field(j => j.ApprovedDate).Type<DateTimeType>();

//             // Finish information
//             descriptor.Field(j => j.FinishedById).Type<IntType>();
//             descriptor.Field(j => j.FinishedBy).Type<WorkerType>();
//             descriptor.Field(j => j.FinishedDate).Type<DateTimeType>();

//             // Chief Engineer and Workers
//             descriptor.Field(j => j.ChiefEngineerId).Type<IntType>();
//             descriptor.Field(j => j.ChiefEngineer).Type<WorkerType>();
//             descriptor.Field(j => j.Workers).Type<ListType<WorkerType>>();

//             // Hazards and Documents
//             descriptor.Field(j => j.Hazards).Type<ListType<HazardType>>();
//             descriptor.Field(j => j.Documents).Type<ListType<DocumentFileType>>();

//             // Audit fields
//             descriptor.Field(j => j.CreatedAt).Type<NonNullType<DateTimeType>>();
//             descriptor.Field(j => j.CreatedBy).Type<NonNullType<StringType>>();
//             descriptor.Field(j => j.UpdatedAt).Type<DateTimeType>();
//             descriptor.Field(j => j.UpdatedBy).Type<StringType>();

//             // Soft delete fields are typically not exposed in GraphQL
//         }
//     }