using HotChocolate.Types;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace Shared.GraphQL.Types
{
    public class WorkerTrainingHistoryType : ObjectType<WorkerTrainingHistory>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkerTrainingHistory> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.TrainingId).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Worker).Type<NonNullType<WorkerType>>();
            descriptor.Field(t => t.Training).Type<NonNullType<LegacyTrainingType>>();
            
            descriptor.Field(t => t.CompletionDate).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.ExpiryDate).Type<DateTimeType>();
            descriptor.Field(t => t.Status).Type<NonNullType<EnumType<TrainingStatus>>>();
            descriptor.Field(t => t.Notes).Type<StringType>();
            descriptor.Field(t => t.CertificateUrl).Type<StringType>();
            descriptor.Field(t => t.Score).Type<FloatType>();

            // Computed properties
            descriptor.Field(t => t.IsExpired).Type<NonNullType<BooleanType>>();
            descriptor.Field(t => t.IsExpiringSoon).Type<NonNullType<BooleanType>>();
            descriptor.Field(t => t.DaysUntilExpiry).Type<IntType>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
