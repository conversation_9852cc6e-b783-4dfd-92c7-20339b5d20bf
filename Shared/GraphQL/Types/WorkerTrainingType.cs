using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class WorkerTrainingType : ObjectType<WorkerTraining>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkerTraining> descriptor)
        {
            descriptor.Field(wt => wt.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(wt => wt.TrainingId).Type<NonNullType<IntType>>();
            descriptor.Field(wt => wt.Worker).Type<WorkerType>();
            descriptor.Field(wt => wt.Training).Type<LegacyTrainingType>();
            descriptor.Field(wt => wt.Notes).Type<StringType>();
            descriptor.Field(wt => wt.AssignedDate).Type<NonNullType<DateTimeType>>();

            // Document files - return file IDs instead of full objects
            descriptor.Field("documentFileIds")
                .Type<ListType<IntType>>()
                .Description("List of document file IDs associated with this worker-training relationship")
                .Resolve(context =>
                {
                    var workerTraining = context.Parent<WorkerTraining>();
                    return workerTraining.DocumentFiles?.Where(d => !d.IsDeleted).Select(d => d.Id).ToList() ?? new List<int>();
                });

            // Computed field for document URLs (for backward compatibility)
            descriptor.Field("documentUrls")
                .Type<ListType<StringType>>()
                .Description("List of document URLs for this worker-training relationship")
                .Resolve(context =>
                {
                    var workerTraining = context.Parent<WorkerTraining>();
                    return workerTraining.DocumentFiles?.Where(d => !d.IsDeleted).Select(d => d.Url).ToList() ?? new List<string>();
                });

            descriptor.Field(wt => wt.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(wt => wt.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(wt => wt.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(wt => wt.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
