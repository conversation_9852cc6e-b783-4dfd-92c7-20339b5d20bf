﻿using HotChocolate.Types;
using HotChocolate;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class WorkerType : ObjectType<Worker>
    {
        protected override void Configure(IObjectTypeDescriptor<Worker> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Name).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Company).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.PhoneNumber).Type<NonNullType<StringType>>();

            descriptor.Field(t => t.NationalId).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Gender).Type<NonNullType<StringType>>();

            // Date fields
            descriptor.Field(t => t.DateOfBirth).Type<DateType>();
            descriptor.Field(t => t.InductionDate).Type<DateTimeType>();
            descriptor.Field(t => t.MedicalCheckDate).Type<DateTimeType>();

            // Optional fields
            descriptor.Field(t => t.MpesaNumber).Type<StringType>();
            descriptor.Field(t => t.Email).Type<StringType>();

            // File metadata fields
            descriptor.Field(t => t.ProfilePictureFileId).Type<IntType>();
            descriptor.Field(t => t.ProfilePictureFile).Type<FileMetadataType>();
            descriptor.Field(t => t.SignatureFileId).Type<IntType>();
            descriptor.Field(t => t.SignatureFile).Type<FileMetadataType>();
            descriptor.Field(t => t.DocumentFiles).Type<ListType<DocumentFileType>>();

            // Computed URL fields for easy access
            descriptor.Field("profilePictureUrl")
                .Type<StringType>()
                .Description("URL to the worker's profile picture")
                .Resolve(context =>
                {
                    var worker = context.Parent<Worker>();
                    return worker.ProfilePictureFile?.Url;
                });

            descriptor.Field("signatureUrl")
                .Type<StringType>()
                .Description("URL to the worker's signature")
                .Resolve(context =>
                {
                    var worker = context.Parent<Worker>();
                    return worker.SignatureFile?.Url;
                });

            descriptor.Field("documentUrls")
                .Type<ListType<DocumentFileType>>()
                .Description("List of document files with names and URLs")
                .Resolve(context =>
                {
                    var worker = context.Parent<Worker>();
                    return worker.DocumentFiles?.Where(d => !d.IsDeleted).ToList() ?? new List<DocumentFile>();
                });

            // Numeric fields
            descriptor.Field(t => t.Age).Type<IntType>(); // Computed property, can be null
            descriptor.Field(t => t.TrainingsCompleted).Type<NonNullType<IntType>>(); // Computed property
            descriptor.Field(t => t.ManHours).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Rating).Type<NonNullType<FloatType>>();

            // Navigation properties
            descriptor.Field(t => t.Trainings).Type<ListType<LegacyTrainingType>>();
            descriptor.Field(t => t.WorkerTrainings).Type<ListType<WorkerTrainingType>>();
            descriptor.Field(t => t.Trades).Type<ListType<TradeType>>();
            descriptor.Field(t => t.Skills).Type<ListType<SkillType>>();
            descriptor.Field(t => t.TrainingHistory).Type<ListType<WorkerTrainingHistoryType>>();
            descriptor.Field(t => t.Incidents).Type<ListType<IncidentType>>();

            // Audit fields
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
