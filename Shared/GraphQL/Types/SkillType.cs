using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class SkillType : ObjectType<Skill>
    {
        protected override void Configure(IObjectTypeDescriptor<Skill> descriptor)
        {
            descriptor.Field(s => s.Id).Type<NonNullType<IntType>>();
            descriptor.Field(s => s.Name).Type<NonNullType<StringType>>();
            descriptor.Field(s => s.Description).Type<StringType>();
            descriptor.Field(s => s.Workers).Type<ListType<WorkerType>>();
            descriptor.Field(s => s.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(s => s.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(s => s.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(s => s.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
