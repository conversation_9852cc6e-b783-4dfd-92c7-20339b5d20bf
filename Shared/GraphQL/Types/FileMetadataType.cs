using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    /// <summary>
    /// GraphQL type for FileMetadata entity
    /// </summary>
    public class FileMetadataType : ObjectType<FileMetadata>
    {
        protected override void Configure(IObjectTypeDescriptor<FileMetadata> descriptor)
        {
            descriptor.Description("Represents file metadata stored in the system");

            descriptor.Field(f => f.Id)
                .Description("Unique identifier for the file metadata");

            descriptor.Field(f => f.FileName)
                .Description("Original name of the uploaded file");

            descriptor.Field(f => f.ContentType)
                .Description("MIME type of the file");

            descriptor.Field(f => f.Size)
                .Description("Size of the file in bytes");

            descriptor.Field(f => f.Description)
                .Description("Optional description of the file");

            descriptor.Field(f => f.BucketName)
                .Description("MinIO bucket where the file is stored");

            descriptor.Field(f => f.<PERSON>)
                .Description("Unique object key in the MinIO bucket");

            descriptor.Field(f => f.ETag)
                .Description("Entity tag for the file");

            descriptor.Field(f => f.Version)
                .Description("Version identifier for the file");

            descriptor.Field(f => f.FileType)
                .Description("Type of the file (JPEG, PNG, PDF, etc.)");

            descriptor.Field(f => f.FolderPath)
                .Description("Optional folder path within the bucket");

            descriptor.Field(f => f.IsPublic)
                .Description("Whether the file is publicly accessible");

            descriptor.Field(f => f.ExpiresAt)
                .Description("Optional expiration date for the file");

            descriptor.Field(f => f.AdditionalMetadata)
                .Description("Additional metadata stored as JSON");

            // Computed URL field
            descriptor.Field(f => f.Url)
                .Type<NonNullType<StringType>>()
                .Description("URL to access the file");

            // Audit fields
            descriptor.Field(f => f.CreatedAt)
                .Description("When the file metadata was created");

            descriptor.Field(f => f.CreatedBy)
                .Description("Who created the file metadata");

            descriptor.Field(f => f.UpdatedAt)
                .Description("When the file metadata was last updated");

            descriptor.Field(f => f.UpdatedBy)
                .Description("Who last updated the file metadata");

            // Helper methods
            descriptor.Field(f => f.GetFullObjectPath())
                .Description("Gets the full object path including folder");

            descriptor.Field(f => f.IsExpired())
                .Description("Checks if the file has expired");

            // Hide soft delete fields from GraphQL
            descriptor.Field(f => f.IsDeleted).Ignore();
            descriptor.Field(f => f.DeletedAt).Ignore();
            descriptor.Field(f => f.DeletedBy).Ignore();
        }
    }
}
