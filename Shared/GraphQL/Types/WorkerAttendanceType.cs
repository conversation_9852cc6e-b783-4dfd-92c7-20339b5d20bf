using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    public class WorkerAttendanceType : ObjectType<WorkerAttendance>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkerAttendance> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Worker).Type<WorkerType>();
            descriptor.Field(t => t.CheckInTime).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CheckOutTime).Type<DateTimeType>();
            descriptor.Field(t => t.Status).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Notes).Type<StringType>();
            descriptor.Field(t => t.IsVerifiedByHikvision).Type<NonNullType<BooleanType>>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<StringType>();
        }
    }
} 