using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;
using Shared.Enums;

namespace Shared.GraphQL.Types
{
    public class RoleType : ObjectType<Role>
    {
        protected override void Configure(IObjectTypeDescriptor<Role> descriptor)
        {
            descriptor.Field(r => r.Id).Type<NonNullType<IdType>>();
            descriptor.Field(r => r.Name).Type<NonNullType<StringType>>();
            descriptor.Field(r => r.Description).Type<StringType>();
            descriptor.Field(r => r.IsSystemRole).Type<NonNullType<BooleanType>>();
            descriptor.Field(r => r.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(r => r.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(r => r.CreatedBy).Type<StringType>();
            descriptor.Field(r => r.UpdatedBy).Type<StringType>();

            // Navigation properties
            descriptor.Field(r => r.Tenant).Type<TenantType>();
            descriptor.Field(r => r.ApplicationUsers).Type<ListType<ApplicationUserType>>();

            // Permission fields - expose as computed properties
            descriptor.Field("workersPermissions")
                .Type<WorkersPermissionsGraphQLType>()
                .Resolve(context =>
                {
                    var role = context.Parent<Role>();
                    return new WorkersPermissionsGraphQL
                    {
                        SiteCreate = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.SiteCreate),
                        SiteRead = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.SiteRead),
                        SiteUpdate = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.SiteUpdate),
                        SiteDelete = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.SiteDelete),
                        CompanyCreate = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.CompanyCreate),
                        CompanyRead = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.CompanyRead),
                        CompanyUpdate = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.CompanyUpdate),
                        CompanyDelete = role.HasWorkersPermission(Shared.Enums.WorkersPermissions.CompanyDelete)
                    };
                });

            descriptor.Field("sitesPermissions")
                .Type<SitesPermissionsGraphQLType>()
                .Resolve(context =>
                {
                    var role = context.Parent<Role>();
                    return new SitesPermissionsGraphQL
                    {
                        SiteCreate = role.HasSitesPermission(Shared.Enums.SitesPermissions.SiteCreate),
                        SiteRead = role.HasSitesPermission(Shared.Enums.SitesPermissions.SiteRead),
                        SiteUpdate = role.HasSitesPermission(Shared.Enums.SitesPermissions.SiteUpdate),
                        SiteDelete = role.HasSitesPermission(Shared.Enums.SitesPermissions.SiteDelete),
                        CompanyCreate = role.HasSitesPermission(Shared.Enums.SitesPermissions.CompanyCreate),
                        CompanyRead = role.HasSitesPermission(Shared.Enums.SitesPermissions.CompanyRead),
                        CompanyUpdate = role.HasSitesPermission(Shared.Enums.SitesPermissions.CompanyUpdate),
                        CompanyDelete = role.HasSitesPermission(Shared.Enums.SitesPermissions.CompanyDelete)
                    };
                });

            // Hide raw permission bytes
            descriptor.Field(r => r.WorkerPermissions).Ignore();
            descriptor.Field(r => r.SitePermissions).Ignore();
            descriptor.Field(r => r.TrainingPermissions).Ignore();
            descriptor.Field(r => r.DocumentPermissions).Ignore();
            descriptor.Field(r => r.PPEPermissions).Ignore();
            descriptor.Field(r => r.RoleManagementPermissions).Ignore();
            descriptor.Field(r => r.ReportPermissions).Ignore();
            descriptor.Field(r => r.IsDeleted).Ignore();
            descriptor.Field(r => r.DeletedAt).Ignore();
            descriptor.Field(r => r.DeletedBy).Ignore();
        }
    }

    public class WorkersPermissionsGraphQLType : ObjectType<WorkersPermissionsGraphQL>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkersPermissionsGraphQL> descriptor)
        {
            descriptor.Field(p => p.SiteCreate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteRead).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteUpdate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteDelete).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyCreate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyRead).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyUpdate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyDelete).Type<NonNullType<BooleanType>>();
        }
    }

    public class SitesPermissionsGraphQLType : ObjectType<SitesPermissionsGraphQL>
    {
        protected override void Configure(IObjectTypeDescriptor<SitesPermissionsGraphQL> descriptor)
        {
            descriptor.Field(p => p.SiteCreate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteRead).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteUpdate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.SiteDelete).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyCreate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyRead).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyUpdate).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.CompanyDelete).Type<NonNullType<BooleanType>>();
        }
    }

    // Helper classes for GraphQL exposure
    public class WorkersPermissionsGraphQL
    {
        public bool SiteCreate { get; set; }
        public bool SiteRead { get; set; }
        public bool SiteUpdate { get; set; }
        public bool SiteDelete { get; set; }
        public bool CompanyCreate { get; set; }
        public bool CompanyRead { get; set; }
        public bool CompanyUpdate { get; set; }
        public bool CompanyDelete { get; set; }
    }

    public class SitesPermissionsGraphQL
    {
        public bool SiteCreate { get; set; }
        public bool SiteRead { get; set; }
        public bool SiteUpdate { get; set; }
        public bool SiteDelete { get; set; }
        public bool CompanyCreate { get; set; }
        public bool CompanyRead { get; set; }
        public bool CompanyUpdate { get; set; }
        public bool CompanyDelete { get; set; }
    }
}
