using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;

namespace Shared.GraphQL.Types
{
    public class TenantType : ObjectType<Tenant>
    {
        protected override void Configure(IObjectTypeDescriptor<Tenant> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IdType>>();
            descriptor.Field(t => t.Name).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();

            // Navigation properties
            descriptor.Field(t => t.ApplicationUsers).Type<ListType<ApplicationUserType>>();
            descriptor.Field(t => t.Roles).Type<ListType<RoleType>>();

            // Hide sensitive fields
            descriptor.Field(t => t.IsDeleted).Ignore();
            descriptor.Field(t => t.DeletedAt).Ignore();
            descriptor.Field(t => t.DeletedBy).Ignore();
        }
    }
}
