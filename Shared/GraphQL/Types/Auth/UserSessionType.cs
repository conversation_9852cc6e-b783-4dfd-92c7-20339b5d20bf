using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;

namespace Shared.GraphQL.Types
{
    public class UserSessionType : ObjectType<UserSession>
    {
        protected override void Configure(IObjectTypeDescriptor<UserSession> descriptor)
        {
            descriptor.Field(s => s.Id).Type<NonNullType<IdType>>();
            descriptor.Field(s => s.SessionId).Type<NonNullType<StringType>>();
            descriptor.Field(s => s.IpAddress).Type<StringType>();
            descriptor.Field(s => s.UserAgent).Type<StringType>();
            descriptor.Field(s => s.DeviceType).Type<StringType>();
            descriptor.Field(s => s.Location).Type<StringType>();
            descriptor.Field(s => s.IsActive).Type<NonNullType<BooleanType>>();
            descriptor.Field(s => s.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(s => s.LastActivityAt).Type<DateTimeType>();
            descriptor.Field(s => s.ExpiresAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(s => s.EndedAt).Type<DateTimeType>();
            descriptor.Field(s => s.EndReason).Type<StringType>();

            // Navigation properties
            descriptor.Field(s => s.ApplicationUser).Type<ApplicationUserType>();

            // Hide sensitive fields
            descriptor.Field(s => s.ApplicationUserId).Ignore();
        }
    }
}
