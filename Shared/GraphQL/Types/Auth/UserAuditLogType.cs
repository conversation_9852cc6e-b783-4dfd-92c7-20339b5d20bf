using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;

namespace Shared.GraphQL.Types
{
    public class UserAuditLogType : ObjectType<UserAuditLog>
    {
        protected override void Configure(IObjectTypeDescriptor<UserAuditLog> descriptor)
        {
            descriptor.Field(a => a.Id).Type<NonNullType<IdType>>();
            descriptor.Field(a => a.Action).Type<NonNullType<EnumType<AuditAction>>>();
            descriptor.Field(a => a.Description).Type<StringType>();
            descriptor.Field(a => a.IpAddress).Type<StringType>();
            descriptor.Field(a => a.UserAgent).Type<StringType>();
            descriptor.Field(a => a.AdditionalData).Type<StringType>();
            descriptor.Field(a => a.CreatedAt).Type<NonNullType<DateTimeType>>();

            // Navigation properties
            descriptor.Field(a => a.ApplicationUser).Type<ApplicationUserType>();

            // Hide sensitive fields
            descriptor.Field(a => a.ApplicationUserId).Ignore();
        }
    }
}
