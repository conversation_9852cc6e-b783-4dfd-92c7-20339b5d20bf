using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;

namespace Shared.GraphQL.Types
{
    public class RefreshTokenType : ObjectType<RefreshToken>
    {
        protected override void Configure(IObjectTypeDescriptor<RefreshToken> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IdType>>();
            descriptor.Field(t => t.IsActive).Type<NonNullType<BooleanType>>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.ExpiresAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.RevokedAt).Type<DateTimeType>();
            descriptor.Field(t => t.RevokedReason).Type<StringType>();
            descriptor.Field(t => t.IpAddress).Type<StringType>();
            descriptor.Field(t => t.UserAgent).Type<StringType>();

            // Navigation properties
            descriptor.Field(t => t.ApplicationUser).Type<ApplicationUserType>();

            // Hide sensitive fields
            descriptor.Field(t => t.Token).Ignore();
            descriptor.Field(t => t.ApplicationUserId).Ignore();
        }
    }
}
