using HotChocolate.Types;
using Shared.GraphQL.Models.Auth;

namespace Shared.GraphQL.Types
{
    public class ApplicationUserType : ObjectType<ApplicationUser>
    {
        protected override void Configure(IObjectTypeDescriptor<ApplicationUser> descriptor)
        {
            descriptor.Field(u => u.Id).Type<NonNullType<IdType>>();
            descriptor.Field(u => u.FirstName).Type<NonNullType<StringType>>();
            descriptor.Field(u => u.LastName).Type<NonNullType<StringType>>();
            descriptor.Field(u => u.Email).Type<NonNullType<StringType>>();
            descriptor.Field(u => u.PhoneNumber).Type<StringType>();
            descriptor.Field(u => u.UserName).Type<StringType>();
            descriptor.Field(u => u.Status).Type<NonNullType<EnumType<UserStatus>>>();
            descriptor.Field(u => u.EmailConfirmed).Type<NonNullType<BooleanType>>();
            descriptor.Field(u => u.PhoneNumberConfirmed).Type<NonNullType<BooleanType>>();
            descriptor.Field(u => u.TwoFactorEnabled).Type<NonNullType<BooleanType>>();
            descriptor.Field(u => u.LastLoginAt).Type<DateTimeType>();
            descriptor.Field(u => u.LastLoginIp).Type<StringType>();
            descriptor.Field(u => u.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(u => u.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(u => u.CreatedBy).Type<StringType>();
            descriptor.Field(u => u.UpdatedBy).Type<StringType>();
            descriptor.Field(u => u.FullName).Type<NonNullType<StringType>>();
            descriptor.Field(u => u.IsActive).Type<NonNullType<BooleanType>>();

            // Navigation properties
            descriptor.Field(u => u.Role).Type<RoleType>();
            descriptor.Field(u => u.Tenant).Type<TenantType>();

            // Hide sensitive fields from Identity
            descriptor.Field(u => u.PasswordHash).Ignore();
            descriptor.Field(u => u.SecurityStamp).Ignore();
            descriptor.Field(u => u.ConcurrencyStamp).Ignore();
            descriptor.Field(u => u.Salt).Ignore();
            descriptor.Field(u => u.FailedLoginAttempts).Ignore();
            descriptor.Field(u => u.AccessFailedCount).Ignore();
            descriptor.Field(u => u.LockoutEnd).Ignore();
            descriptor.Field(u => u.LockoutEnabled).Ignore();
            descriptor.Field(u => u.LockedOutUntil).Ignore();
            descriptor.Field(u => u.IsDeleted).Ignore();
            descriptor.Field(u => u.DeletedAt).Ignore();
            descriptor.Field(u => u.DeletedBy).Ignore();
            descriptor.Field(u => u.NormalizedEmail).Ignore();
            descriptor.Field(u => u.NormalizedUserName).Ignore();
        }
    }
}

