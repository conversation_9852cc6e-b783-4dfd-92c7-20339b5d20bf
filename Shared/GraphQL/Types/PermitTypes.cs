using HotChocolate.Types;
using Shared.GraphQL.Models.Permits;

namespace Shared.GraphQL.Types
{
    public class PermitType : InterfaceType<Permit>
    {
        protected override void Configure(IInterfaceTypeDescriptor<Permit> descriptor)
        {
            descriptor.Field(p => p.Id).Type<NonNullType<IntType>>();
            descriptor.Field(p => p.JobId).Type<NonNullType<IntType>>();
            descriptor.Field(p => p.Job).Type<JobType>();
            descriptor.Field(p => p.PTWRefNumber).Type<NonNullType<StringType>>();
            descriptor.Field(p => p.ProjectName).Type<NonNullType<StringType>>();
            descriptor.Field(p => p.StartingDateTime).Type<NonNullType<DateTimeType>>();
            descriptor.Field(p => p.EndingDateTime).Type<NonNullType<DateTimeType>>();
            descriptor.Field(p => p.Status).Type<NonNullType<EnumType<Shared.Enums.PermitStatus>>>();
            descriptor.Field(p => p.PermitType).Type<NonNullType<EnumType<Shared.Enums.PermitType>>>();
            descriptor.Field(p => p.Isolation).Type<ListType<StringType>>();
            descriptor.Field(p => p.Inspections).Type<ListType<StringType>>();
            // JSON objects
            descriptor.Field(p => p.PermitIssuer).Type<PermitIssuerType>();
            descriptor.Field(p => p.PermitReturn).Type<PermitReturnType>();
            descriptor.Field(p => p.SignOff).Type<SignOffType>();

            // Relationships
            descriptor.Field(p => p.OtherPermitsInUse).Type<ListType<PermitType>>();
            // descriptor.Field(p => p.Documents).Type<ListType<DocumentFileType>>();

            // Audit fields
            descriptor.Field(p => p.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(p => p.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(p => p.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(p => p.UpdatedBy).Type<StringType>();
            descriptor.Field(p => p.AttendancePictureFile).Type<FileMetadataType>();
            descriptor.Field(p => p.AttendancePictureFileId).Type<IntType>();
        }
    }

    public class GeneralWorkPermitType : ObjectType<GeneralWorkPermit>
    {
        protected override void Configure(IObjectTypeDescriptor<GeneralWorkPermit> descriptor)
        {
            // descriptor.Field(p => p.Isolation).Type<StringType>();
            // descriptor.Field(p => p.WorkAreaInspectionAndPermitRenewal).Type<ListType<WorkAreaInspectionType>>();
        }
    }

    public class HotWorkPermitType : ObjectType<HotWorkPermit>
    {
        protected override void Configure(IObjectTypeDescriptor<HotWorkPermit> descriptor)
        {
            // descriptor.Field(p => p.NatureOfWork).Type<StringType>();
            // descriptor.Field(p => p.FireExtinguishers).Type<StringType>();
            descriptor.Field(p => p.FireSafetySupervisor).Type<FireSafetySupervisorType>();
        }
    }

    public class ExcavationWorkPermitType : ObjectType<ExcavationWorkPermit>
    {
        protected override void Configure(IObjectTypeDescriptor<ExcavationWorkPermit> descriptor)
        {
            // descriptor.Field(p => p.DepthOfExcavation).Type<StringType>();
            // descriptor.Field(p => p.ProtectionSystems).Type<StringType>();
            // descriptor.Field(p => p.ListOfEquipmentToBeUsed).Type<StringType>();
            // descriptor.Field(p => p.Inspections).Type<StringType>();
            descriptor.Field(p => p.InspectionAuthorization).Type<InspectionAuthorizationType>();
        }
    }

    public class WorkAtHeightPermitType : ObjectType<WorkAtHeightPermit>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkAtHeightPermit> descriptor)
        {
            // descriptor.Field(p => p.ModeOfAccessToBeUsed).Type<StringType>();
            // descriptor.Field(p => p.Inspections).Type<StringType>();
            descriptor.Field(p => p.InspectionAuthorization).Type<InspectionAuthorizationType>();
        }
    }

    public class ConfinedSpacePermitType : ObjectType<ConfinedSpacePermit>
    {
        protected override void Configure(IObjectTypeDescriptor<ConfinedSpacePermit> descriptor)
        {
            descriptor.Field(p => p.WorkersHaveBeenTrained).Type<NonNullType<BooleanType>>();
            descriptor.Field(p => p.NameOfTrainingOrganization).Type<StringType>();
            descriptor.Field(p => p.TopReading).Type<AtmosphericReadingType>();
            descriptor.Field(p => p.MidReading).Type<AtmosphericReadingType>();
            descriptor.Field(p => p.BottomReading).Type<AtmosphericReadingType>();
            descriptor.Field(p => p.EmergencyGuidelines).Type<StringType>();
            descriptor.Field(p => p.TaskObserver).Type<TaskObserverType>();
        }
    }

    // JSON object types
    public class PermitIssuerType : ObjectType<PermitIssuer>
    {
        protected override void Configure(IObjectTypeDescriptor<PermitIssuer> descriptor)
        {
            descriptor.Field(pi => pi.CompetentPersons).Type<ListType<CompetentPersonType>>();
            descriptor.Field(pi => pi.AuthorisedPersons).Type<ListType<AuthorisedPersonType>>();
        }
    }

    public class PermitReturnType : ObjectType<PermitReturn>
    {
        protected override void Configure(IObjectTypeDescriptor<PermitReturn> descriptor)
        {
            descriptor.Field(pr => pr.CompetentPersons).Type<ListType<CompetentPersonType>>();
            descriptor.Field(pr => pr.AuthorisedPersons).Type<ListType<AuthorisedPersonType>>();
        }
    }

    public class SignOffType : ObjectType<SignOff>
    {
        protected override void Configure(IObjectTypeDescriptor<SignOff> descriptor)
        {
            descriptor.Field(so => so.DateTime).Type<NonNullType<DateTimeType>>();
            descriptor.Field(so => so.Workers).Type<ListType<PermitWorkerType>>();
        }
    }

    public class CompetentPersonType : ObjectType<CompetentPerson>
    {
        protected override void Configure(IObjectTypeDescriptor<CompetentPerson> descriptor)
        {
            descriptor.Field(cp => cp.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(cp => cp.Name).Type<NonNullType<StringType>>();
            descriptor.Field(cp => cp.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(cp => cp.SignedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class AuthorisedPersonType : ObjectType<AuthorisedPerson>
    {
        protected override void Configure(IObjectTypeDescriptor<AuthorisedPerson> descriptor)
        {
            descriptor.Field(ap => ap.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(ap => ap.Name).Type<NonNullType<StringType>>();
            descriptor.Field(ap => ap.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(ap => ap.SignedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class PermitWorkerType : ObjectType<PermitWorker>
    {
        protected override void Configure(IObjectTypeDescriptor<PermitWorker> descriptor)
        {
            descriptor.Field(pw => pw.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(pw => pw.Designation).Type<NonNullType<StringType>>();
            descriptor.Field(pw => pw.Name).Type<NonNullType<StringType>>();
            descriptor.Field(pw => pw.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(pw => pw.SignedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class FireSafetySupervisorType : ObjectType<FireSafetySupervisor>
    {
        protected override void Configure(IObjectTypeDescriptor<FireSafetySupervisor> descriptor)
        {
            descriptor.Field(fss => fss.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(fss => fss.Name).Type<NonNullType<StringType>>();
            descriptor.Field(fss => fss.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(fss => fss.SignedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class InspectionAuthorizationType : ObjectType<InspectionAuthorization>
    {
        protected override void Configure(IObjectTypeDescriptor<InspectionAuthorization> descriptor)
        {
            descriptor.Field(ia => ia.NameOfInspector).Type<NonNullType<StringType>>();
            descriptor.Field(ia => ia.Designation).Type<NonNullType<StringType>>();
            descriptor.Field(ia => ia.DateOfInspection).Type<NonNullType<DateTimeType>>();
            descriptor.Field(ia => ia.Comments).Type<StringType>();
        }
    }

    public class WorkAreaInspectionType : ObjectType<WorkAreaInspection>
    {
        protected override void Configure(IObjectTypeDescriptor<WorkAreaInspection> descriptor)
        {
            descriptor.Field(wai => wai.Name).Type<NonNullType<StringType>>();
            descriptor.Field(wai => wai.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(wai => wai.SignedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(wai => wai.Comments).Type<StringType>();
        }
    }

    public class AtmosphericReadingType : ObjectType<AtmosphericReading>
    {
        protected override void Configure(IObjectTypeDescriptor<AtmosphericReading> descriptor)
        {
            descriptor.Field(ar => ar.Oxygen).Type<StringType>();
            descriptor.Field(ar => ar.Explosive).Type<StringType>();
            descriptor.Field(ar => ar.Toxic).Type<StringType>();
            descriptor.Field(ar => ar.Co2).Type<StringType>();
        }
    }

    public class TaskObserverType : ObjectType<TaskObserver>
    {
        protected override void Configure(IObjectTypeDescriptor<TaskObserver> descriptor)
        {
            descriptor.Field(to => to.WorkerId).Type<NonNullType<IntType>>();
            descriptor.Field(to => to.Name).Type<NonNullType<StringType>>();
            descriptor.Field(to => to.SignatureFileId).Type<NonNullType<StringType>>();
            descriptor.Field(to => to.SignedAt).Type<NonNullType<DateTimeType>>();
        }
    }
}
