using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    /// <summary>
    /// GraphQL type for InspectionItem entity
    /// </summary>
    public class InspectionItemType : ObjectType<InspectionItem>
    {
        protected override void Configure(IObjectTypeDescriptor<InspectionItem> descriptor)
        {
            descriptor.Description("Represents an individual inspection item");

            descriptor.Field(i => i.Id)
                .Description("Unique identifier for the inspection item");

            descriptor.Field(i => i.Description)
                .Description("Description of what is being inspected");

            descriptor.Field(i => i.IsTrue)
                .Description("Whether the inspection item passed (true) or failed (false)");

            descriptor.Field(i => i.Remarks)
                .Description("Additional remarks or comments for this inspection item");

            descriptor.Field(i => i.InspectionId)
                .Description("Foreign key to the parent inspection");

            descriptor.Field(i => i.Inspection)
                .Description("Navigation property to the parent inspection");

            descriptor.Field(i => i.ImageFiles)
                .Description("Collection of image files associated with this inspection item");

            descriptor.Field(i => i.CreatedAt)
                .Description("Date and time when the inspection item was created");

            descriptor.Field(i => i.CreatedBy)
                .Description("User who created the inspection item");

            descriptor.Field(i => i.UpdatedAt)
                .Description("Date and time when the inspection item was last updated");

            descriptor.Field(i => i.UpdatedBy)
                .Description("User who last updated the inspection item");

            // Hide soft delete fields from GraphQL
            descriptor.Field(i => i.IsDeleted).Ignore();
            descriptor.Field(i => i.DeletedAt).Ignore();
            descriptor.Field(i => i.DeletedBy).Ignore();
        }
    }
}
