using HotChocolate.Types;
using Shared.GraphQL.Models;

namespace Shared.GraphQL.Types
{
    /// <summary>
    /// GraphQL type for Inspection entity
    /// </summary>
    public class InspectionType : ObjectType<Inspection>
    {
        protected override void Configure(IObjectTypeDescriptor<Inspection> descriptor)
        {
            descriptor.Description("Represents an inspection with multiple inspection items");

            descriptor.Field(i => i.Id)
                .Description("Unique identifier for the inspection");

            descriptor.Field(i => i.InspectionItems)
                .Description("Collection of inspection items that make up this inspection");

            descriptor.Field(i => i.Approved)
                .Description("Whether the overall inspection is approved");

            descriptor.Field(i => i.Comments)
                .Description("General comments about the inspection");

            descriptor.Field(i => i.SignatureFileId)
                .Description("Foreign key to the inspector's signature file");

            descriptor.Field(i => i.SignatureFile)
                .Description("Navigation property to the inspector's signature file");

            descriptor.Field(i => i.InspectedById)
                .Description("Foreign key to the worker who performed the inspection");

            descriptor.Field(i => i.InspectedBy)
                .Description("Navigation property to the worker who performed the inspection");

            descriptor.Field(i => i.CreatedAt)
                .Description("Date and time when the inspection was created");

            descriptor.Field(i => i.CreatedBy)
                .Description("User who created the inspection");

            descriptor.Field(i => i.UpdatedAt)
                .Description("Date and time when the inspection was last updated");

            descriptor.Field(i => i.UpdatedBy)
                .Description("User who last updated the inspection");

            // Hide soft delete fields from GraphQL
            descriptor.Field(i => i.IsDeleted).Ignore();
            descriptor.Field(i => i.DeletedAt).Ignore();
            descriptor.Field(i => i.DeletedBy).Ignore();
        }
    }
}
