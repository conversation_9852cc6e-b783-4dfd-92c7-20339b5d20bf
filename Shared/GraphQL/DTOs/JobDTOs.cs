
// using Shared.Enums;
// using Shared.GraphQL.Models;

// namespace Shared.GraphQL.DTOs;

// public record JobDTO
// {
//     // Basic information
//     public int Id { get; set; }
//     public string Title { get; set; } = string.Empty;
//     public string? Description { get; set; } = string.Empty;
//     public string Location { get; set; } = string.Empty;
//     public string PPE { get; set; } = string.Empty; // todo: make it a list of PPES
//     public JobStatus Status { get; set; } = JobStatus.REQUESTED;
//     public ICollection<PermitType> RequiredPermits { get; set; } = [];
//     public TimeSpan TimeForCompletion { get; set; } = TimeSpan.FromDays(1); // Default to 1 day
//     public DateTime StartDate { get; set; } = DateTime.Today.AddDays(1); // Default to tomorrow
//     public DateTime? DueDate { get; set; }

//     // Excavation specific fields
//     public string? DepthOfExcavation { get; set; }
//     public string? ExcavationProtectionSystems { get; set; }
//     public string? ExcavationEquipmentsToBeUsed { get; set; }

//     // Hot work specific fields
//     public string? FireExtinguishers { get; set; }
//     public string? NatureOfHotWork { get; set; }

//     // Category relationship
//     public int? CategoryId { get; set; }
//     public Category? Category { get; set; }

//     // Request information
//     public int? RequestedById { get; set; }
//     public Worker? RequestedBy { get; set; }
//     public DateTime? RequestedDate { get; set; }

//     // Block information
//     public int? BlockedById { get; set; }
//     public Worker? BlockedBy { get; set; }
//     public DateTime? BlockedDate { get; set; }

//     // Review information
//     public int? ReviewedById { get; set; }
//     public Worker? ReviewedBy { get; set; }
//     public DateTime? ReviewedDate { get; set; }

//     // Approval information (used for both approved and disapproved)
//     public int? ApprovedById { get; set; }
//     public Worker? ApprovedBy { get; set; }
//     public DateTime? ApprovedDate { get; set; }

//     // Finish information
//     public int? FinishedById { get; set; }
//     public Worker? FinishedBy { get; set; }
//     public DateTime? FinishedDate { get; set; }

//     // Chief Engineer and Workers
//     public int? ChiefEngineerId { get; set; }
//     public Worker? ChiefEngineer { get; set; }
//     public ICollection<Worker> Workers { get; set; } = [];

//     // Hazards, Documents, and Permits
//     public ICollection<Hazard> Hazards { get; set; } = [];
//     public ICollection<DocumentFile> Documents { get; set; } = [];
//     public ICollection<Models.Permits.Permit> Permits { get; set; } = [];

//     // Audit Fields
//     public DateTime CreatedAt { get; set; }
//     public string CreatedBy { get; set; } = string.Empty;
//     public DateTime? UpdatedAt { get; set; }
//     public string UpdatedBy { get; set; } = string.Empty;

//     // Soft Delete Fields
//     public bool IsDeleted { get; set; }
//     public DateTime? DeletedAt { get; set; }
//     public string? DeletedBy { get; set; }

//     // Computed property for due date
//     public DateTime CalculatedDueDate
//     {
//         get
//         {
//             if (DueDate.HasValue)
//                 return DueDate.Value;
//             return StartDate.Add(TimeForCompletion);
//         }
//     }
//     public static Job ToJob(JobDTO jobDTO)
//     {
//         return new Job
//         {
//             Id = jobDTO.Id,
//             Title = jobDTO.Title,
//             Description = jobDTO.Description,
//             Location = jobDTO.Location,
//             PPE = jobDTO.PPE,
//             Status = jobDTO.Status,
//             RequiredPermits = (HashSet<PermitType>)jobDTO.RequiredPermits,
//             TimeForCompletion = jobDTO.TimeForCompletion,
//             StartDate = jobDTO.StartDate,
//             DueDate = jobDTO.DueDate,
//             DepthOfExcavation = jobDTO.DepthOfExcavation,
//             ExcavationProtectionSystems = jobDTO.ExcavationProtectionSystems,
//             ExcavationEquipmentsToBeUsed = jobDTO.ExcavationEquipmentsToBeUsed,
//             FireExtinguishers = jobDTO.FireExtinguishers,
//             NatureOfHotWork = jobDTO.NatureOfHotWork,
//             CategoryId = jobDTO.CategoryId,
//             Category = jobDTO.Category,
//             RequestedById = jobDTO.RequestedById,
//             RequestedBy = jobDTO.RequestedBy,
//             RequestedDate = jobDTO.RequestedDate,
//             BlockedById = jobDTO.BlockedById,
//             BlockedBy = jobDTO.BlockedBy,
//             BlockedDate = jobDTO.BlockedDate,
//             ReviewedById = jobDTO.ReviewedById,
//             ReviewedBy = jobDTO.ReviewedBy,
//             ReviewedDate = jobDTO.ReviewedDate,
//             ApprovedById = jobDTO.ApprovedById,
//             ApprovedBy = jobDTO.ApprovedBy,
//             ApprovedDate = jobDTO.ApprovedDate,
//             FinishedById = jobDTO.FinishedById,
//             FinishedBy = jobDTO.FinishedBy,
//             FinishedDate = jobDTO.FinishedDate,
//             ChiefEngineerId = jobDTO.ChiefEngineerId,
//             ChiefEngineer = jobDTO.ChiefEngineer,
//             Workers = jobDTO.Workers,
//             Hazards = jobDTO.Hazards,
//             Documents = jobDTO.Documents,
//             Permits = jobDTO.Permits,
//             CreatedAt = jobDTO.CreatedAt,
//             CreatedBy = jobDTO.CreatedBy,
//             UpdatedAt = jobDTO.UpdatedAt,
//             UpdatedBy = jobDTO.UpdatedBy,
//             IsDeleted = jobDTO.IsDeleted,
//             DeletedAt = jobDTO.DeletedAt,
//             DeletedBy = jobDTO.DeletedBy
//         };
//     }
//     public static JobDTO FromJob(Job job)
//     {
//         return new JobDTO
//         {
//             Id = job.Id,
//             Title = job.Title,
//             Description = job.Description,
//             Location = job.Location,
//             PPE = job.PPE,
//             Status = job.Status,
//             RequiredPermits = job.RequiredPermits,
//             TimeForCompletion = job.TimeForCompletion,
//             StartDate = job.StartDate,
//             DueDate = job.DueDate,
//             DepthOfExcavation = job.DepthOfExcavation,
//             ExcavationProtectionSystems = job.ExcavationProtectionSystems,
//             ExcavationEquipmentsToBeUsed = job.ExcavationEquipmentsToBeUsed,
//             FireExtinguishers = job.FireExtinguishers,
//             NatureOfHotWork = job.NatureOfHotWork,
//             CategoryId = job.CategoryId,
//             Category = job.Category,
//             RequestedById = job.RequestedById,
//             RequestedBy = job.RequestedBy,
//             RequestedDate = job.RequestedDate,
//             BlockedById = job.BlockedById,
//             BlockedBy = job.BlockedBy,
//             BlockedDate = job.BlockedDate,
//             ReviewedById = job.ReviewedById,
//             ReviewedBy = job.ReviewedBy,
//             ReviewedDate = job.ReviewedDate,
//             ApprovedById = job.ApprovedById,
//             ApprovedBy = job.ApprovedBy,
//             ApprovedDate = job.ApprovedDate,
//             FinishedById = job.FinishedById,
//             FinishedBy = job.FinishedBy,
//             FinishedDate = job.FinishedDate,
//             ChiefEngineerId = job.ChiefEngineerId,  
//             ChiefEngineer = job.ChiefEngineer,
//             Workers = job.Workers,
//             Hazards = job.Hazards,
//             Documents = job.Documents,
//             Permits = job.Permits,
//             CreatedAt = job.CreatedAt,
//             CreatedBy = job.CreatedBy,
//             UpdatedAt = job.UpdatedAt,
//             UpdatedBy = job.UpdatedBy,
//             IsDeleted = job.IsDeleted,
//             DeletedAt = job.DeletedAt,
//             DeletedBy = job.DeletedBy
//         };
//     }
// }
