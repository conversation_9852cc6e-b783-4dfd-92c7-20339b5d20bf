using System;
using System.Text.RegularExpressions;

namespace Shared.Utils
{
    public class TrainingDuration
    {
        private static readonly Regex DurationRegex = new(@"^(?:(?<years>\d+)\s*y(?:ears?)?\s*)?(?:(?<months>\d+)\s*mo(?:nths?)?\s*)?(?:(?<days>\d+)\s*d(?:ays?)?\s*)?(?:(?<hours>\d+)\s*h(?:ours?)?\s*)?(?:(?<minutes>\d+)\s*m(?:in(?:utes?)?)?\s*)?$", RegexOptions.IgnoreCase);

        public int? Years { get; set; }
        public int? Months { get; set; }
        public int? Days { get; set; }
        public int? Hours { get; set; }
        public int? Minutes { get; set; }

        public static TrainingDuration Parse(string duration)
        {
            if (string.IsNullOrWhiteSpace(duration))
                return null;

            var match = DurationRegex.Match(duration.Trim());
            if (!match.Success)
                throw new FormatException("Invalid duration format. Expected format: '2y 6mo 15d 3h 30m' or similar combinations.");

            return new TrainingDuration
            {
                Years = match.Groups["years"].Success ? int.Parse(match.Groups["years"].Value) : null,
                Months = match.Groups["months"].Success ? int.Parse(match.Groups["months"].Value) : null,
                Days = match.Groups["days"].Success ? int.Parse(match.Groups["days"].Value) : null,
                Hours = match.Groups["hours"].Success ? int.Parse(match.Groups["hours"].Value) : null,
                Minutes = match.Groups["minutes"].Success ? int.Parse(match.Groups["minutes"].Value) : null
            };
        }

        public override string ToString()
        {
            var parts = new System.Collections.Generic.List<string>();
            
            if (Years.HasValue && Years.Value > 0)
                parts.Add($"{Years.Value}y");
            if (Months.HasValue && Months.Value > 0)
                parts.Add($"{Months.Value}mo");
            if (Days.HasValue && Days.Value > 0)
                parts.Add($"{Days.Value}d");
            if (Hours.HasValue && Hours.Value > 0)
                parts.Add($"{Hours.Value}h");
            if (Minutes.HasValue && Minutes.Value > 0)
                parts.Add($"{Minutes.Value}m");

            return string.Join(" ", parts);
        }

        public TimeSpan? ToTimeSpan()
        {
            if (!Years.HasValue && !Months.HasValue && !Days.HasValue && !Hours.HasValue && !Minutes.HasValue)
                return null;

            var totalDays = (Years ?? 0) * 365 + (Months ?? 0) * 30 + (Days ?? 0);
            var totalHours = (Hours ?? 0) + (Minutes ?? 0) / 60.0;

            return TimeSpan.FromDays(totalDays).Add(TimeSpan.FromHours(totalHours));
        }
    }
} 