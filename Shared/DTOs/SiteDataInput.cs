using Shared.DTOs;

namespace Shared.DTOs
{
    public class SiteDataInput
    {
        public ProjectDetailsInput? ProjectDetails { get; set; }
        public SiteSpecificationInput? SiteSpecification { get; set; }
        public RegulatoryComplianceInput? RegulatoryCompliance { get; set; }
        public SiteCommitteeInput? SiteCommittee { get; set; }
        public EmergencyContactsInput? EmergencyContacts { get; set; }
        public ProjectTimelineInput? ProjectTimeline { get; set; }

        public SiteDataDto ToDto()
        {
            return new SiteDataDto
            {
                ProjectDetails = ProjectDetails?.ToDto(),
                SiteSpecification = SiteSpecification?.ToDto(),
                RegulatoryCompliance = RegulatoryCompliance?.ToDto(),
                SiteCommittee = SiteCommittee?.ToDto(),
                EmergencyContacts = EmergencyContacts?.ToDto(),
                ProjectTimeline = ProjectTimeline?.ToDto()
            };
        }
    }

    public class ProjectDetailsInput
    {
        public string? Name { get; set; }
        public string? Cost { get; set; }
        public string? MainContractor { get; set; }
        public List<string>? Subcontractors { get; set; }
        public KeyPersonelInput? KeyPersonel { get; set; }
        public string? ContractType { get; set; }
        public List<string>? ProjectType { get; set; }

        public ProjectDetailsDto ToDto()
        {
            return new ProjectDetailsDto
            {
                Name = Name,
                Cost = Cost,
                MainContractor = MainContractor,
                Subcontractors = Subcontractors,
                KeyPersonel = KeyPersonel?.ToDto(),
                ContractType = ContractType,
                ProjectType = ProjectType
            };
        }
    }

    public class KeyPersonelInput
    {
        public string? Architect { get; set; }
        public string? Engineer { get; set; }

        public KeyPersonelDto ToDto()
        {
            return new KeyPersonelDto
            {
                Architect = Architect,
                Engineer = Engineer
            };
        }
    }

    public class SiteSpecificationInput
    {
        public SiteLocationInput? SiteLocation { get; set; }
        public BuildingStatsInput? BuildingStats { get; set; }
        public BuildingFootprintInput? BuildingFootprint { get; set; }
        public UtilitiesServicesInput? UtilitiesServices { get; set; }
        public AccessRoadsInput? AccessRoads { get; set; }

        public SiteSpecificationDto ToDto()
        {
            return new SiteSpecificationDto
            {
                SiteLocation = SiteLocation?.ToDto(),
                BuildingStats = BuildingStats?.ToDto(),
                BuildingFootprint = BuildingFootprint?.ToDto(),
                UtilitiesServices = UtilitiesServices?.ToDto(),
                AccessRoads = AccessRoads?.ToDto()
            };
        }
    }

    public class SiteLocationInput
    {
        public string? TotalArea { get; set; }
        public string? LocationMap { get; set; }

        public SiteLocationDto ToDto()
        {
            return new SiteLocationDto
            {
                TotalArea = TotalArea,
                LocationMap = LocationMap
            };
        }
    }

    public class BuildingStatsInput
    {
        public string? Floors { get; set; }
        public string? Basement { get; set; }
        public string? Parking { get; set; }

        public BuildingStatsDto ToDto()
        {
            return new BuildingStatsDto
            {
                Floors = Floors,
                Basement = Basement,
                Parking = Parking
            };
        }
    }

    public class BuildingFootprintInput
    {
        public string? BuildingArea { get; set; }
        public string? BuiltArea { get; set; }

        public BuildingFootprintDto ToDto()
        {
            return new BuildingFootprintDto
            {
                BuildingArea = BuildingArea,
                BuiltArea = BuiltArea
            };
        }
    }

    public class UtilitiesServicesInput
    {
        public string? Water { get; set; }
        public string? Electricity { get; set; }
        public string? Sewer { get; set; }
        public string? Internet { get; set; }

        public UtilitiesServicesDto ToDto()
        {
            return new UtilitiesServicesDto
            {
                Water = Water,
                Electricity = Electricity,
                Sewer = Sewer,
                Internet = Internet
            };
        }
    }

    public class AccessRoadsInput
    {
        public List<string>? MainAccessRoads { get; set; }
        public List<string>? SecondaryAccessRoads { get; set; }

        public AccessRoadsDto ToDto()
        {
            return new AccessRoadsDto
            {
                MainAccessRoads = MainAccessRoads,
                SecondaryAccessRoads = SecondaryAccessRoads
            };
        }
    }

    public class RegulatoryComplianceInput
    {
        public BuildingPermitInput? BuildingPermit { get; set; }
        public ClassificationInput? Classification { get; set; }
        public string? FireSafetyRating { get; set; }
        public ComplianceStandardInput? ComplianceStandard { get; set; }
        public string? OccupancyType { get; set; }

        public RegulatoryComplianceDto ToDto()
        {
            return new RegulatoryComplianceDto
            {
                BuildingPermit = BuildingPermit?.ToDto(),
                Classification = Classification?.ToDto(),
                FireSafetyRating = FireSafetyRating,
                ComplianceStandard = ComplianceStandard?.ToDto(),
                OccupancyType = OccupancyType
            };
        }
    }

    public class BuildingPermitInput
    {
        public string? PermitNumber { get; set; }
        public string? PermitSpecification { get; set; }
        public string? PermitType { get; set; }

        public BuildingPermitDto ToDto()
        {
            return new BuildingPermitDto
            {
                PermitNumber = PermitNumber,
                PermitSpecification = PermitSpecification,
                PermitType = PermitType
            };
        }
    }

    public class ClassificationInput
    {
        public string? BuildingClass { get; set; }
        public string? ConstructionType { get; set; }

        public ClassificationDto ToDto()
        {
            return new ClassificationDto
            {
                BuildingClass = BuildingClass,
                ConstructionType = ConstructionType
            };
        }
    }

    public class ComplianceStandardInput
    {
        public string? Accessibility { get; set; }
        public string? Environmental { get; set; }

        public ComplianceStandardDto ToDto()
        {
            return new ComplianceStandardDto
            {
                Accessibility = Accessibility,
                Environmental = Environmental
            };
        }
    }

    public class SiteCommitteeInput
    {
        public List<string>? CommitteeMembers { get; set; }

        public SiteCommitteeDto ToDto()
        {
            return new SiteCommitteeDto
            {
                CommitteeMembers = CommitteeMembers
            };
        }
    }

    public class EmergencyContactsInput
    {
        public List<string>? Police { get; set; }
        public List<string>? FireDepartment { get; set; }
        public List<string>? MedicalServices { get; set; }
        public List<string>? EmergencyManagement { get; set; }

        public EmergencyContactsDto ToDto()
        {
            return new EmergencyContactsDto
            {
                Police = Police,
                FireDepartment = FireDepartment,
                MedicalServices = MedicalServices,
                EmergencyManagement = EmergencyManagement
            };
        }
    }

    public class ProjectTimelineInput
    {
        public string? StartDate { get; set; }
        public string? EndDate { get; set; }
        public List<string>? Milestones { get; set; }

        public ProjectTimelineDto ToDto()
        {
            return new ProjectTimelineDto
            {
                StartDate = StartDate,
                EndDate = EndDate,
                Milestones = Milestones
            };
        }
    }
}
