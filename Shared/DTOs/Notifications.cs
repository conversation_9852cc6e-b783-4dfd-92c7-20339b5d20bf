using System.Collections.Generic;

namespace Shared.DTOs
{
    public enum NotificationChannel
    {
        InApp,
        Email,
        Sms
    }

    public enum NotificationPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum NotificationStatus
    {
        Pending,
        Sent,
        Failed,
        Read
    }

    public record NotificationEvent(
        string Type,
        string Title,
        string Message,
        string? Entity = null,
        string? Operation = null,
        Dictionary<string, string>? Metadata = null,
        NotificationPriority Priority = NotificationPriority.Medium,
        List<string>? Recipients = null,
        List<NotificationChannel>? Channels = null
    );

    public record NotificationRuleCondition(
        string Field,
        string? EqualTo = null,
        string? NotEqualTo = null
    );

    public record NotificationRule(
        string Entity,
        string Operation,
        string? Type,
        string TitleTemplate,
        string MessageTemplate,
        List<string>? Fields,
        NotificationRuleCondition? Condition,
        List<string>? Channels,
        List<string>? Recipients,
        NotificationPriority Priority = NotificationPriority.Medium
    );

    public record NotificationRecipient(
        int UserId,
        string Email,
        string? FirstName = null,
        string? LastName = null,
        List<NotificationChannel>? PreferredChannels = null
    );
}

