namespace Shared.Enums
{
    public enum CertificateType
    {
        TEMPORARY,
        MEDICAL,
        PERMANENT
    }

    public enum TrainingMode
    {
        ONLINE,
        ONSITE,
        HYBRID
    }

    public enum TrainingSessionStatus
    {
        DRAFT,
        SCHEDULED,
        IN_PROGRESS,
        COMPLETED,
        <PERSON>IN<PERSON><PERSON>ZED,
        ARCHIVED,
        <PERSON><PERSON><PERSON>LE<PERSON>,
        NO_SHOW,
        ABORTED
    }

    public enum EnrollmentStatus
    {
        REGISTERED,     // Worker is registered for the training
        ATTENDED,       // Worker attended the training
        DID_NOT_ATTEND, // Worker did not attend (no-show)
        WITHDRAWN       // Worker withdrew from the training
    }

    public enum TrainingOutcome
    {
        PENDING,
        PASS,
        FAIL,
        INCOMPLETE
    }

    public enum CertificateStatus
    {
        ISSUED,
        VALID,
        EXPIRING_SOON,
        EXPIRED,
        REVOKED
    }

    public enum WorkerEligibilityStatus
    {
        ELIGIBLE,
        ELIGIBLE_WITHIN_GRACE,
        NOT_ELIGIBLE
    }

    public enum PricingStrategy
    {
        Per<PERSON>erson,      // Charge per person trained
        PerHour,        // Charge per hour of training

        PerDay,         // Charge per day of training

        PerWeek,         // Charge per week of training
        FlatFee,        // Fixed fee regardless of attendees or duration
        Subscription    // Recurring subscription model
    }
}

