using System;

namespace Shared.Enums
{
    [Flags]
    public enum WorkersPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum SitesPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum TrainingsPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum DocsPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum PPEPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum RoleManagementPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    [Flags]
    public enum ReportsPermissions : byte
    {
        None = 0,
        SiteCreate = 1,
        SiteRead = 2,
        SiteUpdate = 4,
        SiteDelete = 8,
        CompanyCreate = 16,
        CompanyRead = 32,
        CompanyUpdate = 64,
        CompanyDelete = 128
    }

    public enum UserStatus
    {
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        PendingActivation = 4
    }

    public enum AuditAction
    {
        Login = 1,
        Logout = 2,
        FailedLogin = 3,
        PasswordChanged = 4,
        ProfileUpdated = 5,
        AccountLocked = 6,
        AccountUnlocked = 7,
        PermissionsChanged = 8,
        RoleChanged = 9,
        TwoFactorEnabled = 10,
        TwoFactorDisabled = 11,
        SessionCreated = 12,
        SessionEnded = 13
    }
}
