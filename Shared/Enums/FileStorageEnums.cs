namespace Shared.Enums
{
    /// <summary>
    /// Allowed file types for MinIO storage
    /// </summary>
    public enum AllowedFileType
    {
        // Image types
        JPEG = 1,
        JPG = 2,
        PNG = 3,
        GIF = 4,
        BMP = 5,
        WEBP = 6,

        // Document types
        PDF = 7,

        // Microsoft Word
        DOC = 8,
        DOCX = 9,

        // Microsoft Excel
        XLS = 10,
        XLSX = 11,

        // Additional common types
        CSV = 12,
        TXT = 13
    }

    /// <summary>
    /// Predefined bucket names for different file categories
    /// </summary>
    public enum BucketName
    {
        PROFILE_PICTURE = 1,
        CERTIFICATION = 2,
        SIGNATURES = 3,
        TEMP = 4,
        DOCS = 5,
        INSPECTIONS = 6
    }

    /// <summary>
    /// File storage operation results
    /// </summary>
    public enum FileStorageResult
    {
        SUCCESS = 1,
        FILE_TOO_LARGE = 2,
        INVALID_FILE_TYPE = 3,
        BUCKET_NOT_FOUND = 4,
        UPLOAD_FAILED = 5,
        DOWNLOAD_FAILED = 6,
        DELETE_FAILED = 7,
        FILE_NOT_FOUND = 8
    }
}
