using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using GraphQLApi.Extensions;
using GraphQLApi.Services;
using Xunit;

namespace GraphQLApi.Tests
{
    public class ServiceRegistrationTests
    {
        [Fact]
        public void ServiceRegistration_ShouldNotThrowServiceLifetimeExceptions()
        {
            // Arrange
            var builder = Host.CreateApplicationBuilder();
            
            // Add minimal configuration
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string>
                {
                    ["ConnectionStrings:DefaultConnection"] = "Server=localhost;Database=TestDb;Trusted_Connection=true;",
                    ["JwtSettings:SecretKey"] = "test-secret-key-that-is-long-enough-for-jwt",
                    ["JwtSettings:Issuer"] = "test-issuer",
                    ["JwtSettings:Audience"] = "test-audience",
                    ["JwtSettings:ExpirationMinutes"] = "60",
                    ["HikvisionApi:BaseUrl"] = "http://localhost:8080",
                    ["MinIO:Endpoint"] = "localhost:9000",
                    ["MinIO:AccessKey"] = "test",
                    ["MinIO:SecretKey"] = "test",
                    ["MinIO:UseSSL"] = "false"
                })
                .Build();
            
            builder.Configuration.AddConfiguration(configuration);
            
            // Act & Assert - This should not throw any service lifetime exceptions
            var exception = Record.Exception(() =>
            {
                builder.Services
                    .AddApplicationServices()
                    .AddAuthenticationServices()
                    .AddJwtServices(builder.Configuration)
                    .AddAuthorizationServices()
                    .AddHikvisionServices(builder.Configuration)
                    .AddMinIOServices(builder.Configuration)
                    .AddGraphQLServices();
                
                // Build the service provider to validate registrations
                var host = builder.Build();
                
                // Test that we can resolve key services without lifetime issues
                using var scope = host.Services.CreateScope();
                var notificationService = scope.ServiceProvider.GetService<INotificationService>();
                var eventBus = host.Services.GetService<NotificationEventBus>();
                
                Assert.NotNull(notificationService);
                Assert.NotNull(eventBus);
            });
            
            Assert.Null(exception);
        }
        
        [Fact]
        public void NotificationEventBus_ShouldBeRegisteredAsSingleton()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder().Build();
            
            // Act
            services.AddApplicationServices();
            
            // Assert
            var descriptor = services.FirstOrDefault(s => s.ServiceType == typeof(NotificationEventBus));
            Assert.NotNull(descriptor);
            Assert.Equal(ServiceLifetime.Singleton, descriptor.Lifetime);
        }
        
        [Fact]
        public void NotificationChannelHandlers_ShouldBeRegisteredAsScoped()
        {
            // Arrange
            var services = new ServiceCollection();
            
            // Act
            services.AddApplicationServices();
            
            // Assert
            var handlers = services.Where(s => s.ServiceType == typeof(INotificationChannelHandler)).ToList();
            Assert.NotEmpty(handlers);
            Assert.All(handlers, handler => Assert.Equal(ServiceLifetime.Scoped, handler.Lifetime));
        }
    }
}
